//用户学生接口

const express = require('express');
const router = express.Router();
const db = require('../config/db');
const multer = require('multer');
const path = require('path');

// 配置 multer 存储
const storage = multer.diskStorage({
    destination: function (req, file, cb) {
        cb(null, path.join(__dirname, '../../public/uploads/'));
    },
    filename: function (req, file, cb) {
        // 生成唯一的文件名
        const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
        cb(null, uniqueSuffix + path.extname(file.originalname));
    }
});

// 文件过滤器
const fileFilter = (req, file, cb) => {
    if (file.mimetype.startsWith('image/')) {
        cb(null, true);
    } else {
        cb(new Error('只允许上传图片文件！'), false);
    }
};

const upload = multer({ 
    storage: storage,
    fileFilter: fileFilter,
    limits: {
        fileSize: 5 * 1024 * 1024 // 限制文件大小为5MB
    }
});

// 存储验证码的对象，格式为 { 手机号: { code: 验证码, expireTime: 过期时间 } }
const verificationCodes = {};

// 生成随机验证码
function generateVerificationCode() {
    return Math.floor(100000 + Math.random() * 900000).toString();
}

router.post('/',(req,res)=>{
    const sql='select * from user'
    db.query(sql,(err,result)=>{
        if(err){
            res.send({
                code:201,
                success:"失败"
            })
        }else{
            res.send({
                code:200,
                success:"成功",
                result:result
            })
        }
    })
})

router.post('/login', (req, res) => {
    // 登录逻辑
    const { username, password } = req.body;
    const sql = `select * from user where username='${username}' and password='${password}'`;
    db.query(sql, (err, result) => {
        if (err) {
            res.send({
                code: 201,
                success: "失败"
            });
        } else if (result.length === 0) {
            res.send({
                code: 201,
                success: "失败"
            });
        } else {
            res.send({
                code: 200,
                success: "成功",
                result: result[0]
            });
        }
    });
});

// 发送验证码接口
router.post('/sendCode', (req, res) => {
    const { phone } = req.body;
    
    // 验证手机号格式
    if (!phone || !/^1[3-9]\d{9}$/.test(phone)) {
        return res.status(400).json({
            code: 201,
            success: "失败",
            message: "请提供有效的手机号"
        });
    }
    
    // 生成6位随机验证码
    const code = generateVerificationCode();
    
    // 设置验证码有效期为5分钟
    const expireTime = Date.now() + 5 * 60 * 1000;
    
    // 存储验证码和过期时间
    verificationCodes[phone] = {
        code,
        expireTime
    };
    
    // 在实际应用中，这里应该调用短信服务发送验证码
    // 这里只是模拟发送成功
    console.log(`向手机号 ${phone} 发送验证码: ${code}`);
    
    res.status(200).json({
        code: 200,
        success: "成功",
        message: "验证码已发送",
        // 在开发环境下返回验证码，方便测试
        verificationCode: code
    });
});

// 验证码登录接口
router.post('/loginByCode', (req, res) => {
    const { phone, code } = req.body;
    
    // 验证手机号和验证码
    if (!phone || !code) {
        return res.status(400).json({
            code: 201,
            success: "失败",
            message: "手机号和验证码不能为空"
        });
    }
    
    // 检查验证码是否存在且有效
    const storedVerification = verificationCodes[phone];
    if (!storedVerification || storedVerification.code !== code || Date.now() > storedVerification.expireTime) {
        return res.status(400).json({
            code: 201,
            success: "失败",
            message: "验证码错误或已过期"
        });
    }
    
    // 验证通过，查询用户信息
    const sql = `SELECT * FROM user WHERE phone = ?`;
    db.query(sql, [phone], (err, result) => {
        if (err) {
            return res.status(500).json({
                code: 201,
                success: "失败",
                message: "服务器错误"
            });
        }
        
        // 如果用户存在，返回用户信息
        if (result.length > 0) {
            // 清除已使用的验证码
            delete verificationCodes[phone];
            
            return res.status(200).json({
                code: 200,
                success: "成功",
                message: "登录成功",
                result: result[0]
            });
        }
        
        // 如果用户不存在，可以选择自动注册或返回错误
        return res.status(404).json({
            code: 201,
            success: "失败",
            message: "该手机号未注册"
        });
    });
});

// 忘记密码接口
router.post('/forgetPassword', (req, res) => {
    
    const { phone, code, newPassword, newpassword } = req.body;
    
    // 使用newPassword或newpassword
    const finalNewPassword = newPassword || newpassword;
    
    
    // 验证参数
    if (!phone || !code || !finalNewPassword) {
        console.log('参数验证失败:', { phone, code, finalNewPassword });
        return res.status(400).json({
            code: 201,
            success: "失败",
            message: `手机号、验证码和新密码不能为空 (${phone ? '手机号✓' : '手机号✗'}, ${code ? '验证码✓' : '验证码✗'}, ${finalNewPassword ? '新密码✓' : '新密码✗'})`
        });
    }
    
    // 检查验证码是否存在且有效
    const storedVerification = verificationCodes[phone];
    

    
    // 再次检查验证码
    const verification = verificationCodes[phone];
    if (!verification) {
        console.log('验证码不存在:', phone);
        return res.status(400).json({
            code: 201,
            success: "失败",
            message: "验证码不存在，请重新获取验证码"
        });
    }
    
    if (verification.code !== code) {
        console.log('验证码不匹配:', { expected: verification.code, received: code });
        return res.status(400).json({
            code: 201,
            success: "失败",
            message: "验证码错误"
        });
    }
    
    if (Date.now() > verification.expireTime) {
        console.log('验证码已过期');
        return res.status(400).json({
            code: 201,
            success: "失败",
            message: "验证码已过期，请重新获取"
        });
    }
    
    // 更新密码
    const sql = `UPDATE user SET password = ? WHERE phone = ?`;
    db.query(sql, [finalNewPassword, phone], (err, result) => {
        if (err) {
            console.error('数据库错误:', err);
            return res.status(500).json({
                code: 201,
                success: "失败",
                message: "服务器错误"
            });
        }
        
    
        
        if (result.affectedRows === 0) {
            return res.status(404).json({
                code: 201,
                success: "失败",
                message: "该手机号未注册"
            });
        }
        
        // 清除已使用的验证码
        delete verificationCodes[phone];
        
        res.status(200).json({
            code: 200,
            success: "成功",
            message: "密码重置成功"
        });
    });
});


//注册接口
router.post('/register', upload.single('user_image'), (req, res) => {
    const { username, password, nick_name, sex, birth, phone, identity, user_form, chinese, math, English, arts } = req.body;
    
    // 检查用户名是否已存在
    const checkUserSql = 'SELECT * FROM user WHERE username = ?';
    db.query(checkUserSql, [username], (err, result) => {
        if (err) {
            return res.status(500).json({
                code: 201,
                success: "失败",
                message: "服务器错误"
            });
        }
        
        if (result.length > 0) {
            return res.status(400).json({
                code: 201,
                success: "失败",
                message: "用户名已存在"
            });
        }

        // 验证手机号和验证码
        const { phone, code } = req.body;
        
        if (!phone || !code) {
            return res.status(400).json({
                code: 201,
                success: "失败",
                message: "手机号和验证码不能为空"
            });
        }
        
        // 检查验证码是否存在且有效
        const storedVerification = verificationCodes[phone];
        if (!storedVerification) {
            return res.status(400).json({
                code: 201,
                success: "失败",
                message: "验证码不存在，请先获取验证码"
            });
        }
        
        if (storedVerification.code !== code) {
            return res.status(400).json({
                code: 201,
                success: "失败",
                message: "验证码错误"
            });
        }
        
        if (Date.now() > storedVerification.expireTime) {
            return res.status(400).json({
                code: 201,
                success: "失败",
                message: "验证码已过期，请重新获取"
            });
        }

        // 获取上传的图片路径
        const user_image = req.file ? `/uploads/${req.file.filename}` : null;

        // 插入新用户
        const insertSql = `
            INSERT INTO user (username, password, nick_name, sex, birth, phone, identity, user_image, user_form, chinese, math, English, arts) 
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        `;
        
        const values = [
            username, 
            password, 
            nick_name, 
            sex, 
            birth, 
            phone, 
            identity, 
            user_image, 
            user_form,
            chinese || null,
            math || null,
            English || null,
            arts || null
        ];
        
        db.query(insertSql, values, (err, result) => {
            if (err) {
                return res.status(500).json({
                    code: 201,
                    success: "失败",
                    message: "注册失败",
                    error: err.message
                });
            }
            
            res.status(200).json({
                code: 200,
                success: "成功",
                message: "注册成功",
                data: {
                    id: result.insertId,
                    username,
                    nick_name,
                    user_image,
                    user_form,
                    chinese,
                    math,
                    English,
                    arts
                }
            });
            
            // 清除已使用的验证码
            delete verificationCodes[phone];
        });
    });
});


//注销学生信息
router.post('/delete',(req,res)=>{
    const sql='delete from user where user_id=?'
    db.query(sql,[req.body.user_id],(err,result)=>{
        if(err){
            res.send({
                code:201,
                message:'注销失败'
            })
        }else{
            res.send({
                code:200,
                message:'注销成功'
            })
        }
    })
})


//修改学生信息
router.post('/updateUser', upload.single('user_image'), (req, res) => {
    const { user_id, username, password, nick_name, sex, birth, phone, identity, user_form, chinese, math, English, arts } = req.body;
    
    // console.log('收到的更新请求数据:', req.body);
    
    // 验证必要参数
    if (!user_id) {
        return res.send({
            code: 201,
            message: '用户ID不能为空'
        });
    }
    
    // 确保user_id是数字
    const userId = parseInt(user_id);
    
    // 构建更新字段
    const updateFields = [];
    const values = [];
    
    // 只更新提供的字段
    if (username) {
        updateFields.push('username = ?');
        values.push(username);
    }
    
    if (password) {
        updateFields.push('password = ?');
        values.push(password);
    }
    
    if (nick_name) {
        updateFields.push('nick_name = ?');
        values.push(nick_name);
    }
    
    if (sex) {
        updateFields.push('sex = ?');
        values.push(sex);
    }
    
    // 处理日期格式
    if (birth) {
        let formattedBirth = birth;
        // 如果是ISO格式的日期时间，转换为YYYY-MM-DD格式
        if (birth.includes('T')) {
            try {
                const birthDate = new Date(birth);
                formattedBirth = birthDate.toISOString().split('T')[0];
            } catch (e) {
                console.error('日期格式转换失败:', e);
            }
        }
        updateFields.push('birth = ?');
        values.push(formattedBirth);
    }
    
    if (phone) {
        updateFields.push('phone = ?');
        values.push(phone);
    }
    
    if (identity) {
        updateFields.push('identity = ?');
        values.push(identity);
    }
    
    if (user_form) {
        updateFields.push('user_form = ?');
        values.push(user_form);
    }
    
    // 添加user_school字段
    if (req.body.user_school) {
        updateFields.push('user_school = ?');
        values.push(req.body.user_school);
    }
    
    // 添加新字段的更新
    if (chinese !== undefined) {
        updateFields.push('chinese = ?');
        values.push(chinese);
    }
    
    if (math !== undefined) {
        updateFields.push('math = ?');
        values.push(math);
    }
    
    if (English !== undefined) {
        updateFields.push('English = ?');
        values.push(English);
    }
    
    if (arts !== undefined) {
        updateFields.push('arts = ?');
        values.push(arts);
    }
    
    // 处理图片上传
    if (req.file) {
        const user_image = `/uploads/${req.file.filename}`;
        updateFields.push('user_image = ?');
        values.push(user_image);
    }
    
    // 如果没有需要更新的字段
    if (updateFields.length === 0) {
        return res.send({
            code: 201,
            message: '没有提供需要更新的字段'
        });
    }
    
    // 添加user_id到values数组
    values.push(userId);
    
    // 构建SQL语句
    const sql = `UPDATE user SET ${updateFields.join(', ')} WHERE user_id = ?`;
    
    // console.log('执行的SQL:', sql);
    // console.log('SQL参数:', values);
    
    // 执行更新操作
    db.query(sql, values, (err, result) => {
        if (err) {
            console.error('SQL执行错误:', err);
            return res.send({
                code: 201,
                message: '更新失败: ' + err.message
            });
        }
        
        if (result.affectedRows === 0) {
            return res.send({
                code: 201,
                message: '未找到该用户'
            });
        }
        
        res.send({
            code: 200,
            message: '用户信息更新成功'
        });
    });
});

// 上传头像接口
router.post('/uploadAvatar', upload.single('avatar'), (req, res) => {
    const { user_id } = req.body;
    
    // 验证必要参数
    if (!user_id) {
        return res.json({
            code: 201,
            message: '用户ID不能为空'
        });
    }
    
    // 验证文件是否上传成功
    if (!req.file) {
        return res.json({
            code: 201,
            message: '未接收到文件'
        });
    }
    
    // 构建图片URL
    const imageUrl = `/uploads/${req.file.filename}`;
    
    // 更新用户头像
    const sql = 'UPDATE user SET user_image = ? WHERE user_id = ?';
    db.query(sql, [imageUrl, user_id], (err, result) => {
        if (err) {
            return res.json({
                code: 201,
                message: '更新头像失败'
            });
        }
        
        if (result.affectedRows === 0) {
            return res.json({
                code: 201,
                message: '未找到该用户'
            });
        }
        
        // 返回成功响应
        res.json({
            code: 200,
            message: '头像上传成功',
            result: {
                imageUrl: imageUrl
            }
        });
    });
});

// 用户成绩分析接口
router.post('/analyzeScore', (req, res) => {
    const { user_id } = req.body;
    
    // 参数验证
    if (!user_id) {
        return res.status(400).send({
            code: 201,
            success: "失败",
            message: "用户ID不能为空"
        });
    }
    
    // 查询用户成绩信息
    const sql = `SELECT user_id, username, nick_name, chinese, math, english, arts, user_fraction FROM user WHERE user_id = ?`;
    
    db.query(sql, [user_id], (err, result) => {
        if (err) {
            return res.status(500).send({
                code: 201,
                success: "失败",
                message: "查询失败",
                error: err.message
            });
        }
        
        if (result.length === 0) {
            return res.status(404).send({
                code: 201,
                success: "失败",
                message: "未找到该用户"
            });
        }
        
        const user = result[0];
        const { chinese, math, english, arts, user_fraction } = user;
        
        // 检查成绩是否完整
        if (!chinese || !math || !english || !arts) {
            return res.status(400).send({
                code: 201,
                success: "失败",
                message: "用户成绩信息不完整"
            });
        }
        
        // 计算总分
        const totalScore = parseInt(chinese) + parseInt(math) + parseInt(english) + parseInt(arts);
        
        // 各科成绩分析
        const subjects = [
            {
                name: '语文',
                score: parseInt(chinese),
                fullScore: 150,
                analysis: getSubjectAnalysis('chinese', parseInt(chinese))
            },
            {
                name: '数学',
                score: parseInt(math),
                fullScore: 150,
                analysis: getSubjectAnalysis('math', parseInt(math))
            },
            {
                name: '英语',
                score: parseInt(english),
                fullScore: 150,
                analysis: getSubjectAnalysis('english', parseInt(english))
            },
            {
                name: '文综',
                score: parseInt(arts),
                fullScore: 300,
                analysis: getSubjectAnalysis('arts', parseInt(arts))
            }
        ];
        
        // 优势和劣势科目分析
        const strengthSubjects = [];
        const weaknessSubjects = [];
        
        subjects.forEach(subject => {
            const percentage = (subject.score / subject.fullScore) * 100;
            if (percentage >= 80) {
                strengthSubjects.push(subject.name);
            } else if (percentage < 60) {
                weaknessSubjects.push(subject.name);
            }
        });
        
        // 模拟省内排名和超越百分比
        const provinceRank = Math.floor(Math.random() * 50000) + 1000;
        const beatPercentage = Math.round((1 - provinceRank / 100000) * 100 * 10) / 10;
        
        // 提升建议
        const improvements = generateImprovements(subjects);
        
        // 生成专业推荐（使用回调函数处理异步查询）
        generateRecommendations(subjects, totalScore, (recommendations) => {
            res.send({
                code: 200,
                success: "成功",
                message: "成绩分析完成",
                result: {
                    userInfo: {
                        user_id: user.user_id,
                        username: user.username,
                        nick_name: user.nick_name
                    },
                    scoreAnalysis: {
                        totalScore: totalScore,
                        provinceRank: provinceRank,
                        beatPercentage: beatPercentage,
                        subjects: subjects,
                        strengthSubjects: strengthSubjects,
                        weaknessSubjects: weaknessSubjects,
                        recommendations: recommendations,
                        improvements: improvements
                    }
                }
            });
        });
    });
});

// 获取科目分析文本
function getSubjectAnalysis(subject, score) {
    const analyses = {
        chinese: {
            excellent: '语文基础扎实，阅读理解和写作能力强',
            good: '语文水平良好，建议加强古诗文理解',
            average: '语文成绩一般，需要提升阅读和写作能力',
            poor: '语文基础薄弱，建议加强基础知识学习'
        },
        math: {
            excellent: '数学逻辑思维优秀，计算能力强',
            good: '数学基础扎实，建议多练习难题',
            average: '数学成绩中等，需要加强解题技巧',
            poor: '数学基础需要加强，建议从基础概念开始'
        },
        english: {
            excellent: '英语水平优秀，词汇量丰富',
            good: '英语基础良好，建议提升听说能力',
            average: '英语成绩一般，需要扩大词汇量',
            poor: '英语基础薄弱，建议加强语法学习'
        },
        arts: {
            excellent: '文综知识面广，理解能力强',
            good: '文综基础扎实，建议加强时事关注',
            average: '文综成绩中等，需要加强记忆和理解',
            poor: '文综基础薄弱，建议系统复习基础知识'
        }
    };
    
    const fullScore = subject === 'arts' ? 300 : 150;
    const percentage = (score / fullScore) * 100;
    
    if (percentage >= 85) return analyses[subject].excellent;
    if (percentage >= 70) return analyses[subject].good;
    if (percentage >= 60) return analyses[subject].average;
    return analyses[subject].poor;
}

// 生成专业推荐
function generateRecommendations(subjects, totalScore, callback) {
    // 查询所有专业
    const sql = 'SELECT specialized_id, specialized_content FROM specialized ORDER BY RAND() LIMIT 10';
    
    db.query(sql, (err, specializedResult) => {
        if (err) {
            console.error('查询专业失败:', err);
            return callback([]);
        }
        
        const recommendations = [];
        const mathScore = subjects.find(s => s.name === '数学').score;
        const englishScore = subjects.find(s => s.name === '英语').score;
        const chineseScore = subjects.find(s => s.name === '语文').score;
        const artsScore = subjects.find(s => s.name === '文综').score;
        
        // 随机选择3-5个专业进行推荐
        const selectedSpecialized = specializedResult.slice(0, Math.min(5, specializedResult.length));
        
        selectedSpecialized.forEach((specialized, index) => {
            let matchRate = 60; // 基础匹配度
            let reason = '根据您的综合成绩推荐';
            let tags = ['适合', '发展前景好'];
            
            // 根据成绩调整匹配度和推荐理由
            if (mathScore >= 130) {
                matchRate += 15;
                reason = '数学成绩优秀，逻辑思维能力强';
                tags.push('理科优势');
            } else if (mathScore >= 100) {
                matchRate += 8;
                reason = '数学基础扎实，适合理性思维专业';
            }
            
            if (englishScore >= 120) {
                matchRate += 10;
                if (reason.includes('数学')) {
                    reason += '，英语水平优秀';
                } else {
                    reason = '英语水平优秀，适合国际化专业';
                }
                tags.push('国际化');
            }
            
            if (chineseScore >= 120) {
                matchRate += 8;
                if (!reason.includes('，')) {
                    reason = '语文基础扎实，文字表达能力强';
                }
                tags.push('文科优势');
            }
            
            if (artsScore >= 200) {
                matchRate += 12;
                if (!reason.includes('文综')) {
                    reason += '，文综成绩优秀';
                }
                tags.push('综合素质高');
            }
            
            // 确保匹配度在合理范围内
            matchRate = Math.min(95, Math.max(60, matchRate + Math.floor(Math.random() * 10) - 5));
            
            // 随机添加一些通用标签
            const additionalTags = ['就业前景好', '社会需求大', '发展空间广', '薪资待遇优', '技能实用'];
            const randomTag = additionalTags[Math.floor(Math.random() * additionalTags.length)];
            if (!tags.includes(randomTag)) {
                tags.push(randomTag);
            }
            
            recommendations.push({
                major: specialized.specialized_content,
                matchRate: matchRate,
                reason: reason,
                tags: tags.slice(0, 3) // 最多显示3个标签
            });
        });
        
        // 按匹配度排序
        recommendations.sort((a, b) => b.matchRate - a.matchRate);
        
        callback(recommendations.slice(0, 3)); // 返回前3个推荐
    });
}

// 生成提升建议
function generateImprovements(subjects) {
    const improvements = [];
    
    subjects.forEach(subject => {
        const percentage = (subject.score / subject.fullScore) * 100;
        
        if (percentage < 70) {
            let suggestion = {};
            switch(subject.name) {
                case '语文':
                    suggestion = {
                        icon: 'book',
                        color: '#007aff',
                        title: '加强语文学习',
                        description: '多读优秀文章，提高阅读理解和写作能力'
                    };
                    break;
                case '数学':
                    suggestion = {
                        icon: 'calculator',
                        color: '#34c759',
                        title: '提升数学能力',
                        description: '加强基础概念理解，多练习解题技巧'
                    };
                    break;
                case '英语':
                    suggestion = {
                        icon: 'chatbubble',
                        color: '#ff9500',
                        title: '提高英语水平',
                        description: '扩大词汇量，加强语法和听说训练'
                    };
                    break;
                case '文综':
                    suggestion = {
                        icon: 'compose',
                        color: '#5856d6',
                        title: '强化文综复习',
                        description: '系统梳理知识点，加强记忆和理解能力'
                    };
                    break;
            }
            improvements.push(suggestion);
        }
    });
    
    return improvements;
}

module.exports = router;

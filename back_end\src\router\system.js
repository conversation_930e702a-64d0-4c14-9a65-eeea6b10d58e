//系统介绍 接口

const express = require('express');
const router = express.Router();
const db = require('../config/db');
const multer = require('multer');
const path = require('path');

// 配置文件上传
const storage = multer.diskStorage({
    destination: function (req, file, cb) {
        cb(null, 'public/uploads/');
    },
    filename: function (req, file, cb) {
        const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
        cb(null, uniqueSuffix + path.extname(file.originalname));
    }
});

const upload = multer({ 
    storage: storage,
    fileFilter: function (req, file, cb) {
        // 只允许上传图片
        if (!file.originalname.match(/\.(jpg|jpeg|png|gif)$/)) {
            return cb(new Error('只允许上传图片文件！'), false);
        }
        cb(null, true);
    }
});

// 获取所有系统介绍数据
router.post('/', (req, res) => {
    const sql = 'SELECT * FROM `system`';
    
    db.query(sql, [], (err, results) => {
        if (err) {
            console.error('数据库查询错误:', err);
            return res.json({
                code: 500,
                message: '服务器错误: ' + err.message,
                data: null
            });
        }
        
        res.json({
            code: 200,
            message: '获取成功',
            data: results
        });
    });
});

// 修改系统信息
router.post('/update', upload.single('system_logo'), (req, res) => {
    const { system_id, system_name, system_message, system_technology } = req.body;
    
    // 构建更新数据对象
    const updateData = {
        system_name,
        system_message,
        system_technology
    };

    // 如果上传了新图片，添加图片路径
    if (req.file) {
        updateData.system_logo = '/uploads/' + req.file.filename;
    }

    // 构建SQL语句
    const sql = 'UPDATE `system` SET ? WHERE system_id = ?';
    
    db.query(sql, [updateData, system_id], (err, results) => {
        if (err) {
            console.error('数据库更新错误:', err);
            return res.json({
                code: 500,
                message: '服务器错误: ' + err.message,
                data: null
            });
        }

        if (results.affectedRows === 0) {
            return res.json({
                code: 400,
                message: '未找到要修改的记录',
                data: null
            });
        }
        
        res.json({
            code: 200,
            message: '修改成功',
            data: {
                affected_rows: results.affectedRows,
                ...updateData
            }
        });
    });
});

module.exports = router;


<template>
  <div class="profile-container" v-if="adminInfo">
    <el-card class="profile-card">
      <template #header>
        <div class="card-header">
          <span>个人信息</span>
          <el-button type="primary" @click="handleEdit" v-if="!isEditing">编辑</el-button>
          <div v-else>
            <el-button type="success" @click="handleSave">保存</el-button>
            <el-button @click="cancelEdit">取消</el-button>
          </div>
        </div>
      </template>
      
      <el-form :model="adminInfo" label-width="120px" :disabled="!isEditing">
        <el-form-item label="头像">
          <el-upload
            class="avatar-uploader"
            :show-file-list="false"
            :on-change="handleAvatarChange"
            :before-upload="beforeAvatarUpload"
            :disabled="!isEditing"
            :auto-upload="false"
            ref="uploadRef"
            v-if="isEditing"
          >
            <img v-if="imageUrl" :src="imageUrl" class="avatar" />
            <el-icon v-else class="avatar-uploader-icon"><Plus /></el-icon>
          </el-upload>
          <img v-else-if="imageUrl" :src="imageUrl" class="avatar" />
        </el-form-item>

        <el-form-item label="用户名">
          <el-input v-model="adminInfo.administrator_name" />
        </el-form-item>

        <el-form-item label="真实姓名">
          <el-input v-model="adminInfo.administrator_realname" />
        </el-form-item>

        <el-form-item label="手机号">
          <el-input v-model="adminInfo.administrator_phone" />
        </el-form-item>

        <el-form-item label="邮箱">
          <el-input v-model="adminInfo.administrator_email" />
        </el-form-item>

        <el-form-item label="密码" v-if="isEditing">
          <el-input v-model="adminInfo.administrator_password" type="password" placeholder="不修改请留空" />
        </el-form-item>
      </el-form>
    </el-card>
  </div>
</template>

<script>
import { ref, onMounted, computed } from 'vue'
import { post, baseURL } from '@/utils/request' // 导入 post 和 baseURL
import { ElMessage, ElLoading } from 'element-plus'
import { Plus } from '@element-plus/icons-vue'
import { useRouter } from 'vue-router'

export default {
  name: 'Profile',
  components: {
    Plus
  },
  setup() {
    const router = useRouter()
    const isEditing = ref(false)
    const adminInfo = ref({
      administrator_id: '',
      administrator_name: '',
      administrator_password: '',
      administrator_phone: '',
      administrator_realname: '',
      administrator_email: '',
      administrator_image: ''
    })
    const originalInfo = ref({})
    const uploadRef = ref(null)
    const imageUrl = ref('')
    const selectedFile = ref(null)

    // 获取上传需要的其他数据
    const getUploadData = computed(() => ({
      administrator_id: adminInfo.value.administrator_id,
      administrator_name: adminInfo.value.administrator_name,
      administrator_password: adminInfo.value.administrator_password || originalInfo.value.administrator_password,
      administrator_phone: adminInfo.value.administrator_phone,
      administrator_realname: adminInfo.value.administrator_realname,
      administrator_email: adminInfo.value.administrator_email
    }))

    // 获取请求头
    const getHeaders = computed(() => ({
      Authorization: `Bearer ${localStorage.getItem('token')}`
    }))

    // 获取管理员信息
    const getAdminInfo = async () => {
      try {
        const storedInfo = JSON.parse(localStorage.getItem('adminInfo') || '{}')
        if (storedInfo) {
          // 直接使用存储的管理员信息
          adminInfo.value = {
            ...adminInfo.value,
            ...storedInfo
          }
          originalInfo.value = { ...adminInfo.value }

          // 如果需要刷新数据，使用登录接口重新获取
          const res = await post('/administrator/login', {
            administrator_name: storedInfo.administrator_name,
            administrator_password: storedInfo.administrator_password
          })
          
          if (res.code === 200 && res.result) {
            adminInfo.value = {
              ...adminInfo.value,
              ...res.result
            }
            originalInfo.value = { ...adminInfo.value }
            // 更新本地存储的信息
            localStorage.setItem('adminInfo', JSON.stringify(res.result))
          }
        } else {
          ElMessage.error('请重新登录')
          router.push('/login')
        }
      } catch (error) {
        console.error('获取个人信息失败:', error)
        ElMessage.error('获取个人信息失败')
      }
    }

    // 处理编辑按钮
    const handleEdit = () => {
      isEditing.value = true
      originalInfo.value = { ...adminInfo.value }
    }

    // 处理取消编辑
    const cancelEdit = () => {
      isEditing.value = false
      adminInfo.value = { ...originalInfo.value }
    }

    // 处理保存
    const handleSave = async () => {
      try {
        if (selectedFile.value) {
          // 检查文件大小
          const fileSizeMB = selectedFile.value.size / (1024 * 1024);
          if (fileSizeMB > 5) {
            ElMessage.warning('文件大小超过5MB，请选择较小的图片');
            return;
          }
          
          // 创建FormData对象
          const formData = new FormData()
          
          // 添加所有管理员信息字段
          Object.keys(adminInfo.value).forEach(key => {
            if (key !== 'administrator_image') { // 跳过图片字段
              formData.append(key, adminInfo.value[key] || '')
            }
          })
          
          // 添加图片文件
          formData.append('administrator_image', selectedFile.value)
          
          // 显示加载提示
          const loading = ElLoading.service({
            lock: true,
            text: '正在上传图片，请稍候...',
            background: 'rgba(0, 0, 0, 0.7)'
          });
          
          try {
            // 发送请求，增加超时时间
            const res = await post('/administrator/update', formData, {
              headers: {
                'Content-Type': 'multipart/form-data',
                'Authorization': `Bearer ${localStorage.getItem('token')}`
              },
              timeout: 30000 // 增加超时时间到30秒
            })
            
            loading.close();
            
            if (res.code === 200) {
              ElMessage.success('更新成功')
              isEditing.value = false
              selectedFile.value = null // 重置文件
              
              // 更新本地存储的管理员信息
              localStorage.setItem('adminInfo', JSON.stringify(res.result))
              
              // 更新当前页面的管理员信息
              await getAdminInfo()
              
              // 强制刷新布局组件以更新头像
              window.dispatchEvent(new Event('storage'))
            }
          } catch (error) {
            loading.close();
            throw error;
          }
        } else {
          // 没有新图片，正常更新其他信息
          const updateData = { ...adminInfo.value }
          if (!updateData.administrator_password) {
            delete updateData.administrator_password // 不修改密码时删除此字段
          }
          
          const res = await post('/administrator/update', updateData)
          if (res.code === 200) {
            ElMessage.success('更新成功')
            isEditing.value = false
            
            // 更新本地存储的管理员信息
            localStorage.setItem('adminInfo', JSON.stringify(res.result))
            
            // 更新当前页面的管理员信息
            await getAdminInfo()
            
            // 强制刷新布局组件以更新头像
            window.dispatchEvent(new Event('storage'))
          }
        }
      } catch (error) {
        console.error('更新失败:', error)
        if (error.code === 'ECONNABORTED') {
          ElMessage.error('请求超时，请检查网络连接或稍后重试')
        } else {
          ElMessage.error('更新失败: ' + (error.message || '未知错误'))
        }
      }
    }

    // 处理头像上传成功
    const handleAvatarSuccess = (res) => {
      if (res.code === 200 && res.result) {
        adminInfo.value = {
          ...adminInfo.value,
          ...res.result
        }
        // 更新本地存储的信息
        localStorage.setItem('adminInfo', JSON.stringify(res.result))
        ElMessage.success('头像上传成功')
      } else {
        ElMessage.error(res.message || '头像上传失败')
      }
    }

    // 上传前验证
    const beforeAvatarUpload = (file) => {
      const isImage = file.type.startsWith('image/')
      const isLt5M = file.size / 1024 / 1024 < 5

      if (!isImage) {
        ElMessage.error('上传头像图片只能是图片格式!')
        return false
      }
      if (!isLt5M) {
        ElMessage.error('上传头像图片大小不能超过 5MB!')
        return false
      }
      return true
    }

    // 图片变更处理
    const handleAvatarChange = (file) => {
      if (file && file.raw) {
        // 预览图片
        imageUrl.value = URL.createObjectURL(file.raw)
        // 存储文件以便后续上传
        selectedFile.value = file.raw
      }
    }

    // 获取图片URL
    const getImageUrl = (path) => {
      if (!path) return ''
      if (path.startsWith('http')) {
        return path
      }
      // 使用导入的基础URL
      return `${baseURL}${path}`
    }

    onMounted(() => {
      getAdminInfo()
      // 初始化图片URL
      if (adminInfo.value && adminInfo.value.administrator_image) {
        imageUrl.value = getImageUrl(adminInfo.value.administrator_image)
      }
    })

    return {
      isEditing,
      adminInfo,
      handleEdit,
      cancelEdit,
      handleSave,
      handleAvatarSuccess,
      beforeAvatarUpload,
      getImageUrl,
      getUploadData,
      getHeaders,
      uploadRef,
      imageUrl,
      handleAvatarChange,
      selectedFile
    }
  }
}
</script>

<style scoped>
.profile-container {
  padding: 20px;
}

.profile-card {
  max-width: 800px;
  margin: 0 auto;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.avatar-uploader {
  text-align: center;
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  width: 178px;
  height: 178px;
}

.avatar-uploader:hover {
  border-color: #409EFF;
}

.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 178px;
  height: 178px;
  text-align: center;
  line-height: 178px;
}

.avatar {
  width: 178px;
  height: 178px;
  display: block;
}
</style>

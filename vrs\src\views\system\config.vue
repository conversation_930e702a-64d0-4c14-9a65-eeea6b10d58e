<template>
  <div class="config-container">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <span>基础配置</span>
        </div>
      </template>

      <el-tabs v-model="activeTab">
        <!-- 系统信息 -->
        <el-tab-pane label="系统信息" name="system">
          <el-form :model="systemForm" label-width="120px">
            <el-form-item label="系统名称">
              <el-input v-model="systemForm.name" placeholder="请输入系统名称" />
            </el-form-item>
            <el-form-item label="系统Logo">
              <el-upload
                class="logo-uploader"
                action="/api/upload"
                :show-file-list="false"
                :on-success="handleLogoSuccess"
                :before-upload="beforeLogoUpload">
                <img v-if="systemForm.logo" :src="systemForm.logo" class="logo" />
                <el-icon v-else class="logo-uploader-icon"><Plus /></el-icon>
              </el-upload>
            </el-form-item>
            <el-form-item label="系统描述">
              <el-input
                v-model="systemForm.description"
                type="textarea"
                rows="4"
                placeholder="请输入系统描述"
              />
            </el-form-item>
            <el-form-item label="版权信息">
              <el-input v-model="systemForm.copyright" placeholder="请输入版权信息" />
            </el-form-item>
            <el-form-item label="技术支持">
              <el-input v-model="systemForm.support" placeholder="请输入技术支持信息" />
            </el-form-item>
          </el-form>
        </el-tab-pane>

        <!-- 注册设置 -->
        <el-tab-pane label="注册设置" name="register">
          <el-form :model="registerForm" label-width="120px">
            <el-form-item label="开放注册">
              <el-switch v-model="registerForm.enableRegister" />
            </el-form-item>
            <el-form-item label="默认角色">
              <el-select v-model="registerForm.defaultRole" placeholder="请选择默认角色">
                <el-option label="普通用户" value="user" />
                <el-option label="VIP用户" value="vip" />
              </el-select>
            </el-form-item>
            <el-form-item label="注册验证">
              <el-checkbox-group v-model="registerForm.verifyMethods">
                <el-checkbox label="email">邮箱验证</el-checkbox>
                <el-checkbox label="phone">手机验证</el-checkbox>
                <el-checkbox label="captcha">图形验证码</el-checkbox>
              </el-checkbox-group>
            </el-form-item>
            <el-form-item label="用户协议">
              <el-switch v-model="registerForm.requireAgreement" />
            </el-form-item>
          </el-form>
        </el-tab-pane>

        <!-- 安全设置 -->
        <el-tab-pane label="安全设置" name="security">
          <el-form :model="securityForm" label-width="120px">
            <el-form-item label="密码强度">
              <el-radio-group v-model="securityForm.passwordStrength">
                <el-radio label="low">低（仅限制长度）</el-radio>
                <el-radio label="medium">中（要求包含字母和数字）</el-radio>
                <el-radio label="high">高（要求包含大小写字母、数字和特殊字符）</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item label="登录失败次数">
              <el-input-number v-model="securityForm.maxLoginAttempts" :min="1" :max="10" />
            </el-form-item>
            <el-form-item label="锁定时间(分钟)">
              <el-input-number v-model="securityForm.lockDuration" :min="5" :max="60" />
            </el-form-item>
            <el-form-item label="会话超时(分钟)">
              <el-input-number v-model="securityForm.sessionTimeout" :min="10" :max="120" />
            </el-form-item>
            <el-form-item label="启用双因素认证">
              <el-switch v-model="securityForm.enable2FA" />
            </el-form-item>
          </el-form>
        </el-tab-pane>

        <!-- 文件上传 -->
        <el-tab-pane label="文件上传" name="upload">
          <el-form :model="uploadForm" label-width="120px">
            <el-form-item label="存储方式">
              <el-radio-group v-model="uploadForm.storageType">
                <el-radio label="local">本地存储</el-radio>
                <el-radio label="oss">阿里云OSS</el-radio>
                <el-radio label="cos">腾讯云COS</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item label="最大文件大小">
              <el-input-number v-model="uploadForm.maxSize" :min="1" :max="50" />
              <span class="unit">MB</span>
            </el-form-item>
            <el-form-item label="允许的文件类型">
              <el-select v-model="uploadForm.allowedTypes" multiple placeholder="请选择文件类型">
                <el-option label="图片文件" value="image" />
                <el-option label="文档文件" value="document" />
                <el-option label="视频文件" value="video" />
                <el-option label="音频文件" value="audio" />
              </el-select>
            </el-form-item>
          </el-form>
        </el-tab-pane>
      </el-tabs>

      <div class="form-footer">
        <el-button type="primary" @click="saveSettings">保存设置</el-button>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { ElMessage } from 'element-plus'
import { Plus } from '@element-plus/icons-vue'

const activeTab = ref('system')

// 系统信息表单
const systemForm = ref({
  name: '',
  logo: '',
  description: '',
  copyright: '',
  support: ''
})

// 注册设置表单
const registerForm = ref({
  enableRegister: true,
  defaultRole: 'user',
  verifyMethods: ['captcha'],
  requireAgreement: true
})

// 安全设置表单
const securityForm = ref({
  passwordStrength: 'medium',
  maxLoginAttempts: 5,
  lockDuration: 30,
  sessionTimeout: 30,
  enable2FA: false
})

// 文件上传表单
const uploadForm = ref({
  storageType: 'local',
  maxSize: 10,
  allowedTypes: ['image', 'document']
})

// 处理Logo上传
const handleLogoSuccess = (res) => {
  systemForm.value.logo = res.url
  ElMessage.success('Logo上传成功')
}

const beforeLogoUpload = (file) => {
  const isImage = file.type.startsWith('image/')
  const isLt2M = file.size / 1024 / 1024 < 2

  if (!isImage) {
    ElMessage.error('只能上传图片文件!')
    return false
  }
  if (!isLt2M) {
    ElMessage.error('图片大小不能超过 2MB!')
    return false
  }
  return true
}

// 保存设置
const saveSettings = async () => {
  try {
    // TODO: 调用保存接口
    ElMessage.success('设置保存成功')
  } catch (error) {
    console.error(error)
    ElMessage.error('设置保存失败')
  }
}
</script>

<style scoped>
.config-container {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.logo-uploader {
  border: 1px dashed var(--el-border-color);
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: var(--el-transition-duration-fast);
}

.logo-uploader:hover {
  border-color: var(--el-color-primary);
}

.logo-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 178px;
  height: 178px;
  text-align: center;
  line-height: 178px;
}

.logo {
  width: 178px;
  height: 178px;
  display: block;
}

.form-footer {
  margin-top: 20px;
  text-align: center;
}

.unit {
  margin-left: 8px;
  color: var(--el-text-color-secondary);
}

:deep(.el-tabs__content) {
  padding: 20px 0;
}
</style> 
<!-- 编辑个人信息 -->

<template>
	<view class="edit-container">
		
		<!-- 编辑表单 -->
		<view class="edit-form">
			<!-- 头像 -->
			<view class="form-item avatar-item">
				<text class="form-label">头像</text>
				<view class="avatar-wrapper" @click="chooseImage">
					<image class="avatar" :src="userInfo.avatar" mode="aspectFill"></image>
					<view class="avatar-edit">
						<uni-icons type="camera" size="20" color="#fff"></uni-icons>
					</view>
				</view>
			</view>
			
			<!-- 基本信息 -->
			<view class="form-section">
				<view class="section-title">基本信息</view>
				
				<view class="form-item">
					<text class="form-label">用户名</text>
					<input class="form-input" type="text" v-model="userInfo.username" placeholder="请输入用户名" />
				</view>
				
				<view class="form-item">
					<text class="form-label">昵称</text>
					<input class="form-input" type="text" v-model="userInfo.nick_name" placeholder="请输入昵称" />
				</view>
				
				<view class="form-item">
					<text class="form-label">性别</text>
					<picker class="form-picker" @change="bindSexChange" :value="sexIndex" :range="sexArray">
						<view class="picker-value">{{sexArray[sexIndex]}}</view>
					</picker>
				</view>
				
				<view class="form-item">
					<text class="form-label">生日</text>
					<picker class="form-picker" mode="date" :value="userInfo.birth" @change="bindDateChange">
						<view class="picker-value">{{userInfo.birth || '请选择日期'}}</view>
					</picker>
				</view>
				
				<view class="form-item">
					<text class="form-label">手机号</text>
					<input class="form-input" type="number" v-model="userInfo.phone" placeholder="请输入手机号" maxlength="11" />
				</view>
				
				<view class="form-item">
					<text class="form-label">毕业学校</text>
					<input class="form-input" type="text" v-model="userInfo.user_school" placeholder="请输入毕业学校" />
				</view>
				
				<view class="form-item">
					<text class="form-label">考试类型</text>
					<input class="form-input" type="text" v-model="userInfo.user_form" placeholder="如：高考、中考等" />
				</view>
			</view>
			
			<!-- 成绩信息 -->
			<view class="form-section">
				<view class="section-title">成绩信息</view>
				
				<view class="form-item">
					<text class="form-label">语文</text>
					<input class="form-input" type="number" v-model="userInfo.Chinese" placeholder="请输入语文成绩" disabled />
				</view>
				
				<view class="form-item">
					<text class="form-label">数学</text>
					<input class="form-input" type="number" v-model="userInfo.math" placeholder="请输入数学成绩" disabled />
				</view>
				
				<view class="form-item">
					<text class="form-label">英语</text>
					<input class="form-input" type="number" v-model="userInfo.English" placeholder="请输入英语成绩" disabled />
				</view>
				
				<view class="form-item">
					<text class="form-label">文科综合</text>
					<input class="form-input" type="number" v-model="userInfo.Arts" placeholder="请输入文科综合成绩" disabled />
				</view>
				
				<view class="form-tip">
					<text>注：成绩信息不可直接修改，请联系管理员</text>
				</view>
			</view>
			
			<!-- 保存按钮 -->
			<view class="save-button-container">
				<button class="save-button" @click="saveUserInfo" :loading="isLoading">保存修改</button>
			</view>
		</view>
	</view>
</template>

<script>
	import request from '../../../utils/request.js';
	
	export default {
		data() {
			return {
				userInfo: {
					id: '',
					user_id: '',
					username: '',
					nick_name: '',
					avatar: '/static/c1.png',
					sex: '男',
					birth: '',
					phone: '',
					identity: '',
					user_school: '',
					user_form: '',
					Chinese: '',
					math: '',
					English: '',
					Arts: ''
				},
				sexArray: ['男', '女', '其他'],
				sexIndex: 0,
				isLoading: false
			}
		},
		onLoad() {
			this.getUserInfo();
		},
		methods: {
			// 获取用户信息
			getUserInfo() {
				try {
					const storedUserInfo = uni.getStorageSync('userInfo');
					if (storedUserInfo) {
						const userInfo = JSON.parse(storedUserInfo);
						console.log('获取到的用户信息:', userInfo);
						
						// 更新表单数据
						this.userInfo = {
							id: userInfo.id || userInfo.user_id || '',
							user_id: userInfo.user_id || userInfo.id || '',
							username: userInfo.username || '',
							nick_name: userInfo.nick_name || userInfo.name || '',
							avatar: userInfo.avatar || '/static/c1.png',
							sex: userInfo.sex || '男',
							birth: userInfo.birth || '',
							phone: userInfo.phone || '',
							identity: userInfo.identity || '',
							user_school: userInfo.user_school || '',
							user_form: userInfo.user_form || '',
							Chinese: userInfo.Chinese || '',
							math: userInfo.math || '',
							English: userInfo.English || '',
							Arts: userInfo.Arts || ''
						};
						
						// 设置性别索引
						this.sexIndex = this.sexArray.indexOf(this.userInfo.sex);
						if (this.sexIndex === -1) this.sexIndex = 0;
					}
				} catch (e) {
					console.error('获取用户信息失败', e);
					uni.showToast({
						title: '获取用户信息失败',
						icon: 'none'
					});
				}
			},
			
			// 返回上一页
			goBack() {
				uni.navigateBack();
			},
			
			// 选择头像
			chooseImage() {
				uni.chooseImage({
					count: 1,
					sizeType: ['compressed'],
					sourceType: ['album', 'camera'],
					success: (res) => {
						const tempFilePath = res.tempFilePaths[0];
						this.userInfo.avatar = tempFilePath;
						
						// 上传头像
						this.uploadAvatar(tempFilePath);
					}
				});
			},
			
			// 上传头像
			uploadAvatar(filePath) {
				uni.showLoading({
					title: '上传中...'
				});
				
				// 确保user_id是数字
				const userId = parseInt(this.userInfo.user_id || this.userInfo.id);
				
				uni.uploadFile({
					url: request.baseUrl + '/auth/uploadAvatar',
					filePath: filePath,
					name: 'avatar',
					formData: {
						user_id: userId
					},
					success: (uploadRes) => {
						try {
							// 解析响应数据
							const data = typeof uploadRes.data === 'string' ? JSON.parse(uploadRes.data) : uploadRes.data;
							
							if (data.code === 200) {
								// 更新头像URL
								const imageUrl = request.baseUrl + data.result.imageUrl;
								this.userInfo.avatar = imageUrl;
								
								// 更新本地存储
								this.updateLocalStorage('avatar', imageUrl);
								
								uni.showToast({
									title: '头像上传成功',
									icon: 'success'
								});
							} else {
								uni.showToast({
									title: data.message || '头像上传失败',
									icon: 'none'
								});
							}
						} catch (e) {
							console.error('解析上传结果失败', e);
							uni.showToast({
								title: '头像上传失败',
								icon: 'none'
							});
						}
					},
					fail: (err) => {
						console.error('上传请求失败:', err);
						uni.showToast({
							title: '头像上传失败',
							icon: 'none'
						});
					},
					complete: () => {
						uni.hideLoading();
					}
				});
			},
			
			// 更新本地存储的用户信息
			updateLocalStorage(field, value) {
				try {
					const storedUserInfo = uni.getStorageSync('userInfo');
					if (storedUserInfo) {
						const userInfo = JSON.parse(storedUserInfo);
						userInfo[field] = value;
						uni.setStorageSync('userInfo', JSON.stringify(userInfo));
					}
				} catch (e) {
					console.error('更新本地存储失败:', e);
				}
			},
			
			// 性别选择器变化
			bindSexChange(e) {
				this.sexIndex = e.detail.value;
				this.userInfo.sex = this.sexArray[this.sexIndex];
			},
			
			// 日期选择器变化
			bindDateChange(e) {
				this.userInfo.birth = e.detail.value;
			},
			
			// 保存用户信息
			saveUserInfo() {
				if (this.isLoading) return;
				
				// 表单验证
				if (!this.userInfo.username) {
					uni.showToast({
						title: '请输入用户名',
						icon: 'none'
					});
					return;
				}
				
				if (!this.userInfo.phone) {
					uni.showToast({
						title: '请输入手机号',
						icon: 'none'
					});
					return;
				}
				
				this.isLoading = true;
				
				// 显示加载提示
				uni.showLoading({
					title: '保存中...',
					mask: true
				});
				
				// 构建请求数据 - 只包含有值的字段
				const requestData = {};
				
				// 必须字段 - 确保是数字类型
				requestData.user_id = parseInt(this.userInfo.user_id || this.userInfo.id);
				
				// 可选字段 - 只添加有值的字段
				if (this.userInfo.username) requestData.username = this.userInfo.username;
				if (this.userInfo.nick_name) requestData.nick_name = this.userInfo.nick_name;
				if (this.userInfo.sex) requestData.sex = this.userInfo.sex;
				
				// 处理日期格式 - 确保是YYYY-MM-DD格式
				if (this.userInfo.birth) {
					// 如果包含T，说明是ISO格式，需要转换
					if (this.userInfo.birth.includes('T')) {
						try {
							const birthDate = new Date(this.userInfo.birth);
							requestData.birth = birthDate.toISOString().split('T')[0]; // 只取日期部分
						} catch (e) {
							requestData.birth = this.userInfo.birth;
						}
					} else {
						requestData.birth = this.userInfo.birth;
					}
				}
				
				if (this.userInfo.phone) requestData.phone = this.userInfo.phone;
				if (this.userInfo.identity) requestData.identity = this.userInfo.identity;
				if (this.userInfo.user_school) requestData.user_school = this.userInfo.user_school;
				if (this.userInfo.user_form) requestData.user_form = this.userInfo.user_form;
				
				// 使用request模块发送请求
				request.post('/auth/updateUser', requestData)
					.then(res => {
						if (res.code === 200) {
							// 更新本地存储
							const storedUserInfo = uni.getStorageSync('userInfo');
							let originalUserInfo = {};
							if (storedUserInfo) {
								try {
									originalUserInfo = JSON.parse(storedUserInfo);
								} catch (e) {
									console.error('解析本地存储的用户信息失败:', e);
								}
							}
							
							// 合并用户信息，保留原有字段
							const updatedUserInfo = {
								...originalUserInfo,
								...this.userInfo,
								// 确保这些字段正确设置
								id: this.userInfo.id || this.userInfo.user_id || originalUserInfo.id || originalUserInfo.user_id,
								user_id: this.userInfo.user_id || this.userInfo.id || originalUserInfo.user_id || originalUserInfo.id,
								name: this.userInfo.nick_name || this.userInfo.username || originalUserInfo.name,
								// 确保avatar字段正确设置，包含完整URL
								avatar: this.userInfo.avatar || originalUserInfo.avatar,
								// 确保保留原有的成绩信息，注意字段名称
								Chinese: originalUserInfo.Chinese || this.userInfo.Chinese,
								math: originalUserInfo.math || this.userInfo.math,
								English: originalUserInfo.English || this.userInfo.English,
								Arts: originalUserInfo.Arts || this.userInfo.Arts,
								// 确保更新学校信息
								user_school: this.userInfo.user_school || originalUserInfo.user_school
							};
							
							uni.setStorageSync('userInfo', JSON.stringify(updatedUserInfo));
							
							uni.showToast({
								title: '保存成功',
								icon: 'success'
							});
							
							setTimeout(() => {
								uni.navigateBack();
							}, 1500);
						} else {
							uni.showToast({
								title: res.message || '保存失败',
								icon: 'none'
							});
						}
					})
					.catch(err => {
						uni.showToast({
							title: '网络请求失败',
							icon: 'none'
						});
					})
					.finally(() => {
						uni.hideLoading();
						this.isLoading = false;
					});
			}
		}
	}
</script>

<style>
.edit-container {
	min-height: 100vh;
	background-color: #f5f5f5;
}



.edit-form {
	padding: 15px;
	padding-bottom: 0px; /* 为底部按钮留出空间 */
}

.form-section {
	background-color: #fff;
	border-radius: 8px;
	margin-bottom: 15px;
	padding: 15px;
}

.section-title {
	font-size: 16px;
	font-weight: bold;
	margin-bottom: 15px;
	color: #333;
}

.form-item {
	display: flex;
	align-items: center;
	padding: 12px 0;
	border-bottom: 1px solid #f0f0f0;
}

.form-item:last-child {
	border-bottom: none;
}

.form-label {
	width: 80px;
	font-size: 14px;
	color: #333;
}

.form-input {
	flex: 1;
	height: 24px;
	font-size: 14px;
}

.form-picker {
	flex: 1;
}

.picker-value {
	font-size: 14px;
	color: #333;
}

.avatar-item {
	padding: 20px 0;
}

.avatar-wrapper {
	position: relative;
	width: 80px;
	height: 80px;
	border-radius: 50%;
	overflow: hidden;
}

.avatar {
	width: 100%;
	height: 100%;
	object-fit: cover;
}

.avatar-edit {
	position: absolute;
	bottom: 0;
	left: 0;
	right: 0;
	height: 30px;
	background-color: rgba(0, 0, 0, 0.5);
	display: flex;
	justify-content: center;
	align-items: center;
}

.form-tip {
	padding: 10px 0;
	font-size: 12px;
	color: #999;
	text-align: center;
}

/* 保存按钮样式 */
.save-button-container {
	padding: 20px 15px 40px;
	margin-top: 20px;
}

.save-button {
	width: 100%;
	height: 45px;
	line-height: 45px;
	background-color: #d4237a;
	color: #fff;
	font-size: 16px;
	border-radius: 22.5px;
	box-shadow: 0 5px 15px rgba(212, 35, 122, 0.3);
}

.save-button:active {
	background-color: #c01e6c;
}
</style>





<template>
	<view class="more-school-container">
		<!-- 头部搜索 -->
		<view class="search-section">
			<view class="search-box">
				<uni-icons type="search" size="18" color="#999"></uni-icons>
				<input 
					class="search-input" 
					placeholder="搜索学校名称" 
					v-model="searchKeyword"
					@input="onSearchInput"
				/>
			</view>
		</view>

		<!-- 学校列表 -->
		<view class="school-list" v-if="!loading">
			<view v-if="filteredSchools.length === 0" class="no-data">
				<uni-icons type="info" size="40" color="#ccc"></uni-icons>
				<text class="no-data-text">暂无学校数据</text>
			</view>
			
			<view v-else>
				<view 
					class="school-card" 
					v-for="(school, index) in filteredSchools" 
					:key="school.school_id"
					@click="viewSchoolDetail(school)"
				>
					<image 
						:src="getSchoolImage(school)" 
						mode="aspectFill" 
						class="school-logo"
						@error="handleImageError(index)"
					></image>
					<view class="school-info">
						<text class="school-name">{{school.school_name}}</text>
						<text class="school-address" v-if="school.school_address">{{school.school_address}}</text>
						<view class="school-stats">
							<text class="stat-item" v-if="school.school_number">招生：{{school.school_number}}人</text>
							<text class="stat-item" v-if="school.Score_line">分数线：{{school.Score_line}}</text>
						</view>
					</view>
					<uni-icons type="right" size="16" color="#ccc"></uni-icons>
				</view>
			</view>
		</view>

		<!-- 加载状态 -->
		<view v-if="loading" class="loading-container">
			<uni-load-more status="loading"></uni-load-more>
		</view>
	</view>
</template>

<script>
	import request from '../../../utils/request.js';

	export default {
		data() {
			return {
				loading: true,
				searchKeyword: '',
				schoolList: [],
				filteredSchools: []
			}
		},
		
		onLoad() {
			this.getSchoolList();
		},
		
		methods: {
			// 获取学校列表
			getSchoolList() {
				this.loading = true;
				
				request.post('/school/', {})
				.then(res => {
					console.log('获取学校列表成功:', res);
					if (res.code === 200 && res.result) {
						this.schoolList = res.result.map(school => {
							// 处理图片URL
							if (school.school_image) {
								if (!school.school_image.startsWith('http')) {
									school.school_image = request.baseUrl + (school.school_image.startsWith('/') ? school.school_image : '/' + school.school_image);
								}
							} else {
								school.school_image = '/static/c1.png';
							}
							return school;
						});
						this.filteredSchools = [...this.schoolList];
					} else {
						this.schoolList = [];
						this.filteredSchools = [];
					}
				})
				.catch(err => {
					console.error('获取学校列表失败:', err);
					this.schoolList = [];
					this.filteredSchools = [];
					uni.showToast({
						title: '获取数据失败',
						icon: 'none'
					});
				})
				.finally(() => {
					this.loading = false;
				});
			},
			
			// 搜索输入
			onSearchInput() {
				this.filterSchools();
			},
			
			// 筛选学校
			filterSchools() {
				let filtered = [...this.schoolList];
				
				// 按关键词筛选
				if (this.searchKeyword.trim()) {
					const keyword = this.searchKeyword.trim().toLowerCase();
					filtered = filtered.filter(school => 
						school.school_name.toLowerCase().includes(keyword) ||
						(school.school_address && school.school_address.toLowerCase().includes(keyword))
					);
				}
				
				this.filteredSchools = filtered;
			},
			
			// 获取学校图片
			getSchoolImage(school) {
				if (school.school_image && school.school_image.trim() !== '') {
					if (school.school_image.startsWith('http')) {
						return school.school_image;
					} else {
						return request.baseUrl + (school.school_image.startsWith('/') ? school.school_image : '/' + school.school_image);
					}
				}
				return '/static/c1.png';
			},
			
			// 处理图片加载错误
			handleImageError(index) {
				this.$set(this.filteredSchools, index, {
					...this.filteredSchools[index],
					school_image: '/static/c1.png'
				});
			},
			
			// 查看学校详情
			viewSchoolDetail(school) {
				uni.navigateTo({
					url: `/pages/Home/function/school_detail?id=${school.school_id}&name=${encodeURIComponent(school.school_name)}`
				});
			}
		}
	}
</script>

<style scoped>
.more-school-container {
	background: #f8f9fa;
	min-height: 100vh;
}

.search-section {
	background: #fff;
	padding: 16px;
	border-bottom: 1px solid #f0f0f0;
}

.search-box {
	display: flex;
	align-items: center;
	background: #f8f9fa;
	border-radius: 20px;
	padding: 8px 16px;
	gap: 8px;
}

.search-input {
	flex: 1;
	font-size: 14px;
	color: #333;
}

.school-list {
	padding: 16px;
}

.no-data {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 60px 0;
	color: #999;
}

.no-data-text {
	margin-top: 12px;
	font-size: 14px;
}

.school-card {
	display: flex;
	align-items: center;
	background: #fff;
	border-radius: 12px;
	padding: 16px;
	margin-bottom: 12px;
	box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.school-logo {
	width: 60px;
	height: 60px;
	border-radius: 8px;
	margin-right: 12px;
	flex-shrink: 0;
}

.school-info {
	flex: 1;
	display: flex;
	flex-direction: column;
	gap: 4px;
}

.school-name {
	font-size: 16px;
	font-weight: bold;
	color: #333;
}

.school-address {
	font-size: 13px;
	color: #666;
}

.school-stats {
	display: flex;
	gap: 12px;
	margin-top: 4px;
}

.stat-item {
	font-size: 12px;
	color: #999;
}

.loading-container {
	padding: 20px;
}
</style>




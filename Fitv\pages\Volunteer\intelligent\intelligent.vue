<!-- 智能ai -->

<template>
	<view class="ai-container">
		<!-- 顶部状态栏占位 -->
		<view class="status-bar"></view>
		
		<!-- 导航栏 -->
		<view class="nav-bar">
			<view class="back-btn" @click="goBack">
				<uni-icons type="left" size="20" color="#333"></uni-icons>
			</view>
			<text class="page-title">
				<view class="ai-title">
					<view class="ai-icon">AI</view>
					智能助手
				</view>
			</text>
		</view>
		
		<!-- 聊天内容区域 -->
		<scroll-view 
			scroll-y 
			class="chat-content" 
			:scroll-top="scrollTop"
			:scroll-with-animation="true"
			@scrolltoupper="loadMoreMessages"
			ref="chatScroll"
		>
			<view class="welcome-message" v-if="messages.length === 0">
				<view class="ai-logo">
					<view class="ai-logo-icon">AI</view>
				</view>
				<text class="welcome-text">您好，我是志愿填报AI助手</text>
				<text class="welcome-desc">我可以帮您分析院校、专业信息，解答填报疑问</text>
				<view class="quick-questions">
					<view class="question-item" v-for="(item, index) in quickQuestions" :key="index" @click="askQuickQuestion(item)">
						<text class="question-text">{{item}}</text>
					</view>
				</view>
			</view>
			
			<view class="message-list" v-else>
				<view 
					v-for="(item, index) in messages" 
					:key="index" 
					:class="['message-item', item.role === 'user' ? 'user-message' : 'ai-message']"
				>
					<!-- AI消息 -->
					<template v-if="item.role !== 'user'">
						<view class="message-avatar">
							<view class="ai-avatar">AI</view>
						</view>
						<view class="message-content">
							<view class="message-text" v-if="item.content && item.content[0] && item.content[0].type === 'text'">{{item.content[0].text}}</view>
							<view class="message-text" v-else-if="item.content && typeof item.content === 'string'">{{item.content}}</view>
							<view class="message-text" v-else>{{JSON.stringify(item.content)}}</view>
							<image 
								v-if="item.content && item.content[0] && item.content[0].type === 'image_url'" 
								:src="item.content[0].image_url.url" 
								mode="widthFix" 
								class="message-image"
								@tap="previewImage(item.content[0].image_url.url)"
							></image>
						</view>
					</template>
					
					<!-- 用户消息 -->
					<template v-else>
						<view class="message-content">
							<view class="message-text" v-if="item.content && item.content[0] && item.content[0].type === 'text'">{{item.content[0].text}}</view>
							<view class="message-text" v-else-if="item.content && typeof item.content === 'string'">{{item.content}}</view>
							<view class="message-text" v-else>{{JSON.stringify(item.content)}}</view>
							<image 
								v-if="item.content && item.content[0] && item.content[0].type === 'image_url'" 
								:src="item.content[0].image_url.url" 
								mode="widthFix" 
								class="message-image"
								@tap="previewImage(item.content[0].image_url.url)"
							></image>
						</view>
						<view class="message-avatar">
							<view class="user-avatar">我</view>
						</view>
					</template>
				</view>
				
				<view class="typing-indicator" v-if="isLoading">
					<view class="typing-dot"></view>
					<view class="typing-dot"></view>
					<view class="typing-dot"></view>
				</view>
			</view>
		</scroll-view>
		
		<!-- 输入区域 -->
		<view class="input-area">
			<view class="input-box">
				<textarea 
					class="input-field" 
					v-model="inputMessage" 
					placeholder="请输入您的问题..."
					:disable-default-padding="true"
					:cursor-spacing="10"
					:show-confirm-bar="false"
					:auto-height="true"
					:maxlength="-1"
					@confirm="sendMessage"
				></textarea>
				<view class="input-actions">
					<view class="action-item" @click="chooseImage">
						<uni-icons type="image" size="24" color="#666"></uni-icons>
					</view>
					<view class="action-item" @click="sendMessage">
						<uni-icons type="paperplane" size="24" color="#d4237a"></uni-icons>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				messages: [],
				inputMessage: '',
				scrollTop: 0,
				isLoading: false,
				quickQuestions: [
					'如何选择适合自己的大学?',
					'计算机专业就业前景如何?',
					'985和211大学有什么区别?',
					'如何根据分数合理填报志愿?'
				],
				apiConfig: {
					url: 'https://ark.cn-beijing.volces.com/api/v3/chat/completions',
					headers: {
						'Content-Type': 'application/json',
						'Authorization': 'Bearer 8326e452-2dbb-4739-b05e-ba2de8bb82c0'
					},
					model: 'doubao-seed-1-6-flash-250615'
				},
				selectedImage: null
			}
		},
		onLoad() {
			// 获取状态栏高度
			const systemInfo = uni.getSystemInfoSync();
			this.statusBarHeight = systemInfo.statusBarHeight;
		},
		methods: {
			// 返回上一页
			goBack() {
				uni.navigateBack();
			},
			
			// 发送消息
			sendMessage() {
				if (!this.inputMessage.trim() && !this.selectedImage) return;
				
				// 构建用户消息
				const userMessage = {
					role: 'user',
					content: []
				};
				
				// 添加文本内容
				if (this.inputMessage.trim()) {
					userMessage.content.push({
						type: 'text',
						text: this.inputMessage.trim()
					});
				}
				
				// 添加图片内容
				if (this.selectedImage) {
					userMessage.content.push({
						type: 'image_url',
						image_url: {
							url: this.selectedImage
						}
					});
				}
				
				// 添加到消息列表
				this.messages.push(userMessage);
				
				// 清空输入框和已选图片
				const currentMessage = this.inputMessage;
				this.inputMessage = '';
				this.selectedImage = null;
				
				// 滚动到底部
				this.scrollToBottom();
			
				// 发送请求到API
				this.sendToAPI([...this.messages], 0, currentMessage);
			},
			
			// 发送到API
			sendToAPI(messagesArray, retryCount = 0, originalMessage = '') {
				this.isLoading = true;
				
				// 构建请求数据
				const requestData = {
					model: this.apiConfig.model,
					messages: messagesArray
				};
				
				console.log('发送请求数据:', JSON.stringify(requestData));
				
				// 发送请求
				uni.request({
					url: this.apiConfig.url,
					method: 'POST',
					header: this.apiConfig.headers,
					data: requestData,
					timeout: 20000, // 设置超时时间为20秒
					success: (res) => {
						uni.hideLoading();
						
						// 详细记录API响应结构
						console.log('响应数据结构:', res.data ? Object.keys(res.data) : 'null');
						
						// 检查响应格式并适配不同的API响应结构
						if (res.statusCode === 200 && res.data) {
							let aiResponse = null;
							
							// 尝试获取AI响应
							if (res.data.choices && res.data.choices.length > 0 && res.data.choices[0].message) {
								// 标准格式
								aiResponse = res.data.choices[0].message;
							} else if (res.data.response) {
								// 可能的替代格式
								aiResponse = {
									role: 'assistant',
									content: [{
										type: 'text',
										text: res.data.response
									}]
								};
							} else if (typeof res.data === 'string') {
								// 纯文本响应
								aiResponse = {
									role: 'assistant',
									content: [{
										type: 'text',
										text: res.data
									}]
								};
							} else if (res.data.message || res.data.content || res.data.text) {
								// 其他可能的格式
								const text = res.data.message || res.data.content || res.data.text;
								aiResponse = {
									role: 'assistant',
									content: [{
										type: 'text',
										text: typeof text === 'string' ? text : JSON.stringify(text)
									}]
								};
							} else {
								// 直接使用整个响应数据
								const responseText = JSON.stringify(res.data);
								aiResponse = {
									role: 'assistant',
									content: [{
										type: 'text',
										text: responseText
									}]
								};
							}
							
							this.messages.push(aiResponse);
							
							// 滚动到底部
							this.scrollToBottom();
						} else {
							// 处理错误，使用模拟数据
							console.error('API响应格式错误，使用模拟数据:', res);
							this.useMockResponse(messagesArray[messagesArray.length - 1]);
						}
					},
					fail: (err) => {
						uni.hideLoading();
						// 处理请求失败
						console.error('请求失败，使用模拟数据:', err);
						this.useMockResponse(messagesArray[messagesArray.length - 1]);
					},
					complete: () => {
						this.isLoading = false;
					}
				});
			},
			
			// 使用模拟数据响应
			useMockResponse(userMessage) {
				// 获取用户最后一条消息的文本内容
				let userText = '';
				if (userMessage && userMessage.content && userMessage.content.length > 0) {
					for (const content of userMessage.content) {
						if (content.type === 'text') {
							userText = content.text;
							break;
						}
					}
				}
			
		
				// 滚动到底部
				this.scrollToBottom();
				
				// 提示用户
				uni.showToast({
					title: '网络连接失败，使用离线回复',
					icon: 'none',
					duration: 2000
				});
			},
			
			// 滚动到底部
			scrollToBottom() {
				setTimeout(() => {
					// 获取消息列表高度
					const query = uni.createSelectorQuery().in(this);
					query.select('.message-list').boundingClientRect(data => {
						if (data) {
							this.scrollTop = data.height + 1000; // 确保滚动到底部
						}
					}).exec();
				}, 100);
			},
			
			// 加载更多消息（向上滚动时）
			loadMoreMessages() {
				// 这里可以实现加载历史消息的逻辑
				console.log('加载更多消息');
			},
			
			// 选择图片
			chooseImage() {
				uni.chooseImage({
					count: 1,
					sizeType: ['compressed'],
					sourceType: ['album', 'camera'],
					success: (res) => {
						// 上传图片
						this.uploadImage(res.tempFilePaths[0]);
					}
				});
			},
			
			// 上传图片
			uploadImage(filePath) {
				uni.showLoading({
					title: '上传中...'
				});
				
				// 为了演示，我们直接使用本地路径
				setTimeout(() => {
					uni.hideLoading();
					this.selectedImage = filePath;
					
					// 显示已选图片提示
					uni.showToast({
						title: '图片已选择',
						icon: 'success'
					});
				}, 1000);
			},
			
			// 预览图片
			previewImage(url) {
				uni.previewImage({
					urls: [url],
					current: url
				});
			},
			
			// 快速提问
			askQuickQuestion(question) {
				this.inputMessage = question;
				this.sendMessage();
			}
		}
	}
</script>

<style>
.ai-container {
	min-height: 100vh;
	background-color: #f5f5f5;
	display: flex;
	flex-direction: column;
}

/* 状态栏占位 */
.status-bar {
	height: var(--status-bar-height);
	width: 100%;
}

/* 导航栏 */
.nav-bar {
	display: flex;
	align-items: center;
	height: 44px;
	background-color: #fff;
	padding: 0 15px;
	position: relative;
	border-bottom: 1px solid #f0f0f0;
}

.back-btn {
	width: 30px;
	height: 30px;
	display: flex;
	align-items: center;
}

.page-title {
	position: absolute;
	left: 50%;
	transform: translateX(-50%);
	font-size: 16px;
	font-weight: bold;
}

.ai-title {
	display: flex;
	align-items: center;
}

.ai-icon {
	width: 24px;
	height: 24px;
	border-radius: 12px;
	background: linear-gradient(135deg, #d4237a, #ff6a9c);
	color: #fff;
	font-size: 12px;
	font-weight: bold;
	display: flex;
	justify-content: center;
	align-items: center;
	margin-right: 5px;
}

/* 聊天内容区域 */
.chat-content {
	flex: 1;
	padding: 10px;
}

/* 欢迎信息 */
.welcome-message {
	display: flex;
	flex-direction: column;
	align-items: center;
	padding: 30px 20px;
}

.ai-logo {
	width: 80px;
	height: 80px;
	border-radius: 40px;
	background: linear-gradient(135deg, #d4237a, #ff6a9c);
	display: flex;
	justify-content: center;
	align-items: center;
	margin-bottom: 20px;
	box-shadow: 0 5px 15px rgba(212, 35, 122, 0.3);
}

.ai-logo-icon {
	color: #fff;
	font-size: 36px;
	font-weight: bold;
}

.welcome-text {
	font-size: 18px;
	font-weight: bold;
	margin-bottom: 10px;
}

.welcome-desc {
	font-size: 14px;
	color: #666;
	margin-bottom: 20px;
	text-align: center;
}

.quick-questions {
	width: 100%;
	margin-top: 20px;
}

.question-item {
	background-color: #fff;
	padding: 12px 15px;
	border-radius: 8px;
	margin-right: 15px;
	margin-bottom: 10px;
	box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
}

.question-text {
	font-size: 14px;
	color: #333;
}

/* 消息列表 */
.message-list {
	padding-bottom: 10px;
}

.message-item {
	display: flex;
	margin-bottom: 15px;
	align-items: flex-start;
	width: 100%;
}

.user-message {
	flex-direction: row-reverse;
	justify-content: flex-end;
}

.ai-message {
	justify-content: flex-start;
}

.message-avatar {
	width: 40px;
	height: 40px;
	margin: 0 0px;
	display: flex;
	justify-content: center;
	align-items: center;
	flex-shrink: 0;
}

.user-message .message-avatar {
	margin-right: 0;
}

.ai-message .message-avatar {
	margin-left: 0;
}

.ai-avatar {
	color: #000;
	font-size: 24px;
	font-weight: bold;
}

.user-avatar {
	color: #000;
	font-size: 24px;
	font-weight: bold;
}

.message-content {
	max-width: 70%;
	padding: 12px 16px;
	border-radius: 16px;
	position: relative;
}

.user-message .message-content {
	background: #d4237a;
	border-bottom-right-radius: 4px;
}

.ai-message .message-content {
	background: #fff;
	border: 1px solid #f0f0f0;
	border-bottom-left-radius: 4px;
}

.message-text {
	font-size: 14px;
	line-height: 1.5;
	word-wrap: break-word;
}

.user-message .message-text {
	color: #fff;
}

.ai-message .message-text {
	color: #333;
}

.message-image {
	width: 100%;
	border-radius: 8px;
	margin-top: 8px;
}

/* 输入区域 */
.input-area {
	background-color: #fff;
	padding: 10px 15px;
	border-top: 1px solid #f0f0f0;
	padding-bottom: calc(10px + env(safe-area-inset-bottom));
}

.input-box {
	display: flex;
	align-items: flex-end;
	background-color: #f5f5f5;
	border-radius: 20px;
	padding: 8px 15px;
}

.input-field {
	flex: 1;
	font-size: 14px;
	min-height: 25px;
	max-height: 40px;
	padding: 0;
	margin: 1;
}

.input-actions {
	display: flex;
	margin-left: 10px;
}

.action-item {
	padding: 5px;
	margin-left: 5px;
}

/* 打字指示器 */
.typing-indicator {
	display: flex;
	align-items: center;
	margin-bottom: 20px;
}

.typing-indicator .message-avatar {
	margin-right: 12px;
}

.typing-dots {
	display: flex;
	gap: 4px;
	padding: 12px 16px;
	background: #fff;
	border: 1px solid #f0f0f0;
	border-radius: 16px;
	border-bottom-left-radius: 4px;
}

.dot {
	width: 6px;
	height: 6px;
	border-radius: 50%;
	background: #667eea;
	animation: typing 1.4s infinite ease-in-out;
}

.dot:nth-child(1) { animation-delay: 0s; }
.dot:nth-child(2) { animation-delay: 0.2s; }
.dot:nth-child(3) { animation-delay: 0.4s; }

@keyframes typing {
	0%, 60%, 100% { transform: translateY(0); }
	30% { transform: translateY(-8px); }
}
</style>





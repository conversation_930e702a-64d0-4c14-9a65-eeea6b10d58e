<!-- 专业列表 页面 -->
<template>
    <view class="specialized-container">
        <!-- 状态栏占位 -->
        <view class="status-bar"></view>
        
        <!-- 顶部搜索栏 -->
        <view class="search-section">
            <view class="search-box">
                <uni-icons type="search" size="18" color="#999"></uni-icons>
                <input 
                    class="search-input" 
                    placeholder="搜索专业名称" 
                    v-model="searchKeyword"
                    @input="onSearchInput"
                />
            </view>
        </view>
        
        <!-- 专业列表 -->
        <view class="specialized-list" v-if="!loading">
            <view 
                class="specialized-card" 
                v-for="(item, index) in filteredSpecializedList" 
                :key="item.specialized_id"
                @click="viewSpecializedDetail(item)"
            >
                <view class="card-header">
                    <image 
                        :src="item.image || '/static/majors/default.jpg'" 
                        mode="aspectFill" 
                        class="specialized-image"
                        @error="handleImageError(index)"
                    ></image>
                    <view class="specialized-info">
                        <text class="specialized-name">{{item.specialized_content}}</text>
                        <text class="specialized-type" v-if="item.specialized_type">{{item.specialized_type}}</text>
                        <text class="specialized-degree" v-if="item.specialized_degree">{{item.specialized_degree}}</text>
                    </view>
                </view>
                
                <view class="card-content" v-if="item.specialized_part">
                    <text class="specialized-desc">{{item.specialized_part}}</text>
                </view>

            </view>
        </view>
        
        <!-- 加载状态 -->
        <view v-if="loading" class="loading-container">
            <uni-icons type="spinner-cycle" size="24" color="#d4237a"></uni-icons>
            <text class="loading-text">加载中...</text>
        </view>
        
        <!-- 空状态 -->
        <view v-if="!loading && filteredSpecializedList.length === 0" class="empty-container">
            <image src="/static/empty.png" class="empty-image"></image>
            <text class="empty-text">暂无专业信息</text>
        </view>
    </view>
</template>

<script>
import request from '../../../utils/request.js';

export default {
    data() {
        return {
            loading: true,
            searchKeyword: '',
            specializedList: [],
            filteredSpecializedList: []
        }
    },
    
    onLoad() {
        this.getSpecializedList();
    },
    
    methods: {
        // 获取专业列表
        getSpecializedList() {
            this.loading = true;
            
            request.post('/specialized/getAllWithDetail', {}).then(res => {
                if (res.code === 200 && res.result && res.result.length > 0) {
                    this.specializedList = res.result.map(item => {
                        return {
                            specialized_id: item.specialized_id,
                            specialized_content: item.specialized_content,
                            specialized_part: item.specialized_part,
                            specialized_degree: item.specialized_degree,
                            specialized_type: item.specialized_type,
                            specialized_introduce: item.specialized_introduce,
                            specialized_course: item.specialized_course,
                            specialized_employment: item.specialized_employment,
                            image: this.processImageUrl(item.specialized_image)
                        };
                    });
                    
                    // 初始化筛选列表
                    this.filteredSpecializedList = [...this.specializedList];
                } else {
                    uni.showToast({
                        title: '获取专业列表失败',
                        icon: 'none'
                    });
                }
            }).catch(err => {
                console.error('获取专业列表失败', err);
                uni.showToast({
                    title: '网络请求失败',
                    icon: 'none'
                });
            }).finally(() => {
                this.loading = false;
            });
        },
        
        // 处理图片URL
        processImageUrl(imageUrl) {
            if (!imageUrl) return '/static/majors/default.jpg';
            
            if (imageUrl.startsWith('http')) {
                try {
                    const url = new URL(imageUrl);
                    return request.baseUrl + url.pathname;
                } catch (e) {
                    const pathMatch = imageUrl.match(/https?:\/\/[^\/]+(.+)/);
                    if (pathMatch) {
                        return request.baseUrl + pathMatch[1];
                    }
                }
            }
            
            if (imageUrl.startsWith('/')) {
                return request.baseUrl + imageUrl;
            } else {
                return request.baseUrl + '/' + imageUrl;
            }
        },
        
        // 搜索输入处理
        onSearchInput() {
            this.filterSpecializedList();
        },
        
        // 筛选专业列表
        filterSpecializedList() {
            let filtered = [...this.specializedList];
            
            // 按关键词筛选
            if (this.searchKeyword.trim()) {
                const keyword = this.searchKeyword.trim().toLowerCase();
                filtered = filtered.filter(item => 
                    item.specialized_content.toLowerCase().includes(keyword) ||
                    (item.specialized_part && item.specialized_part.toLowerCase().includes(keyword))
                );
            }
            
            this.filteredSpecializedList = filtered;
        },
        
        // 查看专业详情
        viewSpecializedDetail(specialized) {
            uni.navigateTo({
                url: `/pages/Home/function/specialized_detail?id=${specialized.specialized_id}&name=${specialized.specialized_content}`
            });
        },
        
        // 图片加载失败处理
        handleImageError(index) {
            this.$set(this.filteredSpecializedList, index, {
                ...this.filteredSpecializedList[index],
                image: '/static/majors/default.jpg'
            });
        }
    }
}
</script>

<style scoped>
.specialized-container {
    background: #f8f9fa;
    min-height: 100vh;
}

.search-section {
    background: #fff;
    padding: 16px;
    border-bottom: 1px solid #f0f0f0;
}

.search-box {
    display: flex;
    align-items: center;
    height: 40px;
    background: #f8f9fa;
    border-radius: 20px;
    padding: 0 16px;
}

.search-input {
    flex: 1;
    font-size: 14px;
    margin-left: 8px;
}

.filter-section {
    background: #fff;
    padding: 12px 0;
    border-bottom: 1px solid #f0f0f0;
}

.filter-scroll {
    white-space: nowrap;
    padding: 0 16px;
}

.filter-item {
    display: inline-block;
    padding: 6px 16px;
    margin-right: 12px;
    background: #f8f9fa;
    border-radius: 16px;
    font-size: 14px;
    color: #666;
    white-space: nowrap;
}

.filter-item.active {
    background: #d4237a;
    color: #fff;
}

.specialized-list {
    padding: 16px;
}

.specialized-card {
    background: #fff;
    border-radius: 12px;
    margin-bottom: 16px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.card-header {
    display: flex;
    padding: 16px;
}

.specialized-image {
    width: 80px;
    height: 80px;
    border-radius: 8px;
    margin-right: 16px;
}

.specialized-info {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.specialized-name {
    font-size: 18px;
    font-weight: bold;
    color: #333;
    margin-bottom: 8px;
}

.specialized-type {
    font-size: 14px;
    color: #666;
    margin-bottom: 4px;
}

.specialized-degree {
    font-size: 12px;
    color: #999;
}

.card-content {
    padding: 0 16px 16px;
}

.specialized-desc {
    font-size: 14px;
    color: #666;
    line-height: 1.5;
}


.loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 60px 20px;
}

.loading-text {
    margin-top: 12px;
    font-size: 14px;
    color: #999;
}

.empty-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 60px 20px;
}

.empty-image {
    width: 120px;
    height: 120px;
    margin-bottom: 16px;
}

.empty-text {
    font-size: 16px;
    color: #999;
}
</style>







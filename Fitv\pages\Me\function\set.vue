<!-- 设置页面 -->

<template>
	<view class="settings-container">
		<!-- 账户设置 -->
		<view class="settings-section">
			<view class="section-title">账户设置</view>
			<view class="settings-list">
				<view class="settings-item" @click="navigateTo('/pages/Me/function/edit')">
					<view class="item-left">
						<uni-icons type="person" size="20" color="#666"></uni-icons>
						<text class="item-text">个人信息</text>
					</view>
					<view class="item-right">
						<uni-icons type="right" size="16" color="#ccc"></uni-icons>
					</view>
				</view>
				
				<view class="settings-item" @click="changePassword">
					<view class="item-left">
						<uni-icons type="locked" size="20" color="#666"></uni-icons>
						<text class="item-text">修改密码</text>
					</view>
					<view class="item-right">
						<uni-icons type="right" size="16" color="#ccc"></uni-icons>
					</view>
				</view>
			</view>
		</view>
		
		<!-- 通知设置 -->
		<view class="settings-section">
			<view class="section-title">通知设置</view>
			<view class="settings-list">
				<view class="settings-item">
					<view class="item-left">
						<uni-icons type="sound" size="20" color="#666"></uni-icons>
						<text class="item-text">消息通知</text>
					</view>
					<view class="item-right">
						<switch :checked="notificationSettings.message" @change="toggleNotification('message')" />
					</view>
				</view>
				
				<view class="settings-item">
					<view class="item-left">
						<uni-icons type="email" size="20" color="#666"></uni-icons>
						<text class="item-text">邮件通知</text>
					</view>
					<view class="item-right">
						<switch :checked="notificationSettings.email" @change="toggleNotification('email')" />
					</view>
				</view>
			</view>
		</view>
		
		<!-- 其他设置 -->
		<view class="settings-section">
			<view class="section-title">其他</view>
			<view class="settings-list">
				<view class="settings-item" @click="clearCache">
					<view class="item-left">
						<uni-icons type="trash" size="20" color="#666"></uni-icons>
						<text class="item-text">清除缓存</text>
					</view>
					<view class="item-right">
						<text class="cache-size">{{cacheSize}}</text>
						<uni-icons type="right" size="16" color="#ccc"></uni-icons>
					</view>
				</view>
				
				<view class="settings-item" @click="aboutApp">
					<view class="item-left">
						<uni-icons type="info" size="20" color="#666"></uni-icons>
						<text class="item-text">关于应用</text>
					</view>
					<view class="item-right">
						<text class="version">v1.0.0</text>
						<uni-icons type="right" size="16" color="#ccc"></uni-icons>
					</view>
				</view>
			</view>
		</view>
		
		<!-- 退出登录 -->
		<view class="logout-section">
			<view class="logout-btn" @click="logout">
				退出登录
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				notificationSettings: {
					message: true,
					email: false
				},
				cacheSize: '12.5MB'
			}
		},
		onLoad() {
			this.loadSettings();
		},
		methods: {
			// 加载设置
			loadSettings() {
				try {
					const settings = uni.getStorageSync('notificationSettings');
					if (settings) {
						this.notificationSettings = JSON.parse(settings);
					}
				} catch (e) {
					console.error('加载设置失败:', e);
				}
			},
			
			// 保存设置
			saveSettings() {
				try {
					uni.setStorageSync('notificationSettings', JSON.stringify(this.notificationSettings));
				} catch (e) {
					console.error('保存设置失败:', e);
				}
			},
			
			// 切换通知设置
			toggleNotification(type) {
				this.notificationSettings[type] = !this.notificationSettings[type];
				this.saveSettings();
				uni.showToast({
					title: '设置已保存',
					icon: 'success'
				});
			},
			
			// 修改密码
			changePassword() {
				uni.navigateTo({
					url:'/pages/Me/forget/revise'
				})
			},
			
			// 清除缓存
			clearCache() {
				uni.showModal({
					title: '清除缓存',
					content: '确定要清除所有缓存数据吗？',
					success: (res) => {
						if (res.confirm) {
							// 清除缓存逻辑
							uni.showToast({
								title: '缓存已清除',
								icon: 'success'
							});
							this.cacheSize = '0MB';
						}
					}
				});
			},
			
			// 关于应用
			aboutApp() {
				uni.showModal({
					title: '关于应用',
					content: '志愿填报助手 v1.0.0\n帮助学生更好地填报志愿',
					showCancel: false
				});
			},
			
			// 页面跳转
			navigateTo(path) {
				uni.navigateTo({
					url: path
				});
			},
			
			// 退出登录
			logout() {
				uni.showModal({
					title: '退出登录',
					content: '确定要退出登录吗？',
					success: (res) => {
						if (res.confirm) {
							uni.removeStorageSync('token');
							uni.removeStorageSync('userInfo');
							uni.removeStorageSync('savedAccount');
							
							uni.reLaunch({
								url: '/pages/Me/Login'
							});
						}
					}
				});
			}
		}
	}
</script>

<style>
.settings-container {
	background-color: #f5f5f5;
	min-height: 100vh;
}

.settings-section {
	margin-bottom: 10px;
}

.section-title {
	padding: 15px;
	font-size: 14px;
	color: #666;
	background-color: #f5f5f5;
}

.settings-list {
	background-color: #fff;
}

.settings-item {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 15px;
	border-bottom: 1px solid #f0f0f0;
}

.settings-item:last-child {
	border-bottom: none;
}

.item-left {
	display: flex;
	align-items: center;
}

.item-text {
	font-size: 16px;
	margin-left: 12px;
}

.item-right {
	display: flex;
	align-items: center;
}

.cache-size, .version {
	font-size: 14px;
	color: #999;
	margin-right: 8px;
}

.logout-section {
	margin-top: 20px;
	padding: 0 15px;
}

.logout-btn {
	height: 45px;
	line-height: 45px;
	text-align: center;
	background-color: #fff;
	border-radius: 5px;
	color: #ff3b30;
	font-size: 16px;
}
</style>


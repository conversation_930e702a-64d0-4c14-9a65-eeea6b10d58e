<template>
  <div class="admission-list-container">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <span>已录取志愿列表</span>
          <div>
            <el-button type="primary" @click="fetchData">刷新数据</el-button>
          </div>
        </div>
      </template>

      <!-- 搜索栏 -->
      <el-form :inline="true" :model="searchForm" class="search-form">
        <el-form-item label="考生姓名">
          <el-input v-model="searchForm.nick_name" placeholder="请输入考生姓名" />
        </el-form-item>
        <el-form-item label="院校名称">
          <el-input v-model="searchForm.school_name" placeholder="请输入院校名称" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">搜索</el-button>
          <el-button @click="resetSearch">重置</el-button>
        </el-form-item>
      </el-form>

      <!-- 数据表格 -->
      <el-table :data="tableData" style="width: 100%" v-loading="loading" border stripe>
        <el-table-column prop="admission_id" label="ID" width="80" />
        <el-table-column prop="nick_name" label="考生姓名" min-width="100" />
        <el-table-column prop="sex" label="性别" width="60" />
        <el-table-column prop="phone" label="联系电话" width="130" />
        <el-table-column prop="user_form" label="考试类型" width="100" />
        <el-table-column prop="user_fraction" label="考生分数" width="80" />
        <el-table-column prop="school_name" label="院校名称" min-width="150" />
        <el-table-column prop="specialized_content" label="专业" min-width="120" />
        <el-table-column prop="admission_state" label="状态" width="100">
          <template #default="scope">
            <el-tag :type="getStatusType(scope.row.admission_state)">
              {{ scope.row.admission_state || '未设置' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="180" fixed="right">
          <template #default="scope">
            <el-button-group>
              <el-button type="primary" link @click="handleEdit(scope.row)">编辑</el-button>
              <el-button type="danger" link @click="handleDelete(scope.row)">删除</el-button>
            </el-button-group>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 编辑对话框 -->
    <el-dialog
      v-model="dialogVisible"
      title="编辑志愿信息"
      width="50%"
    >
      <el-form :model="form" label-width="120px" ref="formRef" :rules="rules">
        <el-form-item label="考生姓名" prop="nick_name">
          <el-input v-model="form.nick_name" />
        </el-form-item>
        <el-form-item label="院校名称" prop="school_name">
          <el-input v-model="form.school_name" />
        </el-form-item>
        <el-form-item label="专业名称" prop="specialized_content">
          <el-input v-model="form.specialized_content" />
        </el-form-item>
        <el-form-item label="考试分数" prop="user_fraction">
          <el-input v-model="form.user_fraction" type="number" />
        </el-form-item>
        <el-form-item label="联系电话" prop="phone">
          <el-input v-model="form.phone" />
        </el-form-item>
        <el-form-item label="录取状态" prop="admission_state">
          <el-select v-model="form.admission_state" placeholder="请选择状态">
            <el-option label="待审核" value="待审核" />
            <el-option label="已录取" value="已录取" />
            <el-option label="未录取" value="未录取" />
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitForm">确认</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import axios from '@/utils/request'

// 搜索表单
const searchForm = ref({
  nick_name: '',
  school_name: ''
})

// 表格数据
const loading = ref(false)
const tableData = ref([])
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)

// 表单相关
const dialogVisible = ref(false)
const formRef = ref(null)
const form = ref({
  admission_id: '',
  user_id: '',
  school_id: '',
  specialized_id: '',
  nick_name: '',
  school_name: '',
  specialized_content: '',
  user_fraction: '',
  phone: '',
  admission_state: '待审核'
})

const rules = {
  nick_name: [{ required: true, message: '请输入考生姓名', trigger: 'blur' }],
  school_name: [{ required: true, message: '请输入院校名称', trigger: 'blur' }],
  specialized_content: [{ required: true, message: '请输入专业名称', trigger: 'blur' }],
  user_fraction: [{ required: true, message: '请输入考试分数', trigger: 'blur' }],
  phone: [
    { required: true, message: '请输入联系电话', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
  ],
  admission_state: [{ required: true, message: '请选择录取状态', trigger: 'change' }]
}

// 获取状态类型
const getStatusType = (status) => {
  switch (status) {
    case '已录取':
      return 'success'
    case '未录取':
      return 'danger'
    case '待审核':
      return 'warning'
    default:
      return 'info'
  }
}

// 搜索
const handleSearch = () => {
  currentPage.value = 1
  fetchData()
}

// 重置搜索
const resetSearch = () => {
  searchForm.value = {
    nick_name: '',
    school_name: ''
  }
  handleSearch()
}

// 获取数据
const fetchData = async () => {
  loading.value = true
  try {
    // 构建查询参数 - 默认只查询已录取的用户
    const params = {
      admission_state: '已录取'
    }
    
    // 添加搜索条件
    if (searchForm.value.nick_name && searchForm.value.nick_name.trim()) {
      params.nick_name = searchForm.value.nick_name.trim()
    }
    if (searchForm.value.school_name && searchForm.value.school_name.trim()) {
      params.school_name = searchForm.value.school_name.trim()
    }
    
    console.log('发送请求参数:', params)
    
    // 发送请求
    const response = await axios.post('/admission/', params)
    
    console.log('接收到的响应:', response)
    
    // 处理响应数据
    if (response && response.code === 200) {
      // 尝试从不同位置获取数据
      let admissionData = []
      
      if (Array.isArray(response.result)) {
        admissionData = response.result
      } else if (Array.isArray(response.data)) {
        admissionData = response.data
      } else if (response.data && Array.isArray(response.data.data)) {
        admissionData = response.data.data
      }
      
      // 确保只显示已录取状态的数据
      admissionData = admissionData.filter(item => item.admission_state === '已录取')
      
      // 如果后端没有过滤，在前端进行过滤
      if (params.nick_name || params.school_name) {
        admissionData = admissionData.filter(item => {
          let matchNickName = true
          let matchSchoolName = true
          
          if (params.nick_name) {
            matchNickName = item.nick_name && item.nick_name.includes(params.nick_name)
          }
          
          if (params.school_name) {
            matchSchoolName = item.school_name && item.school_name.includes(params.school_name)
          }
          
          return matchNickName && matchSchoolName
        })
      }
      
      tableData.value = admissionData
      total.value = admissionData.length
      
      if (admissionData.length === 0) {
        ElMessage.info('未找到匹配的已录取记录')
      } 
      // else {
      //   ElMessage.success(`找到 ${admissionData.length} 条已录取记录`)
      // }
      
      console.log('处理后的表格数据:', tableData.value)
    } else {
      tableData.value = []
      total.value = 0
      ElMessage.error(response?.message || '获取数据失败')
    }
  } catch (error) {
    console.error('获取志愿列表失败:', error)
    ElMessage.error(error.message || '获取数据失败')
    tableData.value = []
    total.value = 0
  } finally {
    loading.value = false
  }
}

// 编辑
const handleEdit = (row) => {
  form.value = JSON.parse(JSON.stringify(row))
  dialogVisible.value = true
}

// 提交表单
const submitForm = async () => {
  if (!formRef.value) return

  await formRef.value.validate(async (valid) => {
    if (valid) {
      try {
        const response = await axios.post('/admission/update', form.value)
        
        if (response && response.code === 200) {
          ElMessage.success('更新成功')
          dialogVisible.value = false
          fetchData()
        } else {
          ElMessage.error(response?.message || '更新失败')
        }
      } catch (error) {
        console.error('更新失败:', error)
        ElMessage.error(error.message || '更新失败')
      }
    }
  })
}

// 删除
const handleDelete = (row) => {
  ElMessageBox.confirm('确认删除该志愿记录吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      const response = await axios.post('/admission/delete', {
        admission_id: row.admission_id
      })
      
      if (response && response.code === 200) {
        ElMessage.success('删除成功')
        fetchData()
      } else {
        ElMessage.error(response?.message || '删除失败')
      }
    } catch (error) {
      console.error('删除失败:', error)
      ElMessage.error(error.message || '删除失败')
    }
  }).catch(() => {
    ElMessage.info('已取消删除')
  })
}

// 分页
const handleSizeChange = (val) => {
  pageSize.value = val
  fetchData()
}

const handleCurrentChange = (val) => {
  currentPage.value = val
  fetchData()
}

// 初始加载
onMounted(() => {
  fetchData()
})
</script>

<style scoped>
.admission-list-container {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.search-form {
  margin-bottom: 20px;
  padding: 20px;
  background-color: var(--el-fill-color-light);
  border-radius: 4px;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}
</style> 

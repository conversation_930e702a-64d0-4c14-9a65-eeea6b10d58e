<!-- 搜索页面 -->

<template>
	<view class="search-container">
		<!-- 搜索头部 -->
		<view class="search-header">
			<view class="search-input-box">
				<uni-icons type="search" size="18" color="#999"></uni-icons>
				<input 
					class="search-input" 
					type="text" 
					v-model="keyword" 
					placeholder="请输入学校名称关键词" 
					confirm-type="search"
					@confirm="handleSearch"
				/>
				<uni-icons v-if="keyword" type="clear" size="18" color="#999" @click="clearKeyword"></uni-icons>
			</view>
			<view class="search-btn" @click="handleSearch">搜索</view>
		</view>
		
		<!-- 搜索结果 -->
		<view class="search-result">
			<!-- 加载中 -->
			<view class="loading" v-if="loading">
				<uni-icons type="spinner-cycle" size="30" color="#d4237a"></uni-icons>
				<text class="loading-text">正在搜索...</text>
			</view>
			
			<!-- 无结果 -->
			<view class="empty-result" v-else-if="searchResult.length === 0 && hasSearched">
				<image class="empty-image" src="/static/1.png" mode="aspectFit"></image>
				<text class="empty-text">未找到相关学校</text>
			</view>
			
			<!-- 搜索结果列表 -->
			<view class="result-list" v-else>
				<view class="result-item" v-for="(item, index) in searchResult" :key="index" @click="goToDetail(item)">
					<view class="school-info">
						<image class="school-image" :src="getSchoolImage(item)" mode="aspectFill"></image>
						<view class="school-detail">
							<text class="school-name">{{item.school_name || '未知学校'}}</text>
							<text class="school-address">{{item.school_address || '地址未知'}}</text>
							<br />
							<text class="school-type">{{item.identity || '类型未知'}}</text>
						</view>
					</view>
					<uni-icons type="right" size="16" color="#999"></uni-icons>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import request from '../../../utils/request.js';
	
	export default {
		data() {
			return {
				keyword: '', // 搜索关键词
				searchResult: [], // 搜索结果
				loading: false, // 加载状态
				hasSearched: false // 是否已经搜索过
			}
		},
		onLoad(options) {
			// 如果有传入的关键词，自动搜索
			if (options.keyword) {
				this.keyword = options.keyword;
				this.handleSearch();
			}
		},
		// 处理页面显示
		onShow() {
			// 检查是否有搜索结果但没有显示
			if (this.hasSearched && this.searchResult.length > 0 && !this.loading) {
				// 重新处理图片URL，确保正确显示
				this.searchResult = this.searchResult.map(item => {
					if (item.school_image) {
						// 确保图片URL是完整的
						if (!item.school_image.startsWith('http')) {
							item.school_image = request.baseUrl + (item.school_image.startsWith('/') ? item.school_image : '/' + item.school_image);
						}
					}
					return item;
				});
			}
		},
		// 处理返回事件
		onBackPress() {
			this.notifyHomeToRefresh();
			return false; // 返回false让系统默认处理返回事件
		},
		// 页面卸载时触发
		onUnload() {
			this.notifyHomeToRefresh();
		},
		methods: {
			// 通知Home页面刷新数据
			notifyHomeToRefresh() {
				// 使用全局事件总线通知Home页面刷新
				uni.$emit('home-refresh');
				
				// 设置一个标记到本地存储，表示需要刷新Home页面
				uni.setStorageSync('home_need_refresh', 'true');
			},
			// 执行搜索
			handleSearch() {
				if (!this.keyword.trim()) {
					uni.showToast({
						title: '请输入搜索关键词',
						icon: 'none'
					});
					return;
				}
				
				this.loading = true;
				this.hasSearched = true;
				
				// 调用搜索接口
				request.post('/search/searchSchool', {
					keyword: this.keyword
				})
				.then(res => {
					this.loading = false;
					
					if (res.code === 200) {
						this.searchResult = res.data || [];
						
						// 如果没有搜索结果
						if (this.searchResult.length === 0) {
							uni.showToast({
								title: '未找到相关学校',
								icon: 'none'
							});
						}
					} else {
						uni.showToast({
							title: res.message || '搜索失败',
							icon: 'none'
						});
					}
				})
				.catch(err => {
					this.loading = false;
					uni.showToast({
						title: '搜索请求失败',
						icon: 'none'
					});
				});
			},
			
			// 清空关键词
			clearKeyword() {
				this.keyword = '';
			},
			
			// 获取学校图片，如果没有则使用默认图片
			getSchoolImage(school) {
				if (school.school_image) {
					// 检查是否是完整URL
					if (school.school_image.startsWith('http')) {
						return school.school_image;
					} else {
						return request.baseUrl + school.school_image;
					}
				}
				return '/static/c1.png'; // 默认图片
			},
			
			// 跳转到学校详情页
			goToDetail(school) {
				// 在跳转前标记需要刷新首页
				this.notifyHomeToRefresh();
				
				// 确保有学校ID
				const schoolId = school.school_id || 0;
				
				// 使用学校ID和名称跳转到详情页
				uni.navigateTo({
					url: `/pages/Home/function/school_detail?id=${schoolId}&name=${encodeURIComponent(school.school_name || '未知学校')}`,
					success: () => {
						console.log('导航到学校详情页成功');
					},
					fail: (err) => {
						console.error('导航到学校详情页失败', err);
						uni.showToast({
							title: '页面跳转失败',
							icon: 'none'
						});
					}
				});
			}
		}
	}
</script>

<style>
.search-container {
	min-height: 100vh;
	background-color: #f5f5f5;
	padding-bottom: 20px;
}

.search-header {
	display: flex;
	align-items: center;
	padding: 10px 15px;
	background-color: #fff;
	position: sticky;
	top: 0;
	z-index: 100;
}

.search-input-box {
	flex: 1;
	display: flex;
	align-items: center;
	height: 36px;
	background-color: #f5f5f5;
	border-radius: 18px;
	padding: 0 12px;
	margin-right: 10px;
}

.search-input {
	flex: 1;
	height: 36px;
	font-size: 14px;
	margin: 0 8px;
}

.search-btn {
	padding: 0 15px;
	height: 36px;
	line-height: 36px;
	background-color: #d4237a;
	color: #fff;
	border-radius: 18px;
	font-size: 14px;
}

.search-result {
	padding: 10px;
}

.loading {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 40px 0;
}

.loading-text {
	margin-top: 10px;
	font-size: 14px;
	color: #999;
}

.empty-result {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 60px 0;
}

.empty-image {
	width: 100px;
	height: 100px;
	opacity: 0.5;
}

.empty-text {
	margin-top: 15px;
	font-size: 14px;
	color: #999;
}

.result-list {
	background-color: #fff;
	border-radius: 8px;
	overflow: hidden;
}

.result-item {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 15px;
	border-bottom: 1px solid #f0f0f0;
}

.result-item:last-child {
	border-bottom: none;
}

.school-info {
	display: flex;
	align-items: center;
	flex: 1;
}

.school-image {
	width: 60px;
	height: 60px;
	border-radius: 4px;
	background-color: #f5f5f5;
}

.school-detail {
	flex: 1;
	margin-left: 12px;
	overflow: hidden;
}

.school-name {
	font-size: 16px;
	color: #333;
	font-weight: bold;
	margin-bottom: 4px;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
}

.school-address {
	font-size: 12px;
	color: #666;
	margin-bottom: 4px;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
}

.school-type {
	font-size: 12px;
	color: #999;
	background-color: #f5f5f5;
	padding: 2px 6px;
	border-radius: 2px;
	display: inline-block;
}
</style>

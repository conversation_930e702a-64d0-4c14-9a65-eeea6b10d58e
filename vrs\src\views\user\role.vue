<template>
  <div class="role-container">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <span>角色管理</span>
          <el-button type="primary" @click="handleAddRole">新增角色</el-button>
        </div>
      </template>

      <el-table :data="roleList" style="width: 100%" v-loading="loading">
        <el-table-column prop="roleName" label="角色名称" width="180" />
        <el-table-column prop="roleCode" label="角色编码" width="180" />
        <el-table-column prop="description" label="角色描述" />
        <el-table-column prop="createTime" label="创建时间" width="180" />
        <el-table-column prop="status" label="状态" width="100">
          <template #default="scope">
            <el-switch
              v-model="scope.row.status"
              :active-value="1"
              :inactive-value="0"
              @change="handleStatusChange(scope.row)"
            />
          </template>
        </el-table-column>
        <el-table-column label="操作" width="250" fixed="right">
          <template #default="scope">
            <el-button type="primary" link @click="handleEdit(scope.row)">编辑</el-button>
            <el-button type="primary" link @click="handlePermission(scope.row)">权限设置</el-button>
            <el-button type="danger" link @click="handleDelete(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 角色表单对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogType === 'add' ? '新增角色' : '编辑角色'"
      width="500px"
    >
      <el-form
        ref="roleFormRef"
        :model="roleForm"
        :rules="rules"
        label-width="100px"
      >
        <el-form-item label="角色名称" prop="roleName">
          <el-input v-model="roleForm.roleName" placeholder="请输入角色名称" />
        </el-form-item>
        <el-form-item label="角色编码" prop="roleCode">
          <el-input v-model="roleForm.roleCode" placeholder="请输入角色编码" />
        </el-form-item>
        <el-form-item label="角色描述" prop="description">
          <el-input
            v-model="roleForm.description"
            type="textarea"
            placeholder="请输入角色描述"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitRole">确定</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 权限设置对话框 -->
    <el-dialog
      v-model="permissionDialogVisible"
      title="权限设置"
      width="600px"
    >
      <el-tree
        ref="permissionTreeRef"
        :data="permissionList"
        show-checkbox
        node-key="id"
        :props="defaultProps"
        :default-checked-keys="checkedKeys"
      />
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="permissionDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitPermission">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { ref, reactive } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'

export default {
  name: 'RoleManagement',
  setup() {
    const loading = ref(false)
    const dialogVisible = ref(false)
    const permissionDialogVisible = ref(false)
    const dialogType = ref('add')
    const currentRole = ref(null)

    // 角色列表数据
    const roleList = ref([
      {
        id: 1,
        roleName: '超级管理员',
        roleCode: 'SUPER_ADMIN',
        description: '系统超级管理员',
        createTime: '2024-03-15 10:00:00',
        status: 1
      },
      {
        id: 2,
        roleName: '普通管理员',
        roleCode: 'ADMIN',
        description: '普通管理员',
        createTime: '2024-03-15 10:00:00',
        status: 1
      }
    ])

    // 角色表单
    const roleForm = reactive({
      roleName: '',
      roleCode: '',
      description: ''
    })

    // 表单验证规则
    const rules = {
      roleName: [
        { required: true, message: '请输入角色名称', trigger: 'blur' },
        { min: 2, max: 20, message: '长度在 2 到 20 个字符', trigger: 'blur' }
      ],
      roleCode: [
        { required: true, message: '请输入角色编码', trigger: 'blur' },
        { min: 2, max: 20, message: '长度在 2 到 20 个字符', trigger: 'blur' }
      ]
    }

    // 权限树数据
    const permissionList = ref([
      {
        id: 1,
        label: '系统管理',
        children: [
          {
            id: 11,
            label: '用户管理'
          },
          {
            id: 12,
            label: '角色管理'
          }
        ]
      },
      {
        id: 2,
        label: '考生管理',
        children: [
          {
            id: 21,
            label: '考生信息'
          },
          {
            id: 22,
            label: '志愿填报'
          }
        ]
      }
    ])

    const defaultProps = {
      children: 'children',
      label: 'label'
    }

    const checkedKeys = ref([])

    // 新增角色
    const handleAddRole = () => {
      dialogType.value = 'add'
      dialogVisible.value = true
      Object.assign(roleForm, {
        roleName: '',
        roleCode: '',
        description: ''
      })
    }

    // 编辑角色
    const handleEdit = (row) => {
      dialogType.value = 'edit'
      currentRole.value = row
      dialogVisible.value = true
      Object.assign(roleForm, {
        roleName: row.roleName,
        roleCode: row.roleCode,
        description: row.description
      })
    }

    // 提交角色
    const submitRole = () => {
      // 实现角色保存逻辑
      dialogVisible.value = false
      ElMessage.success(dialogType.value === 'add' ? '新增成功' : '修改成功')
    }

    // 删除角色
    const handleDelete = (row) => {
      ElMessageBox.confirm(
        `确认删除角色"${row.roleName}"吗？`,
        '警告',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        }
      )
        .then(() => {
          ElMessage.success('删除成功')
        })
        .catch(() => {})
    }

    // 状态变更
    const handleStatusChange = (row) => {
      ElMessage.success(`${row.roleName}状态更新成功`)
    }

    // 权限设置
    const handlePermission = (row) => {
      currentRole.value = row
      permissionDialogVisible.value = true
      // 模拟获取当前角色的权限
      checkedKeys.value = [11, 21]
    }

    // 提交权限
    const submitPermission = () => {
      permissionDialogVisible.value = false
      ElMessage.success('权限设置成功')
    }

    return {
      loading,
      roleList,
      dialogVisible,
      permissionDialogVisible,
      dialogType,
      roleForm,
      rules,
      permissionList,
      defaultProps,
      checkedKeys,
      handleAddRole,
      handleEdit,
      handleDelete,
      handleStatusChange,
      handlePermission,
      submitRole,
      submitPermission
    }
  }
}
</script>

<style scoped>
.role-container {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.dialog-footer {
  padding-top: 20px;
  text-align: right;
}
</style> 
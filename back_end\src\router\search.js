//搜索接口

const express = require('express');
const router = express.Router();
const db = require('../config/db');

// 根据学校名称搜索学校
router.post('/searchSchool', (req, res) => {
    const { keyword } = req.body;
    
    if (!keyword) {
        return res.send({
            code: 201,
            message: '搜索关键词不能为空'
        });
    }
    
    // 使用LIKE进行模糊搜索
    const sql = `SELECT * FROM school WHERE school_name LIKE ?`;
    const searchKeyword = `%${keyword}%`;
    
    db.query(sql, [searchKeyword], (err, result) => {
        if (err) {
            return res.send({
                code: 201,
                message: '搜索失败',
                error: err.message
            });
        }
        
        res.send({
            code: 200,
            message: '搜索成功',
            total: result.length,
            data: result
        });
    });
});

// 根据用户名搜索用户
router.post('/searchUser', (req, res) => {
    const { keyword } = req.body;
    
    if (!keyword) {
        return res.send({
            code: 201,
            message: '搜索关键词不能为空'
        });
    }
    
    // 使用LIKE进行模糊搜索，支持用户名和昵称搜索
    const sql = `SELECT user_id, username, nick_name, user_image, phone, identity, user_school 
                 FROM user 
                 WHERE username LIKE ? OR nick_name LIKE ?`;
    const searchKeyword = `%${keyword.trim()}%`;
    
    db.query(sql, [searchKeyword, searchKeyword], (err, result) => {
        if (err) {
            return res.send({
                code: 201,
                message: '搜索失败',
                error: err.message
            });
        }
        
        res.send({
            code: 200,
            message: '搜索成功',
            total: result.length,
            data: result
        });
    });
});

module.exports = router;

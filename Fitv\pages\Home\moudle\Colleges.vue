<template>
	<view class="colleges-section">
		<view class="section-header">
			<text class="section-title">推荐院校</text>
			<text class="more-btn" @click="viewMore">查看更多 ></text>
		</view>
		<view class="school-list">
			<view 
				class="school-card" 
				v-for="(item, index) in schools" 
				:key="index"
				@click="viewSchoolDetail(item)"
			>
				<image :src="item.school_image" mode="aspectFit" class="school-logo"></image>
				<view class="school-info">
					<text class="school-name">{{item.school_name}}</text>
					<view class="school-tags">
						<!-- 移除identity字段的渲染 -->
					</view>
				
					<view class="school-stats">
						<text class="stat-item">电话: {{item.school_phone || '暂无'}}</text>
						<text class="stat-item">邮箱: {{item.school_email || '暂无'}}</text>
					</view>
				</view>
				<view class="school-score">
					<text class="score-label">参考分数</text>
					<text class="score-value">{{item.Score_line || '暂无'}}</text>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
import request from '../../../utils/request.js';

export default {
	name: 'Colleges',
	data() {
		return {
			schools: []
		}
	},
	created() {
		this.getSchools();
	},
	methods: {
		// 获取推荐院校数据
		getSchools() {
			// 清空现有数据，避免显示旧数据
			this.schools = [];
			
			// 显示加载中状态
			uni.showLoading({
				title: '加载中...',
				mask: false
			});
			
			request.post('/school/', {}).then(res => {
				uni.hideLoading();
				
				if (res.code === 200 && res.result && res.result.length > 0) {
					// 只取前3条数据
					this.schools = res.result.slice(0, 3).map(item => {
						// 处理图片URL
						if (item.school_image) {
							if (!item.school_image.startsWith('http')) {
								item.school_image = request.baseUrl + (item.school_image.startsWith('/') ? item.school_image : '/' + item.school_image);
							}
						} else {
							item.school_image = '/static/c1.png'; // 默认图片
						}
						return item;
					});
				} else {
					this.setDefaultSchools();
				}
			}).catch(err => {
				uni.hideLoading();
				console.error('获取院校数据失败', err);
				this.setDefaultSchools();
			});
		},
		
		// 设置默认院校数据
		setDefaultSchools() {
			this.schools = [];
		},
		
		// 查看更多
		viewMore() {
			uni.navigateTo({
				url: '/pages/Home/function/more_school'
			});
		},
		
		// 查看院校详情
		viewSchoolDetail(school) {
			this.$emit('view-detail', school);
		}
	}
}
</script>

<style scoped>
.colleges-section {
	background: #fff;
	margin: 0 16px 16px;
	border-radius: 12px;
	padding: 16px;
}

.section-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 16px;
}

.section-title {
	font-size: 18px;
	font-weight: bold;
	color: #333;
}

.more-btn {
	font-size: 14px;
	color: #d4237a;
}

.school-list {
	display: flex;
	flex-direction: column;
	gap: 12px;
}

.school-card {
	display: flex;
	align-items: center;
	padding: 12px;
	border: 1px solid #f0f0f0;
	border-radius: 8px;
}

.school-logo {
	width: 60px;
	height: 60px;
	border-radius: 8px;
	margin-right: 12px;
}

.school-info {
	flex: 1;
}

.school-name {
	font-size: 16px;
	font-weight: bold;
	color: #333;
	margin-bottom: 4px;
}

.school-tags {
	display: flex;
	gap: 4px;
	margin-bottom: 4px;
}

.school-tag {
	font-size: 10px;
	padding: 2px 6px;
	border-radius: 10px;
	background: #e3f2fd;
	color: #1976d2;
}


.school-stats {
	display: flex;
	flex-direction: column; /* Changed from row to column */
	gap: 4px; /* Adjusted gap for vertical layout */
}

.stat-item {
	font-size: 11px;
	color: #999;
}

.school-score {
	display: flex;
	flex-direction: column;
	align-items: center;
}

.score-label {
	font-size: 10px;
	color: #999;
	margin-bottom: 2px;
}

.score-value {
	font-size: 14px;
	font-weight: bold;
	color: #d4237a;
}
</style>

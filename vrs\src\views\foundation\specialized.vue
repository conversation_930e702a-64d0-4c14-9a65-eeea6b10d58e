<!-- 专业类型管理 -->

<template>
  <div class="specialized-container">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <span>专业类型管理</span>
          <el-button type="primary" @click="handleAdd">添加专业类型</el-button>
        </div>
      </template>
      
      <el-table :data="tableData" style="width: 100%" v-loading="loading" row-key="specialized_id">
        <el-table-column prop="specialized_id" label="ID" width="80" />
        <el-table-column prop="specialized_content" label="专业分类" />
        <el-table-column label="操作" width="280" fixed="right">
          <template #default="scope">
            <el-button-group>
              <el-button type="primary" link @click="handleEdit(scope.row)">编辑</el-button>
              <el-button type="success" link @click="handleViewDetail(scope.row)">查看详情</el-button>
              <el-button type="danger" link @click="handleDelete(scope.row)">删除</el-button>
            </el-button-group>
          </template>
        </el-table-column>
      </el-table>

      <div class="pagination-container">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 30, 50]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 添加/编辑对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogType === 'add' ? '添加专业类型' : '编辑专业类型'"
      width="700px"
    >
      <el-form :model="form" :rules="rules" ref="formRef" label-width="100px">
        <el-form-item label="专业名称" prop="specialized_content" required>
          <el-input v-model="form.specialized_content" placeholder="请输入专业名称（必填）" />
        </el-form-item>
        
        <el-divider content-position="center">专业详情（以下内容均为选填）</el-divider>
        
        <el-form-item label="专业简介" prop="specialized_part">
          <el-input v-model="form.specialized_part" placeholder="请输入专业简介，如：人工智能时代核心专业（选填）" />
        </el-form-item>
        
        <el-form-item label="授予学位" prop="specialized_degree">
          <el-input v-model="form.specialized_degree" placeholder="请输入授予学位，如：工学学士（选填）" />
        </el-form-item>
        
        <el-form-item label="所属门类" prop="specialized_type">
          <el-input v-model="form.specialized_type" placeholder="请输入所属门类，如：工学（选填）" />
        </el-form-item>
        
        <el-form-item label="专业介绍" prop="specialized_introduce">
          <el-input 
            v-model="form.specialized_introduce" 
            type="textarea" 
            :rows="4"
            placeholder="请输入专业简介（选填）" 
          />
        </el-form-item>
        
        <el-form-item label="主要课程" prop="specialized_course">
          <el-input 
            v-model="form.specialized_course" 
            type="textarea" 
            :rows="3"
            placeholder="请输入主要课程，如：数据结构、计算机组成原理等（选填）" 
          />
        </el-form-item>
        
        <el-form-item label="就业方向" prop="specialized_employment">
          <el-input 
            v-model="form.specialized_employment" 
            type="textarea" 
            :rows="3"
            placeholder="请输入就业方向（选填）" 
          />
        </el-form-item>
        
        <el-form-item label="专业图片" prop="specialized_image">
          <div class="upload-container">
            <el-upload
              class="image-uploader"
              :action="`${baseURL}/specialized/uploadImage`"
              :show-file-list="false"
              :on-success="handleImageSuccess"
              :on-error="handleImageError"
              :before-upload="beforeImageUpload"
              :headers="uploadHeaders"
              name="image"
              :with-credentials="false"
              :timeout="30000"
            >
              <img v-if="form.specialized_image" :src="getImageDisplayUrl(form.specialized_image)" class="uploaded-image" />
              <el-icon v-else class="upload-icon"><Plus /></el-icon>
            </el-upload>
            <div class="upload-tip">
              <p>点击上传图片，或直接输入图片URL</p>
              <el-input v-model="form.specialized_image" placeholder="请输入专业图片URL（选填）" />
            </div>
          </div>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSubmit">确定</el-button>
        </span>
      </template>
    </el-dialog>
    
    <!-- 专业详情对话框 -->
    <el-dialog
      v-model="detailDialogVisible"
      title="专业详情"
      width="700px"
    >
      <div class="detail-container" v-loading="detailLoading">
        <h2 class="detail-title">{{ detailData.specialized_content }}</h2>
        
        <el-descriptions border :column="1" class="detail-descriptions">
          <el-descriptions-item label="专业ID">{{ detailData.specialized_id }}</el-descriptions-item>
          <el-descriptions-item label="所属门类">{{ detailData.specialized_type }}</el-descriptions-item>
          <el-descriptions-item label="专业分类">{{ detailData.specialized_part }}</el-descriptions-item>
          <el-descriptions-item label="授予学位">{{ detailData.specialized_degree }}</el-descriptions-item>
        </el-descriptions>
        
        <div class="detail-section">
          <h3>专业简介</h3>
          <p>{{ detailData.specialized_introduce }}</p>
        </div>
        
        <div class="detail-section">
          <h3>主要课程</h3>
          <p>{{ detailData.specialized_course }}</p>
        </div>
        
        <div class="detail-section">
          <h3>就业方向</h3>
          <p>{{ detailData.specialized_employment }}</p>
        </div>
        
        <div class="detail-section" v-if="detailData.specialized_image">
          <h3>专业图片</h3>
          <div class="image-container">
            <el-image 
              :src="getImageDisplayUrl(detailData.specialized_image)" 
              fit="contain"
              :preview-src-list="[getImageDisplayUrl(detailData.specialized_image)]"
              class="specialized-image"
            ></el-image>
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import axios from '@/utils/request'
import { Plus } from '@element-plus/icons-vue'

const loading = ref(false)
const tableData = ref([])
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)
const dialogVisible = ref(false)
const dialogType = ref('add')
const formRef = ref(null)

// 详情弹窗相关
const detailDialogVisible = ref(false)
const detailData = ref({})
const detailLoading = ref(false) // 新增的加载状态

const form = ref({
  specialized_content: '',
  specialized_part: '',
  specialized_degree: '',
  specialized_type: '',
  specialized_introduce: '',
  specialized_course: '',
  specialized_employment: '',
  specialized_image: ''
})

const rules = {
  specialized_content: [
    { required: true, message: '请输入专业名称', trigger: 'blur' },
    { min: 2, max: 100, message: '长度在 2 到 100 个字符', trigger: 'blur' }
  ],
  specialized_part: [
    { required: false, validator: (rule, value, callback) => {
      if (!value || value.trim() === '') {
        callback();
      } else if (value.length < 2 || value.length > 100) {
        callback(new Error('长度在 2 到 100 个字符'));
      } else {
        callback();
      }
    }, trigger: 'blur' }
  ],
  specialized_degree: [
    { required: false, validator: (rule, value, callback) => {
      if (!value || value.trim() === '') {
        callback();
      } else if (value.length < 2 || value.length > 50) {
        callback(new Error('长度在 2 到 50 个字符'));
      } else {
        callback();
      }
    }, trigger: 'blur' }
  ],
  specialized_type: [
    { required: false, validator: (rule, value, callback) => {
      if (!value || value.trim() === '') {
        callback();
      } else if (value.length < 2 || value.length > 50) {
        callback(new Error('长度在 2 到 50 个字符'));
      } else {
        callback();
      }
    }, trigger: 'blur' }
  ],
  specialized_introduce: [
    { required: false, validator: (rule, value, callback) => {
      if (!value || value.trim() === '') {
        callback();
      } else if (value.length < 10 || value.length > 500) {
        callback(new Error('长度在 10 到 500 个字符'));
      } else {
        callback();
      }
    }, trigger: 'blur' }
  ],
  specialized_course: [
    { required: false, validator: (rule, value, callback) => {
      if (!value || value.trim() === '') {
        callback();
      } else if (value.length < 10 || value.length > 500) {
        callback(new Error('长度在 10 到 500 个字符'));
      } else {
        callback();
      }
    }, trigger: 'blur' }
  ],
  specialized_employment: [
    { required: false, validator: (rule, value, callback) => {
      if (!value || value.trim() === '') {
        callback();
      } else if (value.length < 10 || value.length > 500) {
        callback(new Error('长度在 10 到 500 个字符'));
      } else {
        callback();
      }
    }, trigger: 'blur' }
  ],
  specialized_image: [
    { required: false, validator: (rule, value, callback) => {
      if (!value || value === '') {
        callback();
      } else if (value.startsWith('http://') || value.startsWith('https://') || value.startsWith('/')) {
        callback();
      } else {
        callback(new Error('请输入有效的URL地址或相对路径'));
      }
    }, trigger: 'blur' }
  ]
}

// 获取列表数据
const getList = async () => {
  loading.value = true
  try {
    const response = await axios.post('/specialized/')
    if (response.code === 200) {
      tableData.value = response.result
      total.value = response.result.length
    } else {
      ElMessage.error(response.message || '获取数据失败')
    }
  } catch (error) {
    console.error(error)
    ElMessage.error('获取数据失败')
  } finally {
    loading.value = false
  }
}

// 添加
const handleAdd = () => {
  dialogType.value = 'add'
  form.value = {
    specialized_content: '',
    specialized_part: '',
    specialized_degree: '',
    specialized_type: '',
    specialized_introduce: '',
    specialized_course: '',
    specialized_employment: '',
    specialized_image: ''
  }
  dialogVisible.value = true
}

// 编辑
const handleEdit = async (row) => {
  dialogType.value = 'edit'
  
  try {
    console.log('正在获取专业详情，ID:', row.specialized_id)
    // 获取专业详情
    const response = await axios.post('/specialized/getSpecializedDetail', {
      specialized_id: row.specialized_id
    })
    
    console.log('获取专业详情响应:', response)
    
    if (response.code === 200 && response.result) {
      form.value = {
        specialized_id: response.result.specialized_id,
        specialized_content: response.result.specialized_content,
        specialized_part: response.result.specialized_part || '',
        specialized_degree: response.result.specialized_degree || '',
        specialized_type: response.result.specialized_type || '',
        specialized_introduce: response.result.specialized_introduce || '',
        specialized_course: response.result.specialized_course || '',
        specialized_employment: response.result.specialized_employment || '',
        specialized_image: response.result.specialized_image || ''
      }
    } else {
      // 如果没有详情数据，只填充基本信息
      form.value = { 
        specialized_id: row.specialized_id,
        specialized_content: row.specialized_content,
        specialized_part: '',
        specialized_degree: '',
        specialized_type: '',
        specialized_introduce: '',
        specialized_course: '',
        specialized_employment: '',
        specialized_image: ''
      }
    }
  } catch (error) {
    console.error('获取专业详情失败:', error)
    console.error('错误详情:', error.response?.data)
    ElMessage.warning('获取专业详情失败，将只能编辑基本信息')
    form.value = { 
      specialized_id: row.specialized_id,
      specialized_content: row.specialized_content,
      specialized_part: '',
      specialized_degree: '',
      specialized_type: '',
      specialized_introduce: '',
      specialized_course: '',
      specialized_employment: '',
      specialized_image: ''
    }
  }
  
  dialogVisible.value = true
}

// 查看详情
const handleViewDetail = (row) => {
  // 显示加载中状态
  detailData.value = {}
  detailDialogVisible.value = true
  detailLoading.value = true // 开始加载
  
  // 发起网络请求获取专业详情
  axios.post('/specialized/getSpecializedDetail', {
    specialized_id: row.specialized_id
  }).then(res => {
    if (res.code === 200 && res.result) {
      detailData.value = res.result
    } else {
      ElMessage.error(res.message || '获取专业详情失败')
    }
  }).catch(error => {
    console.error('获取专业详情失败:', error)
    ElMessage.error('获取专业详情失败')
  }).finally(() => {
    detailLoading.value = false // 结束加载
  })
}

// 删除
const handleDelete = (row) => {
  ElMessageBox.confirm('确认删除该专业类型吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      const response = await axios.post('/specialized/delete', {
        specialized_id: row.specialized_id
      })
      if (response.code === 200) {
        ElMessage.success('删除成功')
        getList()
      } else {
        ElMessage.error(response.message || '删除失败')
      }
    } catch (error) {
      console.error(error)
      ElMessage.error('删除失败')
    }
  })
}

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return
  await formRef.value.validate(async (valid) => {
    if (valid) {
      try {
        let response
        const submitData = {
          user_id: 1, // 这里可以从用户信息中获取
          specialized_content: form.value.specialized_content,
          specialized_part: form.value.specialized_part || '',
          specialized_degree: form.value.specialized_degree || '',
          specialized_type: form.value.specialized_type || '',
          specialized_introduce: form.value.specialized_introduce || '',
          specialized_course: form.value.specialized_course || '',
          specialized_employment: form.value.specialized_employment || '',
          specialized_image: form.value.specialized_image || ''
        }
        
        console.log('准备提交的数据:', submitData);
        
        // 设置较长的超时时间
        const config = { timeout: 10000 };
        
        if (dialogType.value === 'add') {
          try {
            response = await axios.post('/specialized/addWithDetail', submitData, config);
          } catch (submitError) {
            console.error('提交失败，详细错误:', submitError);
            // 如果是网络错误，尝试重试一次
            if (submitError.message && submitError.message.includes('Network Error')) {
              ElMessage.warning('网络连接不稳定，正在重试...');
              // 延迟1秒后重试
              await new Promise(resolve => setTimeout(resolve, 1000));
              response = await axios.post('/specialized/addWithDetail', submitData, config);
            } else {
              throw submitError; // 其他错误则继续抛出
            }
          }
        } else {
          try {
            submitData.specialized_id = form.value.specialized_id;
            response = await axios.post('/specialized/updateWithDetail', submitData, config);
          } catch (submitError) {
            console.error('提交失败，详细错误:', submitError);
            // 如果是网络错误，尝试重试一次
            if (submitError.message && submitError.message.includes('Network Error')) {
              ElMessage.warning('网络连接不稳定，正在重试...');
              // 延迟1秒后重试
              await new Promise(resolve => setTimeout(resolve, 1000));
              response = await axios.post('/specialized/updateWithDetail', submitData, config);
            } else {
              throw submitError; // 其他错误则继续抛出
            }
          }
        }
        
        console.log('服务器响应:', response);
        
        if (response && response.code === 200) {
          ElMessage.success(dialogType.value === 'add' ? '添加成功' : '编辑成功');
          dialogVisible.value = false;
          getList();
        } else {
          ElMessage.error((response && response.message) || (dialogType.value === 'add' ? '添加失败' : '编辑失败'));
        }
      } catch (error) {
        console.error('操作失败，错误详情:', error);
        let errorMsg = dialogType.value === 'add' ? '添加失败' : '编辑失败';
        
        if (error.response) {
          console.error('服务器响应:', error.response.data);
          errorMsg += ` - 服务器返回: ${error.response.status}`;
        } else if (error.request) {
          console.error('请求未收到响应');
          errorMsg += ' - 服务器未响应，请检查网络连接';
        } else {
          console.error('请求配置错误:', error.message);
          errorMsg += ` - ${error.message}`;
        }
        
        ElMessage.error(errorMsg);
      }
    }
  })
}

// 分页
const handleSizeChange = (val) => {
  pageSize.value = val
  // 这里可以实现服务端分页
}

const handleCurrentChange = (val) => {
  currentPage.value = val
  // 这里可以实现服务端分页
}

// 图片上传相关
import { baseURL } from '@/utils/request' // 导入baseURL

const uploadHeaders = ref({})

const beforeImageUpload = (rawFile) => {
  const isImage = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'].includes(rawFile.type)
  const isLt5M = rawFile.size / 1024 / 1024 < 5

  if (!isImage) {
    ElMessage.error('只能上传JPG/PNG/GIF/WEBP格式的图片!')
    return false
  }
  if (!isLt5M) {
    ElMessage.error('图片大小不能超过 5MB!')
    return false
  }
  
  console.log('准备上传文件:', rawFile.name, '大小:', rawFile.size, '类型:', rawFile.type)
  console.log('上传地址:', `${baseURL}/specialized/uploadImage`)
  console.log('请求头:', uploadHeaders.value)
  
  return true
}

const handleImageSuccess = (response) => {
  console.log('图片上传响应:', response);
  if (response.code === 200 && response.result) {
    let imageUrl = response.result.imageUrl || response.result;
    
    // 如果返回的是完整URL，提取相对路径部分
    if (imageUrl.startsWith('http')) {
      const url = new URL(imageUrl);
      imageUrl = url.pathname;
    }
    
    // 确保以 / 开头的相对路径
    if (!imageUrl.startsWith('/')) {
      imageUrl = '/' + imageUrl;
    }
    
    form.value.specialized_image = imageUrl;
    ElMessage.success('图片上传成功');
  } else {
    ElMessage.error(response.message || '图片上传失败');
  }
}

const handleImageError = (err, file) => {
  console.error('上传错误详情:', err);
  console.error('文件信息:', file);
  console.error('上传地址:', `${baseURL}/specialized/uploadImage`);
  console.error('请求头:', uploadHeaders.value);
  
  if (err.message && err.message.includes('Network Error')) {
    ElMessage.error('网络连接失败，请检查网络或服务器状态');
  } else {
    ElMessage.error('图片上传失败: ' + (err.message || '未知错误'));
  }
}

onMounted(() => {
  // 设置上传请求头
  const token = localStorage.getItem('token')
  console.log('当前Token:', token ? '已设置' : '未设置')
  
  if (token) {
    uploadHeaders.value = {
      Authorization: `Bearer ${token}`
    }
    console.log('已设置上传请求头:', uploadHeaders.value)
  } else {
    console.warn('未找到认证令牌，可能导致请求失败')
    // 尝试重定向到登录页或提示用户
    ElMessage.warning('您可能需要重新登录以获取完整功能')
  }
  
  // 获取专业类型列表
  getList().catch(err => {
    console.error('获取专业类型列表失败:', err)
    ElMessage.error('获取专业类型列表失败，请检查网络连接或重新登录')
  })
})

// 处理图片显示URL
const getImageDisplayUrl = (imagePath) => {
  if (!imagePath) return ''
  if (imagePath.startsWith('http')) {
    return imagePath
  }
  // 显示时使用完整URL
  return `${baseURL}${imagePath}`
}
</script>

<style scoped>
.specialized-container {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.pagination-container {
  margin-top: 20px;
  text-align: right;
}

/* 详情样式 */
.detail-container {
  padding: 10px;
}

.detail-title {
  text-align: center;
  margin-bottom: 20px;
  font-size: 22px;
  color: #303133;
}

.detail-descriptions {
  margin-bottom: 20px;
}

.detail-section {
  margin-bottom: 20px;
}

.detail-section h3 {
  font-size: 18px;
  color: #303133;
  margin-bottom: 10px;
  padding-bottom: 8px;
  border-bottom: 1px solid #ebeef5;
}

.detail-section p {
  line-height: 1.8;
  color: #606266;
  text-align: justify;
}

/* 图片预览样式 */
.image-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 200px; /* 确保有足够高度 */
  background-color: #f5f7fa; /* 背景色 */
  border-radius: 4px;
  overflow: hidden;
}

.specialized-image {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
}

/* 上传组件样式 */
.upload-container {
  display: flex;
  align-items: center;
  gap: 10px; /* 图片和输入框之间的间距 */
}

.image-uploader {
  width: 100px; /* 控制上传按钮的尺寸 */
  height: 100px;
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  background-color: #f5f7fa;
  display: flex;
  justify-content: center;
  align-items: center;
}

.image-uploader:hover {
  border-color: #409eff;
}

.image-uploader .el-icon {
  font-size: 28px;
  color: #8c939d;
}

.uploaded-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.upload-tip {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.upload-tip p {
  font-size: 12px;
  color: #909399;
  margin-bottom: 5px;
}

.upload-tip .el-input {
  width: 200px; /* 输入框宽度 */
}
</style> 

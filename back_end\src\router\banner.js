//轮播图接口

const express = require('express');
const router = express.Router();
const db = require('../config/db');
const multer = require('multer');
const path = require('path');

// 配置 multer 存储
const storage = multer.diskStorage({
    destination: function (req, file, cb) {
        cb(null, path.join(__dirname, '../../public/uploads/'));
    },
    filename: function (req, file, cb) {
        // 生成唯一的文件名
        const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
        cb(null, uniqueSuffix + path.extname(file.originalname));
    }
});

// 文件过滤器
const fileFilter = (req, file, cb) => {
    if (file.mimetype.startsWith('image/')) {
        cb(null, true);
    } else {
        cb(new Error('只允许上传图片文件！'), false);
    }
};

const upload = multer({ 
    storage: storage,
    fileFilter: fileFilter,
    limits: {
        fileSize: 5 * 1024 * 1024 // 限制文件大小为5MB
    }
});


router.post('/',(req,res)=>{
    const sql='select * from banner'
    db.query(sql,(err,result)=>{
        if(err){
            res.send({
                code:201,
                success:"失败"
            })
        }else{
            res.send({
                code:200,
                success:"成功",
                result:result
            })
        }
    })
})

router.post('/upload', upload.single('banner_image'), (req, res) => {
    const { banner_name } = req.body;
    
    // 如果没有提供banner_name，返回错误
    if (!banner_name) {
        return res.status(400).json({
            code: 201,
            success: "失败",
            message: "请提供轮播图名称"
        });
    }

    // 获取上传的图片路径，如果没有上传图片则为null
    const banner_image = req.file ? `/uploads/${req.file.filename}` : null;

    // 插入新轮播图
    const insertSql = `
        INSERT INTO banner (banner_name, banner_image) 
        VALUES (?, ?)
    `;
    
    const values = [banner_name, banner_image];
    
    db.query(insertSql, values, (err, result) => {
        if (err) {
            return res.status(500).json({
                code: 201,
                success: "失败",
                message: "轮播图添加失败",
                error: err.message
            });
        }
        
        res.status(200).json({
            code: 200,
            success: "成功",
            message: "轮播图添加成功",
            data: {
                banner_id: result.insertId,
                banner_name,
                banner_image
            }
        });
    });
});


//删除轮播图
router.post('/delete',(req,res)=>{
    const sql='delete from banner where banner_id=?'
    db.query(sql,[req.body.banner_id],(err,result)=>{
        if(err){
            res.send({
                code:201,
                message:'删除失败'
            })
        }else{
            res.send({
                code:200,
                message:'删除成功'
            })
        }
    })
})

// 修改轮播图
router.post('/update', upload.single('banner_image'), (req, res) => {
    const { banner_id, banner_name } = req.body;
    
    if (!banner_id) {
        return res.status(400).json({
            code: 201,
            success: "失败",
            message: "轮播图ID不能为空"
        });
    }

    const updateFields = [];
    const values = [];

    if (banner_name !== undefined) {
        updateFields.push('banner_name = ?');
        values.push(banner_name);
    }

    // 如果上传了新图片，添加图片路径
    if (req.file) {
        updateFields.push('banner_image = ?');
        values.push(`/uploads/${req.file.filename}`);
    }

    if (updateFields.length === 0) {
        return res.status(400).json({
            code: 201,
            success: "失败",
            message: "没有要更新的字段"
        });
    }

    values.push(banner_id);
    const sql = `UPDATE banner SET ${updateFields.join(', ')} WHERE banner_id = ?`;

    db.query(sql, values, (err, result) => {
        if (err) {
            return res.status(500).json({
                code: 201,
                success: "失败",
                message: "修改失败",
                error: err.message
            });
        }

        if (result.affectedRows === 0) {
            return res.status(404).json({
                code: 201,
                success: "失败",
                message: "未找到要修改的轮播图"
            });
        }

        res.json({
            code: 200,
            success: "成功",
            message: "修改成功"
        });
    });
});

module.exports = router;

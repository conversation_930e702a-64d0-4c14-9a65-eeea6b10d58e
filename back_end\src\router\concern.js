// 用户关注接口

const express = require('express');
const router = express.Router();
const db = require('../config/db');

// 获取用户关注列表（该用户关注的消息及其作者信息）
router.post('/getUserConcerns', (req, res) => {
    const { user_id } = req.body;
    
    if (!user_id) {
        return res.status(400).send({
            code: 201,
            success: "失败",
            message: "用户ID不能为空"
        });
    }
    
    const sql = `
        SELECT 
            c.concern_id,
            c.user_id,
            c.message_id,
            c.concern_data,
            m.user_id as target_user_id,
            u.nick_name,
            u.user_image,
            u.user_school,
            u.user_form,
            u.user_fraction,
            u.sex,
            u.birth,
            u.phone
        FROM concern c
        LEFT JOIN message m ON c.message_id = m.message_id
        LEFT JOIN user u ON m.user_id = u.user_id
        WHERE c.user_id = ?
        GROUP BY m.user_id, u.nick_name
        ORDER BY MAX(c.concern_data) DESC
    `;
    
    db.query(sql, [user_id], (err, result) => {
        if (err) {
            res.status(500).send({
                code: 201,
                success: "失败",
                message: "查询失败",
                error: err.message
            });
        } else {
            res.send({
                code: 200,
                success: "成功",
                message: "查询成功",
                result: result
            });
        }
    });
});

// 添加关注（关注某个消息，实际上是关注消息的作者）
router.post('/addConcern', (req, res) => {
    const { user_id, message_id } = req.body;
    
    if (!user_id || !message_id) {
        return res.status(400).send({
            code: 201,
            success: "失败",
            message: "用户ID和消息ID不能为空"
        });
    }
    
    // 先查询消息的发布者
    const getMessageUserSql = 'SELECT user_id FROM message WHERE message_id = ?';
    
    db.query(getMessageUserSql, [message_id], (err, messageResult) => {
        if (err) {
            return res.status(500).send({
                code: 201,
                success: "失败",
                message: "查询消息失败",
                error: err.message
            });
        }
        
        if (messageResult.length === 0) {
            return res.status(404).send({
                code: 201,
                success: "失败",
                message: "消息不存在"
            });
        }
        
        const messageUserId = messageResult[0].user_id;
        
        // 检查是否是自己关注自己
        if (parseInt(user_id) === parseInt(messageUserId)) {
            return res.status(400).send({
                code: 201,
                success: "失败",
                message: "不能关注自己发布的消息"
            });
        }
        
        // 检查是否已经关注
        const checkSql = 'SELECT * FROM concern WHERE user_id = ? AND message_id = ?';
        
        db.query(checkSql, [user_id, message_id], (err, result) => {
            if (err) {
                return res.status(500).send({
                    code: 201,
                    success: "失败",
                    message: "检查关注状态失败",
                    error: err.message
                });
            }
            
            if (result.length > 0) {
                return res.send({
                    code: 201,
                    success: "失败",
                    message: "已经关注过该消息"
                });
            }
            
            // 添加关注
            const insertSql = 'INSERT INTO concern (user_id, message_id, concern_data) VALUES (?, ?, NOW())';
            
            db.query(insertSql, [user_id, message_id], (err, result) => {
                if (err) {
                    res.status(500).send({
                        code: 201,
                        success: "失败",
                        message: "添加关注失败",
                        error: err.message
                    });
                } else {
                    res.send({
                        code: 200,
                        success: "成功",
                        message: "关注成功",
                        data: {
                            concern_id: result.insertId,
                            user_id,
                            message_id
                        }
                    });
                }
            });
        });
    });
});

// 取消关注
router.post('/removeConcern', (req, res) => {
    const { user_id, message_id } = req.body;
    
    if (!user_id || !message_id) {
        return res.status(400).send({
            code: 201,
            success: "失败",
            message: "用户ID和消息ID不能为空"
        });
    }
    
    const sql = 'DELETE FROM concern WHERE user_id = ? AND message_id = ?';
    
    db.query(sql, [user_id, message_id], (err, result) => {
        if (err) {
            res.status(500).send({
                code: 201,
                success: "失败",
                message: "取消关注失败",
                error: err.message
            });
        } else if (result.affectedRows === 0) {
            res.send({
                code: 201,
                success: "失败",
                message: "未找到关注记录"
            });
        } else {
            res.send({
                code: 200,
                success: "成功",
                message: "取消关注成功"
            });
        }
    });
});

// 检查关注状态
router.post('/checkConcern', (req, res) => {
    const { user_id, message_id } = req.body;
    
    if (!user_id || !message_id) {
        return res.status(400).send({
            code: 201,
            success: "失败",
            message: "用户ID和消息ID不能为空"
        });
    }
    
    const sql = 'SELECT * FROM concern WHERE user_id = ? AND message_id = ?';
    
    db.query(sql, [user_id, message_id], (err, result) => {
        if (err) {
            res.status(500).send({
                code: 201,
                success: "失败",
                message: "查询失败",
                error: err.message
            });
        } else {
            res.send({
                code: 200,
                success: "成功",
                message: "查询成功",
                isConcerned: result.length > 0
            });
        }
    });
});

// 获取用户粉丝列表（关注该用户消息的人）
router.post('/getUserFans', (req, res) => {
    const { user_id } = req.body;
    
    if (!user_id) {
        return res.status(400).send({
            code: 201,
            success: "失败",
            message: "用户ID不能为空"
        });
    }
    
    const sql = `
        SELECT 
            c.concern_id,
            c.user_id as fan_user_id,
            c.message_id,
            c.concern_data,
            u.nick_name as fan_nick_name,
            u.user_image as fan_user_image,
            u.user_school as fan_user_school,
            u.user_form as fan_user_form,
            u.user_fraction as fan_user_fraction,
            u.sex as fan_sex,
            u.birth as fan_birth,
            u.phone as fan_phone
        FROM concern c
        LEFT JOIN message m ON c.message_id = m.message_id
        LEFT JOIN user u ON c.user_id = u.user_id
        WHERE m.user_id = ?
        GROUP BY c.user_id, u.nick_name
        ORDER BY MAX(c.concern_data) DESC
    `;
    
    db.query(sql, [user_id], (err, result) => {
        if (err) {
            res.status(500).send({
                code: 201,
                success: "失败",
                message: "查询失败",
                error: err.message
            });
        } else {
            res.send({
                code: 200,
                success: "成功",
                message: "查询成功",
                result: result
            });
        }
    });
});

// 获取关注和粉丝统计（去重后的真实数量）
router.post('/getUserStats', (req, res) => {
    const { user_id } = req.body;
    
    if (!user_id) {
        return res.status(400).send({
            code: 201,
            success: "失败",
            message: "用户ID不能为空"
        });
    }
    
    // 获取关注数量（去重后的用户数量）
    const followingCountSql = `
        SELECT COUNT(DISTINCT m.user_id) as following_count 
        FROM concern c
        LEFT JOIN message m ON c.message_id = m.message_id
        WHERE c.user_id = ?
    `;
    
    // 获取粉丝数量（去重后的用户数量）
    const fansCountSql = `
        SELECT COUNT(DISTINCT c.user_id) as fans_count 
        FROM concern c
        LEFT JOIN message m ON c.message_id = m.message_id
        WHERE m.user_id = ?
    `;
    
    db.query(followingCountSql, [user_id], (err, followingResult) => {
        if (err) {
            return res.status(500).send({
                code: 201,
                success: "失败",
                message: "查询关注数量失败",
                error: err.message
            });
        }
        
        db.query(fansCountSql, [user_id], (err, fansResult) => {
            if (err) {
                return res.status(500).send({
                    code: 201,
                    success: "失败",
                    message: "查询粉丝数量失败",
                    error: err.message
                });
            }
            
            res.send({
                code: 200,
                success: "成功",
                message: "查询成功",
                data: {
                    following_count: followingResult[0].following_count,
                    fans_count: fansResult[0].fans_count
                }
            });
        });
    });
});

// 获取单个用户详细信息及其发布的消息
router.post('/getUserDetail', (req, res) => {
    const { user_id } = req.body;
    
    if (!user_id) {
        return res.status(400).send({
            code: 201,
            success: "失败",
            message: "用户ID不能为空"
        });
    }
    
    // 先查询用户基本信息
    const userSql = `
        SELECT 
            user_id,
            nick_name,
            user_image,
            user_school,
            sex,
            birth,
            phone
        FROM user 
        WHERE user_id = ?
    `;
    
    db.query(userSql, [user_id], (err, userResult) => {
        if (err) {
            return res.status(500).send({
                code: 201,
                success: "失败",
                message: "查询用户信息失败",
                error: err.message
            });
        }
        
        if (userResult.length === 0) {
            return res.status(404).send({
                code: 201,
                success: "失败",
                message: "用户不存在"
            });
        }
        
        // 再查询用户的消息
        const messageSql = `
            SELECT 
                message_id,
                message_content,
                message_image,
                message_imageone,
                message_imagetwo
            FROM message 
            WHERE user_id = ?
            ORDER BY message_id DESC
        `;
        
        db.query(messageSql, [user_id], (err, messageResult) => {
            if (err) {
                return res.status(500).send({
                    code: 201,
                    success: "失败",
                    message: "查询消息失败",
                    error: err.message
                });
            }
            
            // 处理消息数据
            const messages = messageResult.map(item => ({
                message_id: item.message_id,
                message_content: item.message_content,
                message_image: item.message_image,
                message_imageone: item.message_imageone,
                message_imagetwo: item.message_imagetwo
            }));
            
            res.send({
                code: 200,
                success: "成功",
                message: "查询成功",
                result: {
                    userInfo: userResult[0],
                    messages: messages
                }
            });
        });
    });
});

// 检查是否关注某个用户
router.post('/checkUserFollow', (req, res) => {
    const { user_id, target_user_id } = req.body;
    
    if (!user_id || !target_user_id) {
        return res.status(400).send({
            code: 201,
            success: "失败",
            message: "用户ID不能为空"
        });
    }
    
    // 检查是否关注了该用户的任何消息
    const sql = `
        SELECT COUNT(*) as count
        FROM concern c
        LEFT JOIN message m ON c.message_id = m.message_id
        WHERE c.user_id = ? AND m.user_id = ?
    `;
    
    db.query(sql, [user_id, target_user_id], (err, result) => {
        if (err) {
            res.status(500).send({
                code: 201,
                success: "失败",
                message: "查询失败",
                error: err.message
            });
        } else {
            res.send({
                code: 200,
                success: "成功",
                message: "查询成功",
                isFollowed: result[0].count > 0
            });
        }
    });
});

// 关注用户（通过关注其最新消息）
router.post('/followUser', (req, res) => {
    const { user_id, target_user_id } = req.body;
    
    if (!user_id || !target_user_id) {
        return res.status(400).send({
            code: 201,
            success: "失败",
            message: "用户ID不能为空"
        });
    }
    
    // 检查是否是自己
    if (parseInt(user_id) === parseInt(target_user_id)) {
        return res.status(400).send({
            code: 201,
            success: "失败",
            message: "不能关注自己"
        });
    }
    
    // 获取目标用户的最新消息
    const getLatestMessageSql = `
        SELECT message_id 
        FROM message 
        WHERE user_id = ? 
        ORDER BY message_id DESC 
        LIMIT 1
    `;
    
    db.query(getLatestMessageSql, [target_user_id], (err, messageResult) => {
        if (err) {
            return res.status(500).send({
                code: 201,
                success: "失败",
                message: "查询用户消息失败",
                error: err.message
            });
        }
        
        if (messageResult.length === 0) {
            return res.status(404).send({
                code: 201,
                success: "失败",
                message: "该用户暂无消息，无法关注"
            });
        }
        
        const messageId = messageResult[0].message_id;
        
        // 检查是否已经关注
        const checkSql = 'SELECT * FROM concern WHERE user_id = ? AND message_id = ?';
        
        db.query(checkSql, [user_id, messageId], (err, result) => {
            if (err) {
                return res.status(500).send({
                    code: 201,
                    success: "失败",
                    message: "检查关注状态失败",
                    error: err.message
                });
            }
            
            if (result.length > 0) {
                return res.send({
                    code: 200,
                    success: "成功",
                    message: "已经关注过该用户"
                });
            }
            
            // 添加关注
            const insertSql = 'INSERT INTO concern (user_id, message_id, concern_data) VALUES (?, ?, NOW())';
            
            db.query(insertSql, [user_id, messageId], (err, result) => {
                if (err) {
                    res.status(500).send({
                        code: 201,
                        success: "失败",
                        message: "关注失败",
                        error: err.message
                    });
                } else {
                    res.send({
                        code: 200,
                        success: "成功",
                        message: "关注成功"
                    });
                }
            });
        });
    });
});

// 取消关注用户（取消关注其所有消息）
router.post('/unfollowUser', (req, res) => {
    const { user_id, target_user_id } = req.body;
    
    if (!user_id || !target_user_id) {
        return res.status(400).send({
            code: 201,
            success: "失败",
            message: "用户ID不能为空"
        });
    }
    
    // 删除对该用户所有消息的关注
    const sql = `
        DELETE c FROM concern c
        LEFT JOIN message m ON c.message_id = m.message_id
        WHERE c.user_id = ? AND m.user_id = ?
    `;
    
    db.query(sql, [user_id, target_user_id], (err, result) => {
        if (err) {
            res.status(500).send({
                code: 201,
                success: "失败",
                message: "取消关注失败",
                error: err.message
            });
        } else {
            res.send({
                code: 200,
                success: "成功",
                message: "取消关注成功"
            });
        }
    });
});

module.exports = router;

//学校接口


const express = require('express');
const router = express.Router();
const db = require('../config/db');
const multer = require('multer');
const path = require('path');

// 配置 multer 存储
const storage = multer.diskStorage({
    destination: function (req, file, cb) {
        // 根据文件类型选择不同的存储目录
        if (file.mimetype.startsWith('image/')) {
            cb(null, path.join(__dirname, '../../public/uploads/'));
        } else if (file.mimetype.startsWith('video/')) {
            cb(null, path.join(__dirname, '../../public/uploads/videos/'));
        } else {
            cb(null, path.join(__dirname, '../../public/uploads/'));
        }
    },
    filename: function (req, file, cb) {
        // 生成唯一的文件名
        const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
        cb(null, uniqueSuffix + path.extname(file.originalname));
    }
});

// 文件过滤器
const fileFilter = (req, file, cb) => {
    if (file.mimetype.startsWith('image/') || file.mimetype.startsWith('video/')) {
        cb(null, true);
    } else {
        cb(new Error('只允许上传图片或视频文件！'), false);
    }
};

const upload = multer({ 
    storage: storage,
    fileFilter: fileFilter,
    limits: {
        fileSize: 50 * 1024 * 1024 // 限制文件大小为50MB（视频文件较大）
    }
});


router.post('/',(req,res)=>{
    const sql='select * from school'
    db.query(sql,(err,result)=>{
        if(err){
            res.send({
                code:201,
                success:"失败"
            })
        }else{
            res.send({
                code:200,
                success:"成功",
                result:result
            })
        }
    })
})

router.post('/login', (req, res) => {
    // 登录逻辑
    const { school_account, school_password } = req.body;
    const sql = `select * from school where school_account='${school_account}' and school_password='${school_password}'`;
    db.query(sql, (err, result) => {
        if (err) {
            res.send({
                code: 201,
                success: "失败"
            });
        } else if (result.length === 0) {
            res.send({
                code: 201,
                success: "失败"
            });
        } else {
            res.send({
                code: 200,
                success: "成功",
                result: result[0]
            });
        }
    });
});

//新增学校
router.post('/register', upload.fields([
    { name: 'school_image', maxCount: 1 },
    { name: 'school_video', maxCount: 1 }
]), (req, res) => {
    const { 
        school_account, 
        school_password, 
        school_name, 
        school_phone, 
        school_address, 
        school_characteristic, 
        school_idea, 
        identity,
        school_email,
        school_number
    } = req.body;
     
    // 检查学校账号是否已存在
    const checkSchoolSql = 'SELECT * FROM school WHERE school_account = ?';
    db.query(checkSchoolSql, [school_account], (err, result) => {
        if (err) {
            return res.status(500).json({
                code: 201,
                success: "失败",
                message: "服务器错误"
            });
        }
        
        if (result.length > 0) {
            return res.status(400).json({
                code: 201,
                success: "失败",
                message: "该学校账号已存在"
            });
        }

        // 获取上传的文件路径
        const school_image = req.files && req.files['school_image'] ? `/uploads/${req.files['school_image'][0].filename}` : null;
        const school_video = req.files && req.files['school_video'] ? `/uploads/videos/${req.files['school_video'][0].filename}` : null;

        // 插入新学校
        const insertSql = `
            INSERT INTO school (
                school_account, 
                school_password, 
                school_name, 
                school_image, 
                school_video,
                school_phone, 
                school_address, 
                school_characteristic, 
                school_idea, 
                identity, 
                school_email,
                school_number
            ) 
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        `;
        
        const values = [
            school_account,
            school_password,
            school_name,
            school_image,
            school_video,
            school_phone,
            school_address,
            school_characteristic,
            school_idea,
            identity,
            school_email,
            school_number
        ];
        
        db.query(insertSql, values, (err, result) => {
            if (err) {
                return res.status(500).json({
                    code: 201,
                    success: "失败",
                    message: "注册失败",
                    error: err.message
                });
            }
            
            res.status(200).json({
                code: 200,
                success: "成功",
                message: "注册成功",
                data: {
                    school_id: result.insertId,
                    school_account,
                    school_name,
                    school_image,
                    school_video,
                    school_phone,
                    school_address,
                    school_characteristic,
                    school_idea,
                    identity,
                    school_email,
                    school_number
                }
            });
        });
    });
});


//注销学校信息
router.post('/delete',(req,res)=>{
    const sql='delete from school where school_id=?'
    db.query(sql,[req.body.school_id],(err,result)=>{
        if(err){
            res.send({
                code:201,
                message:'注销失败'
            })
        }else{
            res.send({
                code:200,
                message:'注销成功'
            })
        }
    })
})

// 添加更新学校信息的接口
router.post('/update', upload.fields([
    { name: 'school_image', maxCount: 1 },
    { name: 'school_video', maxCount: 1 }
]), (req, res) => {
    const { 
        school_id,
        school_account, 
        school_password, 
        school_name, 
        school_phone, 
        school_address, 
        school_characteristic, 
        school_idea, 
        identity,
        school_email,
        school_number
    } = req.body;

    if (!school_id) {
        return res.status(400).json({
            code: 201,
            success: "失败",
            message: "学校ID不能为空"
        });
    }

    // 构建更新数据
    const updateData = {
        school_account,
        school_password,
        school_name,
        school_phone,
        school_address,
        school_characteristic,
        school_idea,
        identity,
        school_email,
        school_number
    };

    // 如果上传了新图片，添加图片路径
    if (req.files && req.files['school_image']) {
        updateData.school_image = `/uploads/${req.files['school_image'][0].filename}`;
    }

    // 如果上传了新视频，添加视频路径
    if (req.files && req.files['school_video']) {
        updateData.school_video = `/uploads/videos/${req.files['school_video'][0].filename}`;
    }

    // 构建SQL语句
    const updateFields = [];
    const values = [];
    
    Object.keys(updateData).forEach(key => {
        if (updateData[key] !== undefined && updateData[key] !== null) {
            updateFields.push(`${key} = ?`);
            values.push(updateData[key]);
        }
    });

    if (updateFields.length === 0) {
        return res.status(400).json({
            code: 201,
            success: "失败",
            message: "没有要更新的数据"
        });
    }

    values.push(school_id);
    const updateSql = `UPDATE school SET ${updateFields.join(', ')} WHERE school_id = ?`;

    db.query(updateSql, values, (err, result) => {
        if (err) {
            return res.status(500).json({
                code: 201,
                success: "失败",
                message: "更新失败",
                error: err.message
            });
        }

        if (result.affectedRows === 0) {
            return res.status(404).json({
                code: 201,
                success: "失败",
                message: "学校不存在"
            });
        }

        res.json({
            code: 200,
            success: "成功",
            message: "更新成功",
            data: updateData
        });
    });
});

// 获取学校详情接口（包含专业信息）
router.post('/detail', (req, res) => {
    const { school_id } = req.body;
    
    if (!school_id) {
        return res.status(400).json({
            code: 201,
            success: "失败",
            message: "学校ID不能为空"
        });
    }

    // 查询学校基本信息
    const schoolSql = 'SELECT * FROM school WHERE school_id = ?';
    
    db.query(schoolSql, [school_id], (err, schoolResult) => {
        if (err) {
            return res.status(500).json({
                code: 201,
                success: "失败",
                message: "查询失败",
                error: err.message
            });
        }

        if (schoolResult.length === 0) {
            return res.status(404).json({
                code: 201,
                success: "失败",
                message: "学校不存在"
            });
        }

        // 查询该学校的所有专业（包含分数信息）
        const specializedSql = `
            SELECT 
                s.specialized_id,
                s.specialized_content,
                sd.specialized_part,
                sd.specialized_degree,
                sd.specialized_type,
                sd.specialized_introduce,
                sd.specialized_course,
                sd.specialized_employment,
                sd.specialized_image,
                sc.min_scores,
                sc.max_scores,
                sc.year
            FROM school_specialized ss
            LEFT JOIN specialized s ON ss.specialized_id = s.specialized_id
            LEFT JOIN specialized_detail sd ON s.specialized_id = sd.specialized_id
            LEFT JOIN school_scores sc ON sc.school_id = ? AND sc.specialized_id = s.specialized_id
            WHERE ss.school_id = ?
            ORDER BY s.specialized_content ASC
        `;

        db.query(specializedSql, [school_id, school_id], (specializedErr, specializedResult) => {
            if (specializedErr) {
                console.error('查询专业信息错误:', specializedErr);
                // 即使专业查询失败，也返回学校基本信息
                return res.json({
                    code: 200,
                    success: "成功",
                    message: "查询成功（专业信息查询失败）",
                    result: {
                        ...schoolResult[0],
                        specializedList: []
                    }
                });
            }

            res.json({
                code: 200,
                success: "成功",
                message: "查询成功",
                result: {
                    ...schoolResult[0],
                    specializedList: specializedResult || []
                }
            });
        });
    });
});

// 学校添加专业接口
router.post('/addSpecialized', (req, res) => {
    const { 
        school_id, 
        specialized_id, 
        min_scores, 
        max_scores, 
        year 
    } = req.body;
    
    // 参数验证
    if (!school_id || !specialized_id || !min_scores) {
        return res.status(400).json({
            code: 201,
            success: "失败",
            message: "学校ID、专业ID和最低分数不能为空"
        });
    }
    
    // 设置默认年份为当前年份
    const currentYear = year || new Date().getFullYear();
    
    // 1. 检查学校专业关联是否已存在
    const checkSchoolSpecializedSql = 'SELECT * FROM school_specialized WHERE school_id = ? AND specialized_id = ?';
    db.query(checkSchoolSpecializedSql, [school_id, specialized_id], (err, checkResult) => {
        if (err) {
            return res.status(500).json({
                code: 201,
                success: "失败",
                message: "查询学校专业关联失败",
                error: err.message
            });
        }
        
        // 2. 插入学校专业关联的函数
        const insertSchoolSpecialized = (callback) => {
            if (checkResult.length === 0) {
                console.log('学校专业关联不存在，正在插入...');
                const insertSchoolSpecializedSql = 'INSERT INTO school_specialized (school_id, specialized_id) VALUES (?, ?)';
                db.query(insertSchoolSpecializedSql, [school_id, specialized_id], (err, result) => {
                    if (err) {
                        console.error('插入学校专业关联失败:', err);
                        return callback(err);
                    }
                    console.log('学校专业关联插入成功:', result);
                    callback(null, { insertId: result.insertId });
                });
            } else {
                console.log('学校专业关联已存在');
                callback(null, { insertId: checkResult[0].id || checkResult[0].school_specialized_id });
            }
        };
        
        // 3. 执行插入学校专业关联
        insertSchoolSpecialized((err, schoolSpecializedResult) => {
            if (err) {
                return res.status(500).json({
                    code: 201,
                    success: "失败",
                    message: "插入学校专业关联失败",
                    error: err.message
                });
            }
            
            // 4. 检查分数记录是否已存在
            const checkScoresSql = 'SELECT * FROM school_scores WHERE school_id = ? AND specialized_id = ? AND year = ?';
            db.query(checkScoresSql, [school_id, specialized_id, currentYear], (err, scoresCheckResult) => {
                if (err) {
                    return res.status(500).json({
                        code: 201,
                        success: "失败",
                        message: "查询分数记录失败",
                        error: err.message
                    });
                }
                
                if (scoresCheckResult.length > 0) {
                    return res.status(400).json({
                        code: 201,
                        success: "失败",
                        message: `该学校在${currentYear}年的该专业分数记录已存在`
                    });
                }
                
                // 5. 插入分数记录
                const insertScoresSql = `
                    INSERT INTO school_scores (school_id, specialized_id, min_scores, max_scores, year) 
                    VALUES (?, ?, ?, ?, ?)
                `;
                
                // 将年份转换为日期格式 (YYYY-01-01)
                const yearDate = `${currentYear}-01-01`;

                db.query(insertScoresSql, [
                    school_id, 
                    specialized_id, 
                    min_scores, 
                    max_scores || null, 
                    yearDate  // 使用日期格式
                ], (err, scoresResult) => {
                    if (err) {
                        return res.status(500).json({
                            code: 201,
                            success: "失败",
                            message: "插入分数记录失败",
                            error: err.message
                        });
                    }
                    
                    console.log('分数记录插入成功:', scoresResult);
                    
                    res.json({
                        code: 200,
                        success: "成功",
                        message: "学校专业添加成功",
                        data: {
                            school_id,
                            specialized_id,
                            min_scores,
                            max_scores: max_scores || null,
                            year: currentYear,  // 返回原始年份数字
                            school_specialized_id: schoolSpecializedResult.insertId,
                            scores_id: scoresResult.insertId
                        }
                    });
                });
            });
        });
    });
});

// 删除学校专业关联接口
router.post('/removeSpecialized', (req, res) => {
    const { school_id, specialized_id } = req.body;
    
    // 参数验证
    if (!school_id || !specialized_id) {
        return res.status(400).json({
            code: 201,
            success: "失败",
            message: "学校ID和专业ID不能为空"
        });
    }
    
    // 开始事务，确保两个表的数据同时删除
    db.query('START TRANSACTION', (err) => {
        if (err) {
            return res.status(500).json({
                code: 201,
                success: "失败",
                message: "事务开始失败",
                error: err.message
            });
        }
        
        // 1. 删除 school_scores 表中的记录
        const deleteScoresSql = 'DELETE FROM school_scores WHERE school_id = ? AND specialized_id = ?';
        db.query(deleteScoresSql, [school_id, specialized_id], (scoresErr, scoresResult) => {
            if (scoresErr) {
                // 回滚事务
                db.query('ROLLBACK', () => {
                    res.status(500).json({
                        code: 201,
                        success: "失败",
                        message: "删除分数记录失败",
                        error: scoresErr.message
                    });
                });
                return;
            }
            
            // 2. 删除 school_specialized 表中的记录
            const deleteSpecializedSql = 'DELETE FROM school_specialized WHERE school_id = ? AND specialized_id = ?';
            db.query(deleteSpecializedSql, [school_id, specialized_id], (specializedErr, specializedResult) => {
                if (specializedErr) {
                    // 回滚事务
                    db.query('ROLLBACK', () => {
                        res.status(500).json({
                            code: 201,
                            success: "失败",
                            message: "删除专业关联失败",
                            error: specializedErr.message
                        });
                    });
                    return;
                }
                
                // 检查是否有记录被删除
                if (specializedResult.affectedRows === 0) {
                    // 回滚事务
                    db.query('ROLLBACK', () => {
                        res.status(404).json({
                            code: 201,
                            success: "失败",
                            message: "未找到该学校专业关联记录"
                        });
                    });
                    return;
                }
                
                // 提交事务
                db.query('COMMIT', (commitErr) => {
                    if (commitErr) {
                        res.status(500).json({
                            code: 201,
                            success: "失败",
                            message: "事务提交失败",
                            error: commitErr.message
                        });
                        return;
                    }
                    
                    res.json({
                        code: 200,
                        success: "成功",
                        message: "删除学校专业关联成功",
                        data: {
                            school_id,
                            specialized_id,
                            scores_deleted: scoresResult.affectedRows,
                            specialized_deleted: specializedResult.affectedRows
                        }
                    });
                });
            });
        });
    });
});

module.exports = router;

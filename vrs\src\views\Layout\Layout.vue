<template>
  <div class="common-layout">
    <el-header class="header">
      <div class="header-left">
        <el-icon class="collapse-btn" @click="toggleCollapse">
          <Fold v-if="!isCollapse" />
          <Expand v-else />
        </el-icon>
        <img src="../../assets/4.png" alt="" class="logo">
        <div class="title-container">
          <h1 class="title">高考志愿填报系统</h1>
          <span class="subtitle">管理后台 v1.0</span>
        </div>
      </div>
      <div class="header-right">
        <div class="header-actions">
          <el-tooltip content="全屏" placement="bottom">
            <div class="action-item" @click="toggleFullScreen">
              <el-icon><FullScreen /></el-icon>
            </div>
          </el-tooltip>
          <el-tooltip content="消息中心" placement="bottom">
            <div class="action-item" @click="showMessages">
              <el-badge :value="3" class="notice-badge">
                <el-icon><Bell /></el-icon>
              </el-badge>
            </div>
          </el-tooltip>
          <el-tooltip :content="currentTheme === 'dark' ? '切换到明亮模式' : '切换到暗黑模式'" placement="bottom">
            <div class="action-item" @click="handleThemeChange">
              <el-icon>
                <Moon v-if="currentTheme === 'light'" />
                <Sunny v-else />
              </el-icon>
            </div>
          </el-tooltip>
        </div>
        <el-divider direction="vertical" />
        <div class="user-info">
          <img :src="userAvatar" class="user-avatar" alt="用户头像">
          <div class="user-detail">
            <span class="welcome">{{ username }}</span>
            <el-dropdown trigger="click" @command="handleCommand">
              <span class="role-name">
                管理员 <el-icon><CaretBottom /></el-icon>
              </span>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="profile">
                    <el-icon><User /></el-icon>个人信息
                  </el-dropdown-item>
                  <el-dropdown-item command="settings">
                    <el-icon><Setting /></el-icon>系统设置
                  </el-dropdown-item>
                  <el-dropdown-item divided command="logout">
                    <el-icon><SwitchButton /></el-icon>退出登录
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
        </div>
      </div>
    </el-header>
    
    <el-container>
      <el-aside :width="isCollapse ? '64px' : '240px'" class="aside">
        <el-menu
          :collapse="isCollapse"
          background-color="#001529"
          text-color="rgba(255,255,255,0.65)"
          active-text-color="#fff"
          :default-active="activeMenu"
          class="custom-menu">
          <div class="menu-header" v-if="!isCollapse">
            <el-icon><Menu /></el-icon>
            <span>功能导航</span>
          </div>

          <el-menu-item index="dashboard" @click="handleSelect('dashboard')">
            <el-icon><DataLine /></el-icon>
            <template #title>控制台</template>
          </el-menu-item>

          <el-sub-menu index="foundation">
            <template #title>
              <el-icon><Tools /></el-icon>
              <span>基础管理</span>
            </template>
            <el-menu-item index="foundation-banner" @click="handleSelect('foundation-banner')">轮播图</el-menu-item>
            <el-menu-item index="foundation-suggestion" @click="handleSelect('foundation-suggestion')">反馈建议</el-menu-item>
            <el-menu-item index="foundation-specialized" @click="handleSelect('foundation-specialized')">专业类型</el-menu-item>
            <el-menu-item index="foundation-province" @click="handleSelect('foundation-province')">省份</el-menu-item>
            <el-menu-item index="foundation-notice" @click="handleSelect('foundation-notice')">公告消息</el-menu-item>
          </el-sub-menu>
          
          <el-sub-menu index="admission">
            <template #title>
              <el-icon><School /></el-icon>
              <span>志愿管理</span>
            </template>
            <el-menu-item index="admission-list" @click="handleSelect('admission-list')">志愿列表</el-menu-item>
            <el-menu-item index="admission-review" @click="handleSelect('admission-review')">志愿审核</el-menu-item>
          </el-sub-menu>

          <el-sub-menu index="school">
            <template #title>
              <el-icon><House /></el-icon>
              <span>院校管理</span>
            </template>
            <el-menu-item index="school-list" @click="handleSelect('school-list')">院校列表</el-menu-item>
            <el-menu-item index="school-major" @click="handleSelect('school-major')">专业管理</el-menu-item>
          </el-sub-menu>

          <el-sub-menu index="user">
            <template #title>
              <el-icon><User /></el-icon>
              <span>用户管理</span>
            </template>
            <el-menu-item index="user-list" @click="handleSelect('user-list')">考生列表</el-menu-item>
            <el-menu-item index="user-role" @click="handleSelect('user-role')">角色管理</el-menu-item>
          </el-sub-menu>

          <el-sub-menu index="system">
            <template #title>
              <el-icon><Setting /></el-icon>
              <span>系统设置</span>
            </template>
            <el-menu-item index="system-config" @click="handleSelect('system-config')">基础配置</el-menu-item>
            <el-menu-item index="system-log" @click="handleSelect('system-log')">操作日志</el-menu-item>
          </el-sub-menu>
        </el-menu>
      </el-aside>

      <el-main>
        <div class="breadcrumb">
          <el-breadcrumb>
            <el-breadcrumb-item>首页</el-breadcrumb-item>
            <el-breadcrumb-item>{{ currentMenuTitle }}</el-breadcrumb-item>
          </el-breadcrumb>
        </div>
        <router-view v-slot="{ Component }">
          <transition name="fade" mode="out-in">
            <component :is="Component" />
          </transition>
        </router-view>
      </el-main>
    </el-container>
    
    <!-- 消息弹窗 -->
    <el-dialog
      v-model="messageDialogVisible"
      title="我的消息"
      width="600px"
      :before-close="handleCloseMessage"
    >
      <div v-loading="messageLoading" class="message-container">
        <div v-if="messages.length === 0" class="empty-message">
          暂无消息
        </div>
        <div v-else class="message-list">
          <div 
            v-for="(message, index) in messages" 
            :key="index"
            class="message-item"
          >
            <div class="message-header">
              <span class="user-name">{{ message.nick_name || '匿名用户' }}</span>
              <span class="message-time">{{ formatTime(message.create_time) }}</span>
            </div>
            <div class="message-content">
              {{ message.message_content }}
            </div>
          </div>
        </div>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="messageDialogVisible = false">关闭</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { ref, onMounted, computed, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import { 
  DataLine, House, School, User, Setting, Bell, CaretBottom, Menu,
  Fold, Expand, FullScreen, SwitchButton, Moon, Sunny, Tools
} from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { getCurrentTheme, toggleTheme, applyTheme } from '@/utils/theme'
import { baseURL } from '@/utils/request' // 导入基础URL
import { post } from '@/utils/request'

export default {
  name: "Layout",
  components: {
    DataLine, House, School, User, Setting, Bell, CaretBottom, Menu,
    Fold, Expand, FullScreen, SwitchButton, Moon, Sunny, Tools
  },
  setup() {
    const router = useRouter()
    const username = ref('')
    const userAvatar = ref('')
    const activeMenu = ref('dashboard')
    const isCollapse = ref(false)
    const currentTheme = ref(getCurrentTheme())

    // 计算当前菜单标题
    const currentMenuTitle = computed(() => {
      const menuMap = {
        'dashboard': '控制台',
        'foundation-banner': '轮播图',
        'foundation-suggestion': '反馈建议',
        'foundation-specialized': '专业类型',
        'foundation-province': '省份',
        'foundation-notice': '公告消息',
        'admission-list': '志愿列表',
        'admission-review': '志愿审核',
        'school-list': '院校列表',
        'school-major': '专业管理',
        'user-list': '考生列表',
        'user-role': '角色管理',
        'system-config': '基础配置',
        'system-log': '操作日志'
      }
      return menuMap[activeMenu.value] || '控制台'
    })

    const getImageUrl = (path) => {
      if (!path) return ''
      if (path.startsWith('http')) {
        return path
      }
      // 使用导入的基础URL
      return `${baseURL}${path}`
    }

    onMounted(() => {
      const adminInfo = JSON.parse(localStorage.getItem('adminInfo') || '{}')
      username.value = adminInfo.administrator_name || '管理员'
      userAvatar.value = getImageUrl(adminInfo.administrator_image) || ''
      // 应用保存的主题
      applyTheme(currentTheme.value)
      
      // 监听 storage 事件，用于在其他页面更新管理员信息时刷新头像
      window.addEventListener('storage', handleStorageChange)
    })

    const handleSelect = (key) => {
      activeMenu.value = key
      const routeMap = {
        'dashboard': '/dashboard',
        'foundation-banner': '/foundation/banner',
        'foundation-suggestion': '/foundation/suggestion',
        'foundation-specialized': '/foundation/specialized',
        'foundation-province': '/foundation/province',
        'foundation-notice': '/foundation/notice',
        'admission-list': '/admission/list',
        'admission-review': '/admission/review',
        'school-list': '/school/list',
        'school-major': '/school/major',
        'user-list': '/user/list',
        'user-role': '/user/role',
        'system-config': '/system/config',
        'system-log': '/system/log'
      }
      router.push(routeMap[key])
    }

    const handleCommand = (command) => {
      if (command === 'logout') {
        localStorage.removeItem('adminInfo')
        localStorage.removeItem('token')
        ElMessage.success('退出登录成功')
        router.push("/login")
      } else if (command === 'profile') {
        router.push('/profile')
      } else if (command === 'settings') {
        router.push('/settings')
      }
    }

    const toggleCollapse = () => {
      isCollapse.value = !isCollapse.value
    }

    const toggleFullScreen = () => {
      if (!document.fullscreenElement) {
        document.documentElement.requestFullscreen()
      } else {
        document.exitFullscreen()
      }
    }

    // 切换主题
    const handleThemeChange = () => {
      currentTheme.value = toggleTheme()
      ElMessage.success(`已切换到${currentTheme.value === 'dark' ? '暗黑' : '明亮'}主题`)
    }

    // 添加 storage 事件处理函数
    const handleStorageChange = () => {
      const adminInfo = JSON.parse(localStorage.getItem('adminInfo') || '{}')
      username.value = adminInfo.administrator_name || '管理员'
      userAvatar.value = getImageUrl(adminInfo.administrator_image) || ''
    }

    // 在组件卸载时移除事件监听
    onUnmounted(() => {
      window.removeEventListener('storage', handleStorageChange)
    })

    const messageDialogVisible = ref(false)
    const messageLoading = ref(false)
    const messages = ref([])

    // 显示消息
    const showMessages = async () => {
      messageDialogVisible.value = true
      messageLoading.value = true
      
      try {
        const res = await post('/message/', {})
        if (res.code === 200) {
          messages.value = res.result || []
        } else {
          ElMessage.error('获取消息失败')
        }
      } catch (error) {
        console.error('获取消息失败:', error)
        ElMessage.error('获取消息失败')
      } finally {
        messageLoading.value = false
      }
    }

    // 关闭消息弹窗
    const handleCloseMessage = () => {
      messageDialogVisible.value = false
      messages.value = []
    }

    // 格式化时间
    const formatTime = (timeString) => {
      if (!timeString) return ''
      const date = new Date(timeString)
      return date.toLocaleString()
    }

    return {
      username,
      userAvatar,
      activeMenu,
      isCollapse,
      currentTheme,
      currentMenuTitle,
      handleSelect,
      handleCommand,
      toggleCollapse,
      toggleFullScreen,
      handleThemeChange,
      getImageUrl,
      messageDialogVisible,
      messageLoading,
      messages,
      showMessages,
      handleCloseMessage,
      formatTime
    }
  }
}
</script>

<style scoped>
.header {
  background: var(--header-bg);
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 24px;
  height: 64px;
  box-shadow: var(--shadow);
  position: relative;
  z-index: 10;
  transition: all 0.3s;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.collapse-btn {
  font-size: 20px;
  cursor: pointer;
  color: var(--text-secondary);
  transition: all 0.3s;
}

.collapse-btn:hover {
  color: var(--menu-active);
}

.title-container {
  margin-left: 12px;
}

.title {
  color: var(--text-color);
  font-size: 18px;
  margin: 0;
  font-weight: 600;
}

.subtitle {
  color: var(--text-secondary);
  font-size: 12px;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 24px;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 16px;
}



.action-item:hover {
  color: var(--menu-active);
  background: var(--hover-bg);
}

.notice-badge {
  cursor: pointer;
  font-size: 18px;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 12px;
  cursor: pointer;
  padding: 4px 8px;
  border-radius: 4px;
  transition: all 0.3s;
}

.user-info:hover {
  background: var(--hover-bg);
}

.user-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  object-fit: cover;
  border: 2px solid #fff;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.user-detail {
  display: flex;
  flex-direction: column;
}

.welcome {
  color: var(--text-color);
  font-size: 14px;
  font-weight: 500;
}

.role-name {
  color: var(--text-secondary);
  font-size: 12px;
  display: flex;
  align-items: center;
  gap: 4px;
}

.aside {
  background: var(--menu-bg);
  height: calc(100vh - 64px);
  box-shadow: var(--shadow);
  position: relative;
  z-index: 9;
  transition: width 0.3s;
}

.menu-header {
  height: 48px;
  display: flex;
  align-items: center;
  padding: 0 20px;
  color: var(--menu-text);
  font-size: 13px;
  border-bottom: 1px solid var(--border-color);
}

.menu-header .el-icon {
  margin-right: 8px;
}

.custom-menu {
  border-right: none;
}

.custom-menu :deep(.el-menu-item) {
  height: 48px;
  line-height: 48px;
}

.custom-menu :deep(.el-sub-menu__title) {
  height: 48px;
  line-height: 48px;
}

.custom-menu :deep(.el-menu-item.is-active) {
  background: #1890ff !important;
}

.custom-menu :deep(.el-menu-item:hover),
.custom-menu :deep(.el-sub-menu__title:hover) {
  background: rgba(255,255,255,0.1) !important;
}

.el-container {
  height: calc(100vh - 64px);
}

.logo {
  width: 32px;
  height: 32px;
  object-fit: contain;
}

.el-main {
  background: var(--bg-color);
  padding: 0;
  overflow-x: hidden;
}

.breadcrumb {
  background: var(--card-bg);
  padding: 16px 24px;
  margin-bottom: 24px;
  box-shadow: var(--shadow);
}

.el-dropdown-menu {
  padding: 4px 0;
}

.el-dropdown-menu__item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
}

/* 路由过渡动画 */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .aside {
    position: fixed;
    left: 0;
    top: 64px;
    z-index: 1000;
    transform: translateX(0);
    transition: transform 0.3s;
  }

  .aside.collapsed {
    transform: translateX(-100%);
  }

  .title-container, 
  .subtitle,
  .user-detail {
    display: none;
  }

  .header {
    padding: 0 12px;
  }

  .header-right {
    gap: 12px;
  }

  .header-actions {
    gap: 8px;
  }

  .action-item {
    padding: 4px;
  }

  .breadcrumb {
    padding: 12px 16px;
    margin-bottom: 16px;
  }
}

.message-container {
  max-height: 400px;
  overflow-y: auto;
}

.empty-message {
  text-align: center;
  color: #999;
  padding: 40px 0;
}

.message-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.message-item {
  padding: 12px;
  border: 1px solid #ebeef5;
  border-radius: 6px;
  background: #fafafa;
}

.message-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.user-name {
  font-weight: 500;
  color: #303133;
}

.message-time {
  font-size: 12px;
  color: #909399;
}

.message-content {
  color: #606266;
  line-height: 1.5;
}
</style>

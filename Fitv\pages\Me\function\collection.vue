<!-- 收藏页面 -->

<template>
	<view class="collection-container">
		
		<!-- 加载状态 -->
		<view v-if="loading" class="loading-state">
			<uni-icons type="spinner-cycle" size="30" color="#999"></uni-icons>
			<text class="loading-text">加载中...</text>
		</view>
		
		<!-- 收藏列表 -->
		<view v-else class="collection-list">
			<view v-if="collectionList.length === 0" class="empty-state">
				<uni-icons type="star" size="60" color="#ddd"></uni-icons>
				<text class="empty-text">暂无收藏院校</text>
				<text class="empty-tip">去首页看看有哪些好学校吧~</text>
			</view>
			
			<view v-else class="school-item" v-for="(item, index) in collectionList" :key="item.collection_id" @click="viewSchoolDetail(item)">
				<view class="school-card">
					<image :src="getSchoolImage(item)" class="school-logo" mode="aspectFit" @error="handleImageError"></image>
					<view class="school-info">
						<view class="school-header">
							<text class="school-name">{{item.school_name}}</text>
							<view class="fav-btn" @click.stop="toggleFavorite(item, index)">
								<uni-icons type="star-filled" size="20" color="#ff9500"></uni-icons>
							</view>
						</view>
						<text class="school-location">{{item.school_address}}</text>
						<view class="school-stats">
							<text class="stat-item">录取分数: {{item.score_line || '暂无'}}</text>
							<text class="stat-item">招生人数: {{item.school_number || '暂无'}}</text>
						</view>
						<text class="collection-time">收藏时间: {{formatTime(item.collection_time)}}</text>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
import request from '../../../utils/request.js';
import { getUserInfo, getUserId } from '../../../utils/user.js';

export default {
	data() {
		return {
			collectionList: [],
			loading: false,
			userInfo: null
		}
	},
	onLoad() {
		this.initUserInfo();
	},
	methods: {
		// 初始化用户信息
		initUserInfo() {
			this.userInfo = getUserInfo();
			if (this.userInfo) {
				this.getCollectionList();
			}
		},
		
		// 获取收藏列表
		getCollectionList() {
			const userId = getUserId({ showLoginPrompt: false });
			if (!userId) {
				console.error('用户ID不存在');
				return;
			}
			
			this.loading = true;
			
			uni.request({
				url: 'http://************:9077/collection/',
				method: 'POST',
				data: {
					user_id: userId
				},
				header: {
					'Content-Type': 'application/json'
				},
				success: (res) => {
					console.log('获取收藏列表成功:', res.data);
					if (res.data.code === 200) {
						this.collectionList = res.data.result || [];
					} else {
						uni.showToast({
							title: res.data.message || '获取收藏列表失败',
							icon: 'none'
						});
						this.collectionList = [];
					}
				},
				fail: (err) => {
					console.error('获取收藏列表失败:', err);
					uni.showToast({
						title: '网络请求失败',
						icon: 'none'
					});
					this.collectionList = [];
				},
				complete: () => {
					this.loading = false;
				}
			});
		},
		
		// 查看学校详情
		viewSchoolDetail(school) {
			uni.navigateTo({
				url: `/pages/Home/function/school_detail?id=${school.school_id}&name=${encodeURIComponent(school.school_name)}`
			});
		},
		
		// 切换收藏状态（取消收藏）
		toggleFavorite(school, index) {
			uni.showModal({
				title: '取消收藏',
				content: `确定要取消收藏${school.school_name}吗？`,
				success: (res) => {
					if (res.confirm) {
						this.removeCollection(school, index);
					}
				}
			});
		},
		
		// 取消收藏
		removeCollection(school, index) {
			uni.showLoading({
				title: '取消收藏中...'
			});
			
			uni.request({
				url: 'http://************:9077/collection/removeCollection',
				method: 'POST',
				data: {
					user_id: this.userInfo.id,
					school_id: school.school_id
				},
				header: {
					'Content-Type': 'application/json'
				},
				success: (res) => {
					console.log('取消收藏响应:', res.data);
					if (res.data.code === 200) {
						// 从列表中移除
						this.collectionList.splice(index, 1);
						uni.showToast({
							title: '已取消收藏',
							icon: 'success'
						});
					} else {
						uni.showToast({
							title: res.data.message || '取消收藏失败',
							icon: 'none'
						});
					}
				},
				fail: (err) => {
					console.error('取消收藏失败:', err);
					uni.showToast({
						title: '网络请求失败',
						icon: 'none'
					});
				},
				complete: () => {
					uni.hideLoading();
				}
			});
		},
		
		// 获取学校图片
		getSchoolImage(item) {
			if (item.school_image && item.school_image.trim() !== '') {
				// 检查是否是完整URL
				if (item.school_image.startsWith('http')) {
					return item.school_image;
				} else {
					// 如果不是完整URL，则拼接基础URL
					return 'http://************:9077' + (item.school_image.startsWith('/') ? item.school_image : '/' + item.school_image);
				}
			}
			// 否则使用默认图片
			return '/static/c1.png';
		},
		
		// 处理图片加载错误
		handleImageError(e) {
			console.log('图片加载失败:', e);
			// 可以设置默认图片
		},
		
		// 格式化时间
		formatTime(dateString) {
			if (!dateString) return '';
			const date = new Date(dateString);
			return date.toLocaleDateString('zh-CN', {
				year: 'numeric',
				month: '2-digit',
				day: '2-digit'
			});
		}
	}
}
</script>

<style>
.collection-container {
	background-color: #f5f5f5;
	min-height: 100vh;
}

.loading-state {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 60px 20px;
	background-color: #fff;
	margin: 15px;
	border-radius: 10px;
}

.loading-text {
	margin-top: 10px;
	font-size: 14px;
	color: #999;
}

.collection-list {
	padding: 15px;
}

.empty-state {
	background-color: #fff;
	padding: 60px 20px;
	text-align: center;
	border-radius: 10px;
}

.empty-text {
	display: block;
	font-size: 16px;
	color: #999;
	margin: 15px 0 5px;
}

.empty-tip {
	font-size: 14px;
	color: #ccc;
}

.school-item {
	margin-bottom: 15px;
}

.school-card {
	background-color: #fff;
	border-radius: 10px;
	padding: 15px;
	display: flex;
	box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.school-logo {
	width: 60px;
	height: 100px;
	border-radius: 8px;
	margin-right: 15px;
}

.school-info {
	flex: 1;
}

.school-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 5px;
}

.school-name {
	font-size: 18px;
	font-weight: bold;
	color: #333;
}

.fav-btn {
	padding: 5px;
}

.school-location {
	font-size: 14px;
	color: #666;
	margin-bottom: 8px;
}

.school-stats {
	display: flex;
	justify-content: space-between;
	margin-bottom: 5px;
}

.stat-item {
	font-size: 12px;
	color: #999;
}

.collection-time {
	font-size: 12px;
	color: #ccc;
}
</style>






const express = require('express');
const router = express.Router();
const db = require('../config/db');

// 获取公告列表
router.post('/', (req, res) => {
    const sql = 'SELECT * FROM notice';
    
    db.query(sql, (err, result) => {
        if (err) {
            console.error('查询公告列表失败:', err);
            res.send({
                code: 201,
                success: "失败",
                message: err.message
            });
        } else {
            res.send({
                code: 200,
                success: "成功",
                result: result
            });
        }
    });
});

//删除公告接口
router.post('/delete',(req,res)=>{
    const sql = 'DELETE FROM notice WHERE notice_id = ?';

    db.query(sql, [req.body.notice_id], (err, result) => {
        if(err){
            res.send({
                code: 201,
                success: "失败",
                message: err.message
            });
        } else {
            res.send({
                code: 200,
                success: "成功",
                message: "删除成功"
            });
        }
    });
});

//修改公告接口
router.post('/update',(req,res)=>{
    const sql = 'UPDATE notice SET notice_title = ?, notice_content = ? WHERE notice_id = ?';

    db.query(sql, [req.body.notice_title, req.body.notice_content, req.body.notice_id], (err, result) => {
        if(err){
            res.send({
                code: 201,
                success: "失败",
                message: err.message
            });
        } else {
            res.send({
                code: 200,
                success: "成功",
                message: "修改成功"
            });
        }
    });
});

//新增公告接口
router.post('/add',(req,res)=>{
    const sql = 'INSERT INTO notice (notice_title, notice_content) VALUES (?, ?)';

    db.query(sql, [req.body.notice_title, req.body.notice_content], (err, result) => {
        if(err){
            res.send({
                code: 201,
                success: "失败",
                message: err.message
            });
        } else {
            res.send({
                code: 200,
                success: "成功",
                message: "添加成功"
            });
        }
    });
});

module.exports = router;

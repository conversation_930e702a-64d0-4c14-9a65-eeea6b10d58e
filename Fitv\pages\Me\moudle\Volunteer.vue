<template>
	<view class="volunteer-section">
		<view class="section-header">
			<text class="section-title">志愿记录</text>
			<text class="more" @click="viewAllRecords">全部记录 ></text>
		</view>
		<view class="volunteer-list">
			<view v-if="loading" class="loading">
				<uni-icons type="spinner-cycle" size="24" color="#999"></uni-icons>
				<text class="loading-text">加载中...</text>
			</view>
			<view v-else-if="volunteerRecords.length === 0" class="empty-state">
				<text class="empty-text">暂无志愿记录</text>
			</view>
			<view v-else class="volunteer-item" v-for="(item, index) in volunteerRecords" :key="index">
				<view class="volunteer-card" @click="viewDetail(item)">
					<view class="school-info">
						<image :src="getSchoolImage(item)" class="school-logo"></image>
						<view class="school-detail">
							<text class="school-name">{{item.school_name || '未知学校'}}</text>
							<br />
							<text class="major-name">{{item.specialized_content || '未知专业'}}</text>
						</view>
						<view class="volunteer-status" :class="getStatusClass(item.admission_state)">
							{{getStatusText(item.admission_state)}}
						</view>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
import request from '../../../utils/request.js';

export default {
	name: 'VolunteerRecords',
	props: {
		userId: {
			type: [String, Number],
			default: ''
		},
		limit: {
			type: Number,
			default: 4 // 修改为默认显示4条记录
		}
	},
	data() {
		return {
			loading: false,
			volunteerRecords: [],
			defaultImage: '/static/c1.png' // 默认学校图片
		}
	},
	watch: {
		userId: {
			immediate: true,
			handler(newVal) {
				if (newVal) {
					this.fetchVolunteerRecords();
				}
			}
		}
	},
	methods: {
		// 获取用户志愿填报记录
		fetchVolunteerRecords() {
			if (!this.userId) {
				console.error('用户ID为空，无法获取志愿记录');
				return;
			}
			
			this.loading = true;
			
			request.post('/specialized/getUserAdmissionStatus', {
				user_id: this.userId
			})
			.then(res => {
				console.log('获取志愿记录成功:', res);
				if (res.code === 200 && res.result) {
					// 对数据进行排序，已录取的排在最前面
					let sortedRecords = res.result.sort((a, b) => {
						// 获取状态优先级
						const getPriority = (status) => {
							if (typeof status === 'string') {
								if (status.includes('已录取')) return 1;
								if (status.includes('待录取')) return 2;
								if (status.includes('未录取')) return 3;
							}
							// 数字代码处理
							switch(status) {
								case '1': return 1; // 已录取
								case '0': return 2; // 待录取
								case '2': return 3; // 未录取
								default: return 2;
							}
						};
						
						return getPriority(a.admission_state) - getPriority(b.admission_state);
					});
					
					// 限制显示4条记录
					this.volunteerRecords = sortedRecords.slice(0, 4);
				} else {
					this.volunteerRecords = [];
					console.warn('获取志愿记录失败:', res.message);
				}
			})
			.catch(err => {
				console.error('获取志愿记录异常:', err);
				this.volunteerRecords = [];
				uni.showToast({
					title: '获取志愿记录失败',
					icon: 'none'
				});
			})
			.finally(() => {
				this.loading = false;
			});
		},
		
		// 查看志愿详情
		viewDetail(item) {
			console.log('查看志愿详情:', item.admission_id);
			uni.navigateTo({
				url: '/pages/Me/function/Volunteer_details?id=' + item.admission_id
			});
		},
		
		// 获取学校图片
		getSchoolImage(item) {
			// 如果有学校图片且不为空，则使用学校图片
			if (item.school_image && item.school_image.trim() !== '') {
				// 检查是否是完整URL
				if (item.school_image.startsWith('http')) {
					return item.school_image;
				} else {
					// 如果不是完整URL，则拼接基础URL
					return request.baseUrl + item.school_image;
				}
			}
			// 否则使用默认图片
			return this.defaultImage;
		},
		
		// 获取状态对应的CSS类
		getStatusClass(status) {
			if (typeof status === 'string') {
				if (status.includes('已录取')) {
					return 'success';
				} else if (status.includes('待录取')) {
					return 'pending';
				} else if (status.includes('未录取')) {
					return 'fail';
				}
			}
			
			// 如果是数字代码
			switch(status) {
				case '1':
					return 'success';
				case '0':
					return 'pending';
				case '2':
					return 'fail';
				default:
					return 'pending';
			}
		},
		
		// 获取状态对应的文本
		getStatusText(status) {
			// 如果已经是中文状态，直接返回
			if (typeof status === 'string' && (
				status.includes('已录取') || 
				status.includes('待录取') || 
				status.includes('未录取')
			)) {
				return status;
			}
			
			// 如果是数字代码，转换为中文
			switch(status) {
				case '1':
					return '已录取';
				case '0':
					return '待录取';
				case '2':
					return '未录取';
				default:
					return '待录取';
			}
		},
		
		// 查看全部记录
		viewAllRecords() {
			this.$emit('view-all');
			// 或者直接跳转
			// uni.navigateTo({
			//     url: '/pages/volunteer-history/volunteer-history'
			// });
		}
	}
}
</script>

<style>
.volunteer-section {
	background-color: #fff;
	margin-top: 10px;
}

.section-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 15px;
}

.section-title {
	font-size: 16px;
	font-weight: bold;
}

.more {
	font-size: 12px;
	color: #999;
}

.volunteer-list {
	padding: 0 15px 15px;
}

.volunteer-item {
	margin-bottom: 15px;
}

.volunteer-card {
	background-color: #f9f9f9;
	border-radius: 8px;
	padding: 15px;
}

.volunteer-card:active {
	background-color: #f0f0f0; /* 点击时的背景色 */
}

.school-info {
	display: flex;
	align-items: center;
}

.school-logo {
	width: 40px;
	height: 40px;
	border-radius: 5px;
	margin-right: 10px;
	object-fit: cover; /* 确保图片正确填充容器 */
}

.school-detail {
	flex: 1;
}

.school-name {
	font-size: 16px;
	font-weight: bold;
}

.major-name {
	font-size: 14px;
	color: #666;
}

.volunteer-status {
	padding: 3px 8px;
	border-radius: 3px;
	font-size: 12px;
}

.volunteer-status.success {
	color: #34c759;
	background-color: rgba(52, 199, 89, 0.1);
}

.volunteer-status.pending {
	color: #ff9500;
	background-color: rgba(255, 149, 0, 0.1);
}

.volunteer-status.fail {
	color: #ff3b30;
	background-color: rgba(255, 59, 48, 0.1);
}

.loading {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 20px 0;
}

.loading-text {
	font-size: 14px;
	color: #999;
	margin-top: 10px;
}

.empty-state {
	display: flex;
	justify-content: center;
	padding: 30px 0;
}

.empty-text {
	font-size: 14px;
	color: #999;
}
</style>



// 主题配置
const themes = {
  light: {
    '--bg-color': '#f0f2f5',
    '--header-bg': '#fff',
    '--menu-bg': '#001529',
    '--menu-text': 'rgba(255,255,255,0.65)',
    '--menu-active': '#1890ff',
    '--text-color': '#262626',
    '--text-secondary': '#8c8c8c',
    '--border-color': '#f0f0f0',
    '--hover-bg': 'rgba(0,0,0,0.02)',
    '--card-bg': '#fff',
    '--shadow': '0 1px 4px rgba(0,21,41,.08)'
  },
  dark: {
    '--bg-color': '#141414',
    '--header-bg': '#1f1f1f',
    '--menu-bg': '#1f1f1f',
    '--menu-text': 'rgba(255,255,255,0.65)',
    '--menu-active': '#1890ff',
    '--text-color': '#fff',
    '--text-secondary': 'rgba(255,255,255,0.45)',
    '--border-color': '#303030',
    '--hover-bg': 'rgba(255,255,255,0.08)',
    '--card-bg': '#1f1f1f',
    '--shadow': '0 1px 4px rgba(0,0,0,0.3)'
  }
}

// 应用主题
export function applyTheme(theme) {
  const root = document.documentElement
  const themeConfig = themes[theme]
  
  for (const [key, value] of Object.entries(themeConfig)) {
    root.style.setProperty(key, value)
  }
  
  // 保存主题设置到本地存储
  localStorage.setItem('theme', theme)
}

// 获取当前主题
export function getCurrentTheme() {
  return localStorage.getItem('theme') || 'light'
}

// 切换主题
export function toggleTheme() {
  const currentTheme = getCurrentTheme()
  const newTheme = currentTheme === 'light' ? 'dark' : 'light'
  applyTheme(newTheme)
  return newTheme
} 
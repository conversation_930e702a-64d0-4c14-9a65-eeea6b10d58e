<!-- 专业详情 页面 -->

<template>
	<view class="detail-container">
		
		<!-- 加载状态 -->
		<view v-if="loading" class="loading-container">
			<uni-icons type="spinner-cycle" size="24" color="#d4237a"></uni-icons>
			<text class="loading-text">加载中...</text>
		</view>
		
		<!-- 错误状态 -->
		<view v-else-if="error" class="error-container">
			<image src="/static/error.png" class="error-image"></image>
			<text class="error-text">{{errorMessage}}</text>
			<button class="retry-btn" @click="fetchDetail">重试</button>
		</view>
		
		<!-- 专业详情内容 -->
		<scroll-view v-else scroll-y class="content-scroll">
			<!-- 专业基本信息 -->
			<view class="basic-info">
				<view class="info-header">
					<image 
						:src="detail.specialized_image || '/static/majors/default.jpg'" 
						mode="aspectFill" 
						class="major-image"
						@error="handleImageError"
					></image>
					<view class="info-content">
						<text class="major-name">{{detail.specialized_content}}</text>
						<text class="major-type" v-if="detail.specialized_type">{{detail.specialized_type}}</text>
						<text class="major-degree" v-if="detail.specialized_degree">授予学位：{{detail.specialized_degree}}</text>
					</view>
				</view>
				
				<view class="major-summary" v-if="detail.specialized_part">
					<text class="summary-text">{{detail.specialized_part}}</text>
				</view>
			</view>
			
			<!-- 专业介绍 -->
			<view class="section" v-if="detail.specialized_introduce">
				<view class="section-title">
					<uni-icons type="info" size="18" color="#d4237a"></uni-icons>
					<text>专业介绍</text>
				</view>
				<view class="section-content">
					<text class="content-text">{{detail.specialized_introduce}}</text>
				</view>
			</view>
			
			<!-- 主要课程 -->
			<view class="section" v-if="detail.specialized_course">
				<view class="section-title">
					<uni-icons type="book" size="18" color="#d4237a"></uni-icons>
					<text>主要课程</text>
				</view>
				<view class="section-content">
					<text class="content-text">{{detail.specialized_course}}</text>
				</view>
			</view>
			
			<!-- 就业方向 -->
			<view class="section" v-if="detail.specialized_employment">
				<view class="section-title">
					<uni-icons type="briefcase" size="18" color="#d4237a"></uni-icons>
					<text>就业方向</text>
				</view>
				<view class="section-content">
					<text class="content-text">{{detail.specialized_employment}}</text>
				</view>
			</view>
			
			<!-- 开设院校 -->
			<view class="section">
				<view class="section-title">
					<uni-icons type="home" size="18" color="#d4237a"></uni-icons>
					<text>开设院校</text>
					<text class="school-count" v-if="schoolList.length > 0">({{schoolList.length}}所)</text>
				</view>
				
				<!-- 院校加载状态 -->
				<view v-if="schoolLoading" class="school-loading">
					<uni-icons type="spinner-cycle" size="20" color="#d4237a"></uni-icons>
					<text class="loading-text">加载院校信息中...</text>
				</view>
				
				<!-- 院校列表 -->
				<view v-else-if="schoolList.length > 0" class="school-list">
					<view 
						class="school-card" 
						v-for="(school, index) in schoolList" 
						:key="school.school_id"
						@click="viewSchoolDetail(school)"
					>
						<image 
							:src="school.school_image || '/static/c1.png'" 
							mode="aspectFill" 
							class="school-logo"
							@error="handleSchoolImageError(index)"
						></image>
						<view class="school-info">
							<text class="school-name">{{school.school_name}}</text>
							<text class="school-address" v-if="school.school_address">{{school.school_address}}</text>
							<text class="school-score" v-if="school.Score_line">录取分数线：{{school.Score_line}}</text>
						</view>
						<uni-icons type="right" size="16" color="#ccc"></uni-icons>
					</view>
				</view>
				
				<!-- 无院校信息 -->
				<view v-else class="no-schools">
					<text class="no-schools-text">暂无开设该专业的院校信息</text>
				</view>
			</view>
		</scroll-view>
	</view>
</template>

<script>
import request from '../../../utils/request.js';

export default {
	data() {
		return {
			id: '',
			name: '',
			loading: true,
			schoolLoading: false,
			error: false,
			errorMessage: '',
			detail: {},
			schoolList: []
		}
	},
	
	onLoad(options) {
		this.id = options.id || '';
		this.name = options.name || '';
		
		if (this.id) {
			this.fetchDetail();
		} else {
			this.error = true;
			this.errorMessage = '缺少专业ID参数';
			this.loading = false;
		}
	},
	
	methods: {
		// 获取专业详情
		fetchDetail() {
			this.loading = true;
			this.error = false;
			
			request.post('/specialized/getSpecializedDetail', {
				specialized_id: this.id
			})
			.then(res => {
				if (res.code === 200 && res.result) {
					this.detail = res.result;
					this.detail.specialized_image = this.processImageUrl(res.result.specialized_image);
					this.fetchSchoolList();
				} else {
					this.error = true;
					this.errorMessage = res.message || '获取专业详情失败';
				}
			})
			.catch(err => {
				console.error('获取专业详情失败:', err);
				this.error = true;
				this.errorMessage = '网络请求失败';
			})
			.finally(() => {
				this.loading = false;
			});
		},
		
		// 获取开设该专业的院校列表
		fetchSchoolList() {
			this.schoolLoading = true;
			
			request.post('/specialized/getSchoolSpecialized', {
				specialized_id: this.id
			})
			.then(res => {
				if (res.code === 200 && res.result) {
					this.schoolList = res.result.map(school => {
						let imageUrl = school.school_image;
						
						// 处理图片路径
						if (!imageUrl) {
							imageUrl = '/static/c1.png';
						} else if (imageUrl.startsWith('/uploads/')) {
							imageUrl = request.baseUrl + imageUrl;
						}
						
						return {
							...school,
							school_image: imageUrl
						};
					});
				} else {
					this.schoolList = [];
				}
			})
			.catch(err => {
				console.error('获取院校列表失败:', err);
				this.schoolList = [];
			})
			.finally(() => {
				this.schoolLoading = false;
			});
		},
		
		// 处理图片URL
		processImageUrl(imageUrl) {
			if (!imageUrl || imageUrl === null || imageUrl === undefined || imageUrl === '') {
				return '/static/c1.png';
			}
			
			if (imageUrl.startsWith('http')) {
				try {
					const url = new URL(imageUrl);
					return request.baseUrl + url.pathname;
				} catch (e) {
					const pathMatch = imageUrl.match(/https?:\/\/[^\/]+(.+)/);
					if (pathMatch) {
						return request.baseUrl + pathMatch[1];
					}
				}
			}
			
			if (imageUrl.startsWith('/')) {
				return request.baseUrl + imageUrl;
			} else {
				return request.baseUrl + '/' + imageUrl;
			}
		},
		
		// 查看院校详情
		viewSchoolDetail(school) {
			uni.navigateTo({
				url: `/pages/Home/function/school_detail?id=${school.school_id}&name=${encodeURIComponent(school.school_name)}`
			});
		},
		
		// 返回上一页
		goBack() {
			uni.navigateBack();
		},
		
		// 专业图片加载失败处理
		handleImageError() {
			this.detail.specialized_image = '/static/majors/default.jpg';
		},
		
		// 院校图片加载失败处理
		handleSchoolImageError(index) {
			this.$set(this.schoolList, index, {
				...this.schoolList[index],
				school_image: '/static/c1.png'
			});
		}
	}
}
</script>

<style scoped>
.detail-container {
	background: #f8f9fa;
	min-height: 100vh;
}



.header-title {
	font-size: 18px;
	font-weight: bold;
	color: #333;
}

.header-placeholder {
	width: 20px;
}

.content-scroll {
	height: calc(100vh - var(--status-bar-height) - 60px);
}

.loading-container, .error-container {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 60px 20px;
}

.loading-text {
	margin-top: 12px;
	font-size: 14px;
	color: #999;
}

.error-image {
	width: 120px;
	height: 120px;
	margin-bottom: 16px;
}

.error-text {
	font-size: 16px;
	color: #999;
	margin-bottom: 20px;
}

.retry-btn {
	background: #d4237a;
	color: #fff;
	border: none;
	border-radius: 20px;
	padding: 8px 24px;
	font-size: 14px;
}

.basic-info {
	background: #fff;
	margin: 16px;
	border-radius: 12px;
	padding: 20px;
}

.info-header {
	display: flex;
	margin-bottom: 16px;
}

.major-image {
	width: 80px;
	height: 80px;
	border-radius: 8px;
	margin-right: 16px;
}

.info-content {
	flex: 1;
	display: flex;
	flex-direction: column;
	justify-content: center;
}

.major-name {
	font-size: 20px;
	font-weight: bold;
	color: #333;
	margin-bottom: 8px;
}

.major-type {
	font-size: 14px;
	color: #666;
	margin-bottom: 4px;
}

.major-degree {
	font-size: 12px;
	color: #999;
}

.major-summary {
	padding: 12px;
	background: #f8f9fa;
	border-radius: 8px;
	border-left: 4px solid #d4237a;
}

.summary-text {
	font-size: 14px;
	color: #666;
	line-height: 1.5;
}

.section {
	background: #fff;
	margin: 0 16px 16px;
	border-radius: 12px;
	padding: 20px;
}

.section-title {
	display: flex;
	align-items: center;
	margin-bottom: 16px;
	font-size: 16px;
	font-weight: bold;
	color: #333;
}

.section-title text {
	margin-left: 8px;
}

.school-count {
	margin-left: 8px !important;
	font-size: 14px !important;
	color: #999 !important;
	font-weight: 400 !important;
}

.section-content {
	padding: 12px;
	background: #f8f9fa;
	border-radius: 8px;
}

.content-text {
	font-size: 14px;
	color: #666;
	line-height: 1.6;
}

.school-loading {
	display: flex;
	align-items: center;
	justify-content: center;
	padding: 20px;
}

.school-list {
	display: flex;
	flex-direction: column;
	gap: 12px;
}

.school-card {
	display: flex;
	align-items: center;
	padding: 12px;
	background: #f8f9fa;
	border-radius: 8px;
	border: 1px solid #f0f0f0;
}

.school-logo {
	width: 50px;
	height: 50px;
	border-radius: 6px;
	margin-right: 12px;
}

.school-info {
	flex: 1;
}

.school-name {
	font-size: 16px;
	font-weight: bold;
	color: #333;
	margin-bottom: 4px;
}

.school-address {
	font-size: 12px;
	color: #999;
	margin-bottom: 2px;
}

.school-score {
	font-size: 12px;
	color: #d4237a;
}

.no-schools {
	padding: 40px 20px;
	text-align: center;
}

.no-schools-text {
	font-size: 14px;
	color: #999;
}
</style>





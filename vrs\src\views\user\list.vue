<template>
  <div class="user-list-container">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <span>考生列表</span>
          <el-button type="primary" @click="handleAdd">添加考生</el-button>
        </div>
      </template>
      
      <el-form :inline="true" :model="searchForm" class="search-form">
        <el-form-item label="考生姓名">
          <el-input v-model="searchForm.name" placeholder="请输入考生姓名" clearable />
        </el-form-item>
        <el-form-item label="考生编号">
          <el-input v-model="searchForm.studentId" placeholder="请输入考生编号" clearable />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">查询</el-button>
          <el-button @click="resetSearch">重置</el-button>
        </el-form-item>
      </el-form>

      <el-table :data="tableData" style="width: 100%" v-loading="loading">
        <el-table-column prop="studentId" label="考生编号" width="120" />
        <el-table-column prop="name" label="考生姓名" width="120" />
        <el-table-column prop="gender" label="性别" width="80" />
        <el-table-column prop="phone" label="联系电话" width="150" />
        <el-table-column prop="email" label="电子邮箱" width="200" />
        <el-table-column prop="status" label="状态" width="100">
          <template #default="scope">
            <el-tag :type="scope.row.status === '已激活' ? 'success' : 'warning'">
              {{ scope.row.status }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="注册时间" width="180" />
        <el-table-column label="操作" fixed="right" width="200">
          <template #default="scope">
            <el-button type="primary" link @click="handleEdit(scope.row)">编辑</el-button>
            <el-button type="primary" link @click="handleResetPassword(scope.row)">重置密码</el-button>
            <el-button type="danger" link @click="handleDelete(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <div class="pagination-container">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 30, 50]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>
  </div>
</template>

<script>
import { ref, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'

export default {
  name: 'UserList',
  setup() {
    const loading = ref(false)
    const currentPage = ref(1)
    const pageSize = ref(10)
    const total = ref(0)
    const searchForm = ref({
      name: '',
      studentId: ''
    })

    const tableData = ref([
      {
        studentId: '2024001',
        name: '张三',
        gender: '男',
        phone: '13800138000',
        email: '<EMAIL>',
        status: '已激活',
        createTime: '2024-03-15 10:00:00'
      },
      // 更多测试数据...
    ])

    const handleSearch = () => {
      // 实现搜索逻辑
      console.log('搜索条件：', searchForm.value)
    }

    const resetSearch = () => {
      searchForm.value = {
        name: '',
        studentId: ''
      }
    }

    const handleAdd = () => {
      // 实现添加考生逻辑
      console.log('添加考生')
    }

    const handleEdit = (row) => {
      // 实现编辑考生逻辑
      console.log('编辑考生：', row)
    }

    const handleResetPassword = (row) => {
      ElMessageBox.confirm(
        `确定要重置考生 ${row.name} 的密码吗？`,
        '提示',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        }
      )
        .then(() => {
          ElMessage.success('密码重置成功')
        })
        .catch(() => {})
    }

    const handleDelete = (row) => {
      ElMessageBox.confirm(
        `确定要删除考生 ${row.name} 吗？`,
        '警告',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        }
      )
        .then(() => {
          ElMessage.success('删除成功')
        })
        .catch(() => {})
    }

    const handleSizeChange = (val) => {
      pageSize.value = val
      // 重新加载数据
    }

    const handleCurrentChange = (val) => {
      currentPage.value = val
      // 重新加载数据
    }

    onMounted(() => {
      // 加载初始数据
    })

    return {
      loading,
      searchForm,
      tableData,
      currentPage,
      pageSize,
      total,
      handleSearch,
      resetSearch,
      handleAdd,
      handleEdit,
      handleResetPassword,
      handleDelete,
      handleSizeChange,
      handleCurrentChange
    }
  }
}
</script>

<style scoped>
.user-list-container {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.search-form {
  margin-bottom: 20px;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}
</style> 
<!-- 发布消息 -->

<template>
	<view class="publish-container">
		<view class="publish-header">
			<text class="cancel-btn" @click="cancel">取消</text>
			<text class="title">发布消息</text>
			<text class="publish-btn" @click="submitMessage" :class="{ 'disabled': !isValid }">发布</text>
		</view>
		
		<view class="form-section">
			<textarea 
				class="content-textarea" 
				v-model="content" 
				placeholder="分享你的想法..." 
				maxlength="500"
				auto-height
			></textarea>
			<view class="word-count">{{content.length}}/500</view>
			
			<view class="upload-section" v-if="images.length > 0">
				<view class="image-list">
					<view class="image-item" v-for="(item, index) in images" :key="index">
						<image :src="item" mode="aspectFill" class="preview-image"></image>
						<view class="delete-icon" @click="removeImage(index)">
							<uni-icons type="close" size="20" color="#fff"></uni-icons>
						</view>
					</view>
				</view>
			</view>
			
			<view class="tools-section">
				<view class="tool-item" @click="chooseImage">
					<uni-icons type="image" size="24" color="#666"></uni-icons>
					<text class="tool-text">图片</text>
				</view>
				
				<view class="tool-item" @click="showTagSelector">
					<uni-icons type="paperclip" size="24" color="#666"></uni-icons>
					<text class="tool-text">标签</text>
				</view>
				
				<view class="tool-item" @click="chooseLocation">
					<uni-icons type="location" size="24" color="#666"></uni-icons>
					<text class="tool-text">位置</text>
				</view>
			</view>
			
			<view class="selected-tags" v-if="selectedTags.length > 0">
				<view class="tag-item" v-for="(tag, index) in selectedTags" :key="index">
					#{{tag}}
					<text class="remove-tag" @click="removeTag(index)">×</text>
				</view>
			</view>
			
			<view class="location-info" v-if="location">
				<uni-icons type="location-filled" size="16" color="#d4237a"></uni-icons>
				<text class="location-text">{{location}}</text>
				<text class="remove-location" @click="removeLocation">×</text>
			</view>
		</view>
	</view>
</template>

<script>
	import request from '../../utils/request.js';
	
	export default {
		data() {
			return {
				content: '',
				images: [],
				selectedTags: [],
				location: '',
				userId: 1,
				nickName: '用户1'
			}
		},
		computed: {
			isValid() {
				return this.content.trim().length > 0;
			}
		},
		onLoad() {
			// 获取用户信息（从本地存储）
			this.getUserInfo();
		},
		methods: {
			// 获取用户信息（完善版）
			getUserInfo() {
				// 尝试从本地存储获取
				let storageKeys = ['userInfo', 'user', 'userData', 'loginInfo', 'currentUser'];
				
				// 遍历所有可能的键名
				for (let key of storageKeys) {
					try {
						let data = uni.getStorageSync(key);
						
						if (data) {
							// 如果是字符串，尝试解析JSON
							if (typeof data === 'string') {
								try {
									data = JSON.parse(data);
								} catch (e) {
									continue;
								}
							}
							
							// 检查不同的ID字段名
							if (data.user_id || data.id || data.userId) {
								this.userId = data.user_id || data.id || data.userId;
								this.nickName = data.nick_name || data.name || data.username || '用户' + this.userId;
								return;
							}
						}
					} catch (e) {
						console.error(`从 ${key} 获取数据出错:`, e);
					}
				}
				
				// 如果没有获取到用户信息，提示登录
				uni.showToast({
					title: '请先登录后发布消息',
					icon: 'none',
					duration: 2000
				});
				
				setTimeout(() => {
					uni.navigateTo({
						url: '/pages/Me/Login'
					});
				}, 2000);
			},
			
			// 提交消息
			submitMessage() {
				if (!this.isValid) return;
				
				uni.showLoading({
					title: '发布中...'
				});
				
				// 准备要发送的数据
				let messageContent = this.content;
				
				// 如果有标签，添加到内容中
				if (this.selectedTags.length > 0) {
					messageContent += '\n\n' + this.selectedTags.map(tag => '#' + tag).join(' ');
				}
				
				// 如果有位置，添加到内容中
				if (this.location) {
					messageContent += '\n\n📍 ' + this.location;
				}
				
				// 检查是否有图片需要上传
				if (this.images.length > 0) {
					this.uploadWithImages(messageContent);
				} else {
					this.uploadTextOnly(messageContent);
				}
			},
			
			// 上传带图片的消息（修复版）
			uploadWithImages(messageContent) {
				const uploadImages = this.images.slice(0, 3);
				const url = request.baseUrl + '/message/add';
				
				// 准备表单数据
				const formData = {
					user_id: this.userId,
					message_content: messageContent
				};
				
				// 先上传第一张图片创建消息
				uni.uploadFile({
					url: url,
					filePath: uploadImages[0],
					name: 'images', // 使用统一的字段名
					formData: formData,
					timeout: 30000,
					success: (res) => {
						try {
							const data = JSON.parse(res.data);
							if (data.code === 200 && uploadImages.length > 1) {
								// 如果有更多图片，继续上传到同一条消息
								this.uploadRemainingImages(data.result.message_id, uploadImages.slice(1));
							} else {
								this.handleSubmitResponse(data);
							}
						} catch (e) {
							this.handleSubmitError(new Error('解析响应失败'));
						}
					},
					fail: (err) => {
						this.handleSubmitError(err);
					}
				});
			},
			
			// 上传剩余图片到已存在的消息
			uploadRemainingImages(messageId, remainingImages) {
				let uploadedCount = 0;
				const totalImages = remainingImages.length;
				
				remainingImages.forEach((imagePath, index) => {
					const url = request.baseUrl + '/message/updateImages';
					
					uni.uploadFile({
						url: url,
						filePath: imagePath,
						name: 'images',
						formData: {
							message_id: messageId,
							image_index: index + 1 // 1, 2 (因为第0张已经上传了)
						},
						timeout: 30000,
						success: (res) => {
							uploadedCount++;
							if (uploadedCount === totalImages) {
								// 所有图片上传完成
								this.handleSubmitResponse({
									code: 200,
									success: "成功",
									result: { message_id: messageId }
								});
							}
						},
						fail: (err) => {
							this.handleSubmitError(err);
						}
					});
				});
			},
			
			// 仅上传文本内容
			uploadTextOnly(messageContent) {
				// 使用request.baseUrl而不是硬编码IP
				const url = request.baseUrl + '/message/add';
				
				// 创建表单数据
				const formData = {
					user_id: this.userId,
					message_content: messageContent
				};
				
				// 发送请求
				uni.request({
					url: url,
					method: 'POST',
					data: formData,
					timeout: 30000, // 30秒超时
					success: (res) => {
						this.handleSubmitResponse(res.data);
					},
					fail: (err) => {
						this.handleSubmitError(err);
					}
				});
			},
			
			// 处理提交响应
			handleSubmitResponse(res) {
				uni.hideLoading();
				console.log('发布消息成功:', res);
				
				if (res.code === 200) {
					uni.showToast({
						title: '发布成功',
						icon: 'success'
					});
					
					// 设置刷新标记
					try {
						uni.setStorageSync('needRefreshMessages', 'true');
					} catch (e) {
						console.error('设置刷新标记失败:', e);
					}
					
					// 延迟返回上一页
					setTimeout(() => {
						uni.navigateBack();
					}, 1500);
				} else {
					uni.showToast({
						title: res.message || '发布失败',
						icon: 'none'
					});
				}
			},
			
			// 处理提交错误
			handleSubmitError(err) {
				uni.hideLoading();
				console.error('发布消息失败:', err);
				uni.showToast({
					title: '发布失败，请重试',
					icon: 'none'
				});
			},
			
			// 选择图片
			chooseImage() {
				if (this.images.length >= 3) {
					uni.showToast({
						title: '最多选择3张图片',
						icon: 'none'
					});
					return;
				}
				
				uni.chooseImage({
					count: 3 - this.images.length,
					sizeType: ['compressed'],
					sourceType: ['album', 'camera'],
					success: (res) => {
						this.images = [...this.images, ...res.tempFilePaths];
					}
				});
			},
			
			// 移除图片
			removeImage(index) {
				this.images.splice(index, 1);
			},
			
			// 显示标签选择器
			showTagSelector() {
				uni.showActionSheet({
					itemList: ['推荐', '院校', '专业', '考试', '志愿', '心得', '其他'],
					success: (res) => {
						const selectedTag = ['推荐', '院校', '专业', '考试', '志愿', '心得', '其他'][res.tapIndex];
						if (!this.selectedTags.includes(selectedTag)) {
							this.selectedTags.push(selectedTag);
						}
					}
				});
			},
			
			// 移除标签
			removeTag(index) {
				this.selectedTags.splice(index, 1);
			},
			
			// 选择位置
			chooseLocation() {
				// 由于H5环境可能不支持位置选择，这里模拟一些位置
				const locations = ['北京市海淀区', '上海市浦东新区', '广州市天河区', '深圳市南山区', '成都市武侯区'];
				
				uni.showActionSheet({
					itemList: locations,
					success: (res) => {
						this.location = locations[res.tapIndex];
					}
				});
			},
			
			// 移除位置
			removeLocation() {
				this.location = '';
			},
			
			// 取消发布
			cancel() {
				if (this.content.trim().length > 0 || this.images.length > 0) {
					uni.showModal({
						title: '提示',
						content: '是否放弃编辑？',
						success: (res) => {
							if (res.confirm) {
								uni.navigateBack();
							}
						}
					});
				} else {
					uni.navigateBack();
				}
			}
		}
	}
</script>

<style>
.publish-container {
	background-color: #fff;
	min-height: 100vh;
}

.publish-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	height: 50px;
	padding: 0 15px;
	border-bottom: 1px solid #f0f0f0;
}

.cancel-btn {
	font-size: 16px;
	color: #333;
}

.title {
	font-size: 18px;
	font-weight: bold;
}

.publish-btn {
	font-size: 16px;
	color: #d4237a;
	font-weight: bold;
}

.publish-btn.disabled {
	color: #ccc;
}

.form-section {
	padding: 15px;
}

.content-textarea {
	width: 100%;
	min-height: 150px;
	font-size: 16px;
	line-height: 1.5;
	padding: 0;
}

.word-count {
	text-align: right;
	font-size: 14px;
	color: #999;
	margin-top: 10px;
}

.upload-section {
	margin-top: 20px;
}

.image-list {
	display: flex;
	flex-wrap: wrap;
}

.image-item {
	width: 31%;
	margin-right: 2%;
	margin-bottom: 10px;
	position: relative;
	height: 100px;
}

.preview-image {
	width: 100%;
	height: 100%;
	border-radius: 8px;
}

.delete-icon {
	position: absolute;
	top: -8px;
	right: -8px;
	width: 24px;
	height: 24px;
	background-color: rgba(0, 0, 0, 0.5);
	border-radius: 50%;
	display: flex;
	justify-content: center;
	align-items: center;
}

.tools-section {
	display: flex;
	margin-top: 20px;
	padding: 15px 0;
	border-top: 1px solid #f0f0f0;
}

.tool-item {
	display: flex;
	flex-direction: column;
	align-items: center;
	margin-right: 30px;
}

.tool-text {
	font-size: 12px;
	color: #666;
	margin-top: 5px;
}

.selected-tags {
	display: flex;
	flex-wrap: wrap;
	margin-top: 15px;
}

.tag-item {
	background-color: #f5f5f5;
	color: #666;
	padding: 5px 10px;
	border-radius: 15px;
	font-size: 14px;
	margin-right: 10px;
	margin-bottom: 10px;
	display: flex;
	align-items: center;
}

.remove-tag {
	margin-left: 5px;
	font-size: 16px;
	color: #999;
}

.location-info {
	display: flex;
	align-items: center;
	margin-top: 15px;
	background-color: #f5f5f5;
	padding: 8px 12px;
	border-radius: 15px;
	width: fit-content;
}

.location-text {
	font-size: 14px;
	color: #666;
	margin: 0 5px;
}

.remove-location {
	font-size: 16px;
	color: #999;
}
</style>























<!-- 志愿填报 -->

<template>
	<view class="volunteer-container">
		<!-- 搜索栏 -->
		<view class="search-box">
			<uni-search-bar placeholder="搜索院校或专业" @confirm="search" @input="input" />
		</view>
		
		<!-- 志愿表单 -->
		<view class="form-section">
		<!-- 	<view class="section-title">我的志愿表</view> -->
			<view v-if="loading" class="loading">
				<uni-icons type="spinner-cycle" size="30" color="#d4237a"></uni-icons>
				<text class="loading-text">加载中...</text>
			</view>
			<view v-else-if="userAdmissions.length === 0" class="no-data">
				<text>您还没有填写志愿</text>
				<button class="add-btn" @click="navigateToInsert">立即填报</button>
			</view>
			<view v-else>
				<uni-card v-for="(item, index) in userAdmissions" :key="index" :title="'志愿' + (index + 1)" extra="查看详情" @click="viewAdmissionDetail(item)">
					<view class="form-item">
						<view class="school-info">
							<image :src="item.school_image || '/static/c1.png'" class="school-logo"></image>
							<view class="info">
								<text class="school-name">{{item.school_name}}</text>
								<br />
								<text class="school-desc">{{item.specialized_part || '暂无专业方向'}}</text>
							</view>
							<view class="status-tag" :class="{'admitted': item.admission_state === '已录取'}">
								{{item.admission_state}}
							</view>
						</view>
						<view class="major-list">
							<view class="major-item">
								<text class="major-name">{{item.specialized_content || '暂无专业信息'}}</text>
								<text class="major-score">分数线: {{item.min_scores || '未知'}}</text>
							</view>
							<view class="major-direction" v-if="item.specialized_part">
							</view>
						</view>
						<view class="action-buttons">
							<button class="cancel-btn" @click.stop="cancelVolunteer(item)">取消志愿</button>
						</view>
					</view>
				</uni-card>
				<button class="add-more-btn" @click="navigateToInsert">添加更多志愿</button>
			</view>
		</view>
		
		<!-- 院校推荐 -->
		<view class="recommend-section">
			<view class="section-title">根据您的成绩推荐</view>
			<view class="recommend-list">
				<view class="recommend-item" v-for="(item, index) in recommendSchools" :key="index" @click="selectSchool(item)">
					<image :src="item.logo" class="recommend-logo"></image>
					<view class="recommend-info">
						<text class="recommend-name">{{item.name}}</text>
						<br />
						<text class="recommend-desc">{{item.description}}</text>
						<view class="recommend-tags">
							<text class="tag" v-for="(tag, tidx) in item.tags" :key="tidx">{{tag}}</text>
						</view>
					</view>
				</view>
			</view>
		</view>
		
		<!-- 智能AI助手浮动图标 -->
		<view class="ai-assistant-btn" @click="openAIAssistant">
			<view class="ai-icon-wrapper">
				<uni-icons type="help" size="24" color="#fff"></uni-icons>
			</view>
			<text class="ai-text">智能AI</text>
		</view>
	</view>
</template>

<script>
	import request from '../../utils/request.js';
	
	export default {
		data() {
			return {
				searchValue: '',
				loading: true,
				userAdmissions: [], // 用户志愿数据
				userId: null, // 用户ID
				recommendSchools: [] // 移除默认推荐数据
			}
		},
		onLoad() {
			// 获取用户信息
			this.getUserInfo();
		},
		onShow() {
			// 页面显示时刷新数据
			if (this.userId) {
				// 检查是否需要刷新数据
				try {
					const needRefresh = uni.getStorageSync('needRefreshAdmissions');
					if (needRefresh === 'true') {
						console.log('检测到需要刷新志愿数据');
						this.getUserAdmissions();
						// 清除刷新标记
						uni.removeStorageSync('needRefreshAdmissions');
					}
				} catch (e) {
					console.error('检查刷新标记失败:', e);
				}
				
				this.getUserAdmissions();
				// 获取推荐院校
				this.getRecommendSchools();
			}
		},
		methods: {
			// 获取用户信息
			getUserInfo() {
				try {
					// 尝试从本地存储获取
					let storageKeys = ['userInfo', 'user', 'userData', 'loginInfo', 'currentUser'];
					
					// 遍历所有可能的键名
					for (let key of storageKeys) {
						try {
							let data = uni.getStorageSync(key);
							console.log(`尝试从 ${key} 获取:`, data);
							
							if (data) {
								// 如果是字符串，尝试解析JSON
								if (typeof data === 'string') {
									try {
										data = JSON.parse(data);
									} catch (e) {
										console.error(`解析 ${key} 的JSON数据失败:`, e);
										continue;
									}
								}
								
								// 检查不同的ID字段名
								if (data.user_id) {
									this.userId = data.user_id;
									console.log(`成功从 ${key} 获取到用户ID(user_id):`, this.userId);
									return;
								} else if (data.id) {
									this.userId = data.id;
									console.log(`成功从 ${key} 获取到用户ID(id):`, this.userId);
									return;
								} else if (data.userId) {
									this.userId = data.userId;
									console.log(`成功从 ${key} 获取到用户ID(userId):`, this.userId);
									return;
								}
							}
						} catch (e) {
							console.error(`从 ${key} 获取数据出错:`, e);
						}
					}
					
					// 尝试从全局变量获取
					try {
						const app = getApp();
						if (app && app.globalData) {
							console.log('全局数据:', app.globalData);
							
							const possibleFields = ['userInfo', 'user', 'userData', 'loginInfo', 'currentUser'];
							
							for (let field of possibleFields) {
								if (app.globalData[field]) {
									const data = app.globalData[field];
									if (data.user_id) {
										this.userId = data.user_id;
										console.log(`成功从全局变量 ${field} 获取到用户ID(user_id):`, this.userId);
										return;
									} else if (data.id) {
										this.userId = data.id;
										console.log(`成功从全局变量 ${field} 获取到用户ID(id):`, this.userId);
										return;
									} else if (data.userId) {
										this.userId = data.userId;
										console.log(`成功从全局变量 ${field} 获取到用户ID(userId):`, this.userId);
										return;
									}
								}
							}
						}
					} catch (e) {
						console.error('获取全局数据出错:', e);
					}
					
					// 如果没有获取到用户ID，提示用户登录
					console.warn('未能获取到用户信息，请先登录');
					this.showLoginPrompt();
					
				} catch (e) {
					console.error('获取用户信息出错:', e);
					this.showLoginPrompt();
				}
			},

			// 显示登录提示
			showLoginPrompt() {
				uni.showToast({
					title: '请先登录后查看志愿',
					icon: 'none',
					duration: 2000
				});
				
				setTimeout(() => {
					uni.navigateTo({
						url: '/pages/Me/Login'
					});
				}, 2000);
			},

			// 获取用户志愿信息
			getUserAdmissions() {
				if (!this.userId) {
					console.warn('用户ID为空，无法获取志愿信息');
					this.loading = false;
					return;
				}
				
				this.loading = true;
				
				request.post('/admission/getUserAdmissions', {
					user_id: this.userId
				})
				.then(res => {
					console.log('获取用户志愿信息成功:', res);
					
					if (res.code === 200 && res.result) {
						this.userAdmissions = res.result;
						// 处理数据，确保specialized_part字段存在
						this.userAdmissions.forEach(item => {
							// 如果没有专业方向，尝试使用学校特色作为备用
							if (!item.specialized_part && item.school_characteristic) {
								item.specialized_part = item.school_characteristic;
							}
						});
						// 存储最新的志愿信息到本地缓存
						try {
							uni.setStorageSync('userAdmissions', JSON.stringify(this.userAdmissions));
						} catch (e) {
							console.error('缓存志愿信息失败:', e);
						}
					} else {
						this.userAdmissions = [];
						console.warn('获取用户志愿信息失败或为空:', res.message);
					}
				})
				.catch(err => {
					console.error('获取用户志愿信息请求失败:', err);
					this.userAdmissions = [];
				})
				.finally(() => {
					this.loading = false;
				});
			},
			
			search(e) {
				console.log('搜索：', e);
			},
			input(e) {
				this.searchValue = e;
			},
			navigateToInsert() {
				uni.navigateTo({
					url: '/pages/Volunteer/function/insert'
				});
			},
			viewAdmissionDetail(item) {
				console.log('查看志愿详情:', item);
				// 跳转到详情页面
				uni.navigateTo({
					url: `/pages/Me/function/Volunteer_details?id=${item.admission_id}`
				});
			},
			selectSchool(school) {
				const schoolId = school.school_id || school.id;
				const schoolName = school.name || school.school_name;
				
				uni.navigateTo({
					url: `/pages/Home/function/school_detail?id=${schoolId}&name=${encodeURIComponent(schoolName)}`
				});
			},
			openAIAssistant() {
				uni.navigateTo({
					url: '/pages/Volunteer/intelligent/intelligent'
				});
			},
			cancelVolunteer(item) {
				uni.showModal({
					title: '确认取消',
					content: `确定要取消 ${item.school_name} 的志愿申请吗？`,
					confirmText: '确定取消',
					cancelText: '保留',
					confirmColor: '#ff3b30',
					success: (res) => {
						if (res.confirm) {
							this.performCancelVolunteer(item);
						}
					}
				});
			},
			performCancelVolunteer(item) {
				// 调用删除接口
				request.post('/admission/delete', {
					admission_id: item.admission_id
				})
				.then(res => {
					console.log('取消志愿成功:', res);
					if (res.code === 200) {
						uni.showToast({
							title: '志愿已取消',
							icon: 'success'
						});
						// 刷新志愿列表
						this.getUserAdmissions();
					} else {
						uni.showToast({
							title: res.message || '取消失败',
							icon: 'none'
						});
					}
				})
				.catch(err => {
					console.error('取消志愿失败:', err);
					uni.showToast({
						title: '取消失败，请重试',
						icon: 'none'
					});
				});
			},
			// 获取推荐院校
			getRecommendSchools() {
				request.post('/school/', {})
				.then(res => {
					if (res.code === 200 && res.result) {
						this.recommendSchools = res.result.slice(0, 3).map(school => {
							return {
								school_id: school.school_id,
								id: school.id || school.school_id,
								name: school.school_name,
								school_name: school.school_name,
								description: school.school_characteristic,
								logo: school.school_image || '/static/c1.png',
								tags: school.specialized_part ? [school.specialized_part] : ['综合院校']
							};
						});
					}
				})
				.catch(err => {
					console.error('获取推荐院校失败:', err);
				});
			}
		}
	}
</script>

<style>
.volunteer-container {
	padding-bottom: 20px;
	position: relative;
	min-height: 100vh;
}

.search-box {
	padding: 10px;
	background-color: #fff;
}

.section-title {
	font-size: 16px;
	font-weight: bold;
	padding: 15px 10px 10px;
	background-color: #f5f5f5;
}

.form-section {
	margin-bottom: 10px;
}

.loading {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 30px 0;
}

.loading-text {
	margin-top: 10px;
	font-size: 14px;
	color: #666;
}

.no-data {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 30px 0;
	color: #666;
}

.add-btn {
	margin-top: 15px;
	background-color: #d4237a;
	color: white;
	font-size: 14px;
	padding: 5px 15px;
	border-radius: 20px;
}

.add-more-btn {
	margin: 15px auto;
	background-color: #f0f0f0;
	color: #666;
	font-size: 14px;
	padding: 8px 15px;
	display: block;
	width: 90%;
}

.school-info {
	display: flex;
	margin-bottom: 10px;
	position: relative;
}

.school-logo {
	width: 50px;
	height: 50px;
	margin-right: 10px;
	border-radius: 5px;
}

.info {
	flex: 1;
}

.school-name {
	font-size: 16px;
	font-weight: bold;
}

.school-desc {
	font-size: 12px;
	color: #666;
	margin-top: 5px;
}

.status-tag {
	position: absolute;
	right: 0;
	top: 0;
	background-color: #f0f0f0;
	color: #666;
	padding: 2px 8px;
	border-radius: 10px;
	font-size: 12px;
}

.status-tag.admitted {
	background-color: rgba(76, 175, 80, 0.2);
	color: #4CAF50;
}

.major-list {
	border-top: 1px solid #f5f5f5;
	padding-top: 10px;
}

.major-item {
	display: flex;
	justify-content: space-between;
	margin-bottom: 5px;
}

.major-name {
	font-size: 14px;
}

.major-score {
	font-size: 14px;
	color: #d4237a;
}

.major-direction {
	font-size: 12px;
	color: #666;
	margin-top: 5px;
}

.empty-form {
	height: 100px;
	display: flex;
	flex-direction: column;
	justify-content: center;
	align-items: center;
	background-color: #f9f9f9;
	border-radius: 5px;
}

.add-text {
	font-size: 14px;
	color: #666;
	margin-top: 5px;
}

.recommend-list {
	background-color: #fff;
}

.recommend-item {
	display: flex;
	padding: 15px 10px;
	border-bottom: 1px solid #f5f5f5;
}

.recommend-logo {
	width: 60px;
	height: 60px;
	margin-right: 10px;
	border-radius: 5px;
}

.recommend-info {
	flex: 1;
}

.recommend-name {
	font-size: 16px;
	font-weight: bold;
}

.recommend-desc {
	font-size: 14px;
	color: #666;
	margin: 5px 0;
}

.recommend-tags {
	display: flex;
	flex-wrap: wrap;
}

.tag {
	font-size: 12px;
	color: #d4237a;
	background-color: rgba(212, 35, 122, 0.1);
	padding: 2px 5px;
	border-radius: 3px;
	margin-right: 5px;
	margin-bottom: 5px;
}

/* 智能AI助手浮动图标样式 */
.ai-assistant-btn {
	position: fixed;
	right: 20px;
	bottom: 80px;
	display: flex;
	flex-direction: column;
	align-items: center;
	z-index: 999;
}

.ai-icon-wrapper {
	width: 50px;
	height: 50px;
	background: linear-gradient(135deg, #d4237a, #ff6a9c);
	border-radius: 50%;
	display: flex;
	justify-content: center;
	align-items: center;
	box-shadow: 0 4px 10px rgba(212, 35, 122, 0.3);
	margin-bottom: 5px;
}

.ai-text {
	font-size: 12px;
	color: #333;
}

.action-buttons {
	margin-top: 10px;
	display: flex;
	justify-content: flex-end;
}

.cancel-btn {
	background-color: #f5f5f5;
	color: black;
	width: 100%;
	font-size: 12px;
	padding: 5px 15px;
	border-radius: 15px;
	border: none;
}

.cancel-btn:active {
	background-color: #ccc;
}
</style>













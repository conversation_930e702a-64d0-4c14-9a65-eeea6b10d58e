const express = require('express');
const cors = require('cors');
const path = require('path');

// 导入路由模块
const authRouter = require('./router/auth');
const administratorRouter = require('./router/administrator');
const schoolRouter = require('./router/school');
const bannerRouter = require('./router/banner');
const admissionRouter = require('./router/admission');
const systemRouter = require('./router/system');
const messageRouter = require('./router/message');
const specializedRouter = require('./router/specialized');
const searchRouter = require('./router/search');
const fractionRouter=require('./router/fraction');
const concernRouter=require('./router/concern');
const collectionRouter=require('./router/collection');
const reviseRouter=require('./router/revise');
const noticeRouter=require('./router/notice');

const app = express();

// 中间件配置
app.use(cors());
app.use(express.json());
app.use(express.urlencoded({ extended: false }));
app.use(express.static(path.join(__dirname, '../public')));

// 错误处理中间件
app.use((err, req, res, next) => {
    if (err instanceof SyntaxError && err.status === 400 && 'body' in err) {
        return res.status(400).json({
            success: "失败",
            msg: "请求数据格式错误，请确保发送正确的JSON格式",
            error: err.message
        });
    }
    next();
});

// 路由配置
app.use('/auth',authRouter);
app.use('/administrator',administratorRouter);
app.use('/school',schoolRouter);
app.use('/banner',bannerRouter);
app.use('/admission',admissionRouter);
app.use('/system',systemRouter);
app.use('/message',messageRouter);
app.use('/specialized',specializedRouter);
app.use('/search',searchRouter);
app.use('/fraction',fractionRouter);
app.use('/concern',concernRouter);
app.use('/collection',collectionRouter);
app.use('/revise',reviseRouter);
app.use('/notice',noticeRouter);

// 全局错误处理
app.use((err, req, res, next) => {
    console.error(err.stack);
    res.status(500).json({
        success: "失败",
        msg: "服务器内部错误",
        error: err.message
    });
});

// 处理未捕获的异常
process.on('uncaughtException', (err) => {
    console.error('未捕获的异常:', err);
    // 不要立即退出，给应用一些时间来清理
    setTimeout(() => {
        process.exit(1);
    }, 1000);
});

// 处理未处理的 Promise 拒绝
process.on('unhandledRejection', (reason, promise) => {
    console.error('未处理的 Promise 拒绝:', reason);
    console.error('Promise:', promise);
});

// 优雅关闭
process.on('SIGTERM', () => {
    console.log('收到 SIGTERM 信号，正在优雅关闭服务器...');
    server.close(() => {
        console.log('服务器已关闭');
        process.exit(0);
    });
});

// 启动服务器
const PORT = process.env.PORT || 9077;
const server = app.listen(PORT, () => {
    console.log(`服务器已启动，监听端口 ${PORT}`);
});

// 设置服务器超时
server.timeout = 120000; // 2分钟超时

module.exports = app;

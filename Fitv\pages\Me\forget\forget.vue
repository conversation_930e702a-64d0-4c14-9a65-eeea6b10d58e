<!-- 忘记密码 -->

<template>
	<view class="forget-container">
		<!-- 顶部背景 -->
		<view class="forget-header">
			<image class="forget-bg" src="/static/e1.jpg" mode="aspectFill"></image>
			<view class="header-title">
				<text class="title-text">忘记密码</text>
				<text class="subtitle-text">重置您的账号密码</text>
			</view>
		</view>
		
		<!-- 表单区域 -->
		<view class="forget-form">
			<!-- 步骤指示器 -->
			<view class="steps">
				<view class="step-item" :class="{'active': step >= 1}">
					<view class="step-number">1</view>
					<text class="step-text">验证手机</text>
				</view>
				<view class="step-line" :class="{'active': step >= 2}"></view>
				<view class="step-item" :class="{'active': step >= 2}">
					<view class="step-number">2</view>
					<text class="step-text">设置密码</text>
				</view>
				<view class="step-line" :class="{'active': step >= 3}"></view>
				<view class="step-item" :class="{'active': step >= 3}">
					<view class="step-number">3</view>
					<text class="step-text">完成</text>
				</view>
			</view>
			
			<!-- 步骤1：验证手机 -->
			<view class="step-content" v-if="step === 1">
				<view class="input-item">
					<uni-icons type="phone" size="20" color="#999"></uni-icons>
					<input type="number" v-model="phone" placeholder="请输入手机号" class="input" maxlength="11" />
				</view>
				<view class="input-item">
					<uni-icons type="locked" size="20" color="#999"></uni-icons>
					<input type="number" v-model="code" placeholder="请输入验证码" class="input" maxlength="6" />
					<view class="code-btn" @click="sendCode">{{codeText}}</view>
				</view>
				<button class="next-btn" @click="verifyCode" :loading="loading">下一步</button>
			</view>
			
			<!-- 步骤2：设置密码 -->
			<view class="step-content" v-if="step === 2">
				<view class="input-item">
					<uni-icons type="locked" size="20" color="#999"></uni-icons>
					<input :type="showPassword ? 'text' : 'password'" v-model="newPassword" placeholder="请输入新密码" class="input" />
					<uni-icons :type="showPassword ? 'eye' : 'eye-slash'" size="20" color="#999" @click="togglePasswordVisibility"></uni-icons>
				</view>
				<view class="input-item">
					<uni-icons type="locked" size="20" color="#999"></uni-icons>
					<input :type="showPassword ? 'text' : 'password'" v-model="confirmPassword" placeholder="请确认新密码" class="input" />
				</view>
				<view class="password-tips">
					<uni-icons type="info" size="14" color="#999"></uni-icons>
					<text class="tips-text">密码长度至少为6位，建议使用字母、数字和符号的组合</text>
				</view>
				<button class="next-btn" @click="resetPassword" :loading="loading">确认修改</button>
			</view>
			
			<!-- 步骤3：完成 -->
			<view class="step-content" v-if="step === 3">
				<view class="success-icon">
					<uni-icons type="checkmarkempty" size="60" color="#d4237a"></uni-icons>
				</view>
				<view class="success-text">密码重置成功</view>
				<view class="success-tips">您的账号密码已成功重置，请使用新密码登录</view>
				<button class="next-btn" @click="backToLogin">返回登录</button>
			</view>
		</view>
	</view>
</template>

<script>
	import request from '@/utils/request';
	
	export default {
		data() {
			return {
				step: 1, // 当前步骤：1-验证手机，2-设置密码，3-完成
				phone: '', // 手机号
				code: '', // 验证码
				newPassword: '', // 新密码
				confirmPassword: '', // 确认密码
				showPassword: false, // 密码可见性
				codeText: '获取验证码', // 验证码按钮文本
				timer: null, // 倒计时定时器
				countdown: 60, // 倒计时秒数
				loading: false, // 加载状态
				verifiedCode: '' // 已验证的验证码，用于第二步
			}
		},
		methods: {
			// 发送验证码
			sendCode() {
				// 检查是否在倒计时中
				if (this.codeText !== '获取验证码') {
					return;
				}
				
				// 检查手机号
				if (!this.phone || this.phone.length !== 11) {
					uni.showToast({
						title: '请输入正确的手机号',
						icon: 'none'
					});
					return;
				}
				
				// 开始倒计时
				this.countdown = 60;
				this.codeText = `${this.countdown}s`;
				this.timer = setInterval(() => {
					this.countdown--;
					this.codeText = `${this.countdown}s`;
					if (this.countdown <= 0) {
						clearInterval(this.timer);
						this.codeText = '获取验证码';
					}
				}, 1000);
				
				// 发送验证码请求
				request.post('/auth/sendCode', {
					phone: this.phone
				}).then(res => {
					uni.showToast({
						title: '验证码已发送',
						icon: 'success'
					});
					console.log('验证码（开发环境）:', res.verificationCode);
				}).catch(err => {
					// 发送失败，停止倒计时
					clearInterval(this.timer);
					this.codeText = '获取验证码';
					uni.showToast({
						title: err.message || '验证码发送失败',
						icon: 'none'
					});
				});
			},
			
			// 验证验证码
			verifyCode() {
				if (this.loading) return;
				
				// 验证手机号和验证码
				if (!this.phone || this.phone.length !== 11) {
					uni.showToast({
						title: '请输入正确的手机号',
						icon: 'none'
					});
					return;
				}
				
				if (!this.code || this.code.length !== 6) {
					uni.showToast({
						title: '请输入6位验证码',
						icon: 'none'
					});
					return;
				}
				
				this.loading = true;
				
				// 发送验证码请求，只是为了验证验证码是否有效，不实际使用登录功能
				request.post('/auth/sendCode', {
					phone: this.phone
				}).then(res => {
					// 这里我们获取到了验证码，但在实际应用中不应该这样做
					// 这里仅用于测试，实际应用中应该通过短信发送验证码
					console.log('验证码（开发环境）:', res.verificationCode);
					
					// 模拟验证通过
					this.loading = false;
					
					// 保存已验证的验证码和手机号，用于第二步重置密码
					this.verifiedCode = res.verificationCode || this.code;
					
					// 进入设置密码步骤
					this.step = 2;
				}).catch(err => {
					this.loading = false;
					uni.showToast({
						title: err.message || '获取验证码失败',
						icon: 'none'
					});
				});
			},
			
			// 切换密码可见性
			togglePasswordVisibility() {
				this.showPassword = !this.showPassword;
			},
			
			// 重置密码
			resetPassword() {
				if (this.loading) return;
				
				// 验证密码
				if (!this.newPassword) {
					uni.showToast({
						title: '请输入新密码',
						icon: 'none'
					});
					return;
				}
				
				if (this.newPassword.length < 6) {
					uni.showToast({
						title: '密码长度至少为6位',
						icon: 'none'
					});
					return;
				}
				
				if (this.newPassword !== this.confirmPassword) {
					uni.showToast({
						title: '两次输入的密码不一致',
						icon: 'none'
					});
					return;
				}
				
				// 再次检查手机号和验证码是否存在
				if (!this.phone || !this.verifiedCode) {
					uni.showToast({
						title: '验证信息已失效，请重新验证',
						icon: 'none'
					});
					this.step = 1; // 返回第一步
					return;
				}
				
				this.loading = true;
				
				// 打印请求参数，用于调试
				const requestData = {
					phone: this.phone,
					code: this.verifiedCode,
					newpassword: this.newPassword
				};
				console.log('发送重置密码请求:', requestData);
				
				// 调用重置密码接口
				request.post('/auth/forgetPassword', requestData)
					.then(res => {
						this.loading = false;
						console.log('重置密码响应:', res);
						
						if (res.code === 200) {
							this.step = 3; // 进入完成步骤
						} else {
							uni.showToast({
								title: res.message || '密码重置失败',
								icon: 'none'
							});
						}
					})
					.catch(err => {
						this.loading = false;
						console.error('重置密码错误:', err);
						
						// 显示详细错误信息
						if (err && err.message) {
							uni.showToast({
								title: err.message,
								icon: 'none',
								duration: 3000
							});
						} else {
							uni.showToast({
								title: '密码重置失败，请重试',
								icon: 'none'
							});
						}
					});
			},
			
			// 返回登录页
			backToLogin() {
				uni.navigateBack();
			}
		},
		// 组件销毁前清除定时器
		beforeDestroy() {
			if (this.timer) {
				clearInterval(this.timer);
			}
		}
	}
</script>

<style>
.forget-container {
	min-height: 100vh;
	background-color: #000;
}

.forget-header {
	position: relative;
	height: 200px;
	overflow: hidden;
}

.forget-bg {
	width: 100%;
	height: 100%;
}

.header-title {
	position: absolute;
	top: 50%;
	left: 0;
	width: 100%;
	transform: translateY(-50%);
	text-align: center;
	color: #fff;
	z-index: 1;
}

.title-text {
	font-size: 28px;
	font-weight: bold;
	display: block;
	margin-bottom: 10px;
	text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
	color: #000;
}

.subtitle-text {
	font-size: 16px;
	display: block;
	text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
	color: #000;
}

.forget-form {
	position: relative;
	margin-top: -20px;
	background-color: #fff;
	border-radius: 20px 20px 0 0;
	padding: 30px 20px;
	box-shadow: 0 -3px 10px rgba(0, 0, 0, 0.05);
	min-height: calc(100vh - 180px);
}

.steps {
	display: flex;
	align-items: center;
	justify-content: center;
	margin-bottom: 40px;
}

.step-item {
	display: flex;
	flex-direction: column;
	align-items: center;
}

.step-number {
	width: 30px;
	height: 30px;
	border-radius: 50%;
	background-color: #f0f0f0;
	color: #999;
	display: flex;
	justify-content: center;
	align-items: center;
	font-size: 14px;
	margin-bottom: 5px;
}

.step-text {
	font-size: 12px;
	color: #999;
}

.step-line {
	width: 60px;
	height: 1px;
	background-color: #f0f0f0;
	margin: 0 10px;
}

.step-item.active .step-number {
	background-color: #d4237a;
	color: #fff;
}

.step-item.active .step-text {
	color: #d4237a;
}

.step-line.active {
	background-color: #d4237a;
}

.input-item {
	display: flex;
	align-items: center;
	height: 50px;
	border-bottom: 1px solid #f0f0f0;
	margin-bottom: 20px;
}

.input {
	flex: 1;
	height: 100%;
	font-size: 16px;
	margin-left: 10px;
}

.code-btn {
	padding: 5px 10px;
	background-color: #f5f5f5;
	color: #d4237a;
	font-size: 14px;
	border-radius: 3px;
}

.next-btn {
	width: 100%;
	height: 50px;
	line-height: 50px;
	background-color: #d4237a;
	color: #fff;
	font-size: 16px;
	border-radius: 25px;
	margin-top: 30px;
}

.password-tips {
	display: flex;
	align-items: center;
	padding: 10px 0;
}

.tips-text {
	font-size: 12px;
	color: #999;
	margin-left: 5px;
}

.success-icon {
	display: flex;
	justify-content: center;
	margin: 30px 0;
}

.success-text {
	font-size: 20px;
	font-weight: bold;
	text-align: center;
	margin-bottom: 10px;
}

.success-tips {
	font-size: 14px;
	color: #999;
	text-align: center;
	margin-bottom: 30px;
}
</style>

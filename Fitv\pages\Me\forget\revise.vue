<template>
	<view class="container">
		
		<view class="form">
			<view class="input-wrapper">
				<input 
					class="input" 
					:type="showOldPassword ? 'text' : 'password'" 
					v-model="form.oldPassword" 
					placeholder="请输入旧密码"
				/>
				<uni-icons 
					:type="showOldPassword ? 'eye' : 'eye-slash'" 
					size="20" 
					color="#999" 
					class="eye-icon"
					@click="showOldPassword = !showOldPassword"
				></uni-icons>
			</view>
			
			<view class="input-wrapper">
				<input 
					class="input" 
					:type="showNewPassword ? 'text' : 'password'" 
					v-model="form.newPassword" 
					placeholder="请输入新密码"
				/>
				<uni-icons 
					:type="showNewPassword ? 'eye' : 'eye-slash'" 
					size="20" 
					color="#999" 
					class="eye-icon"
					@click="showNewPassword = !showNewPassword"
				></uni-icons>
			</view>
			
			<view class="input-wrapper">
				<input 
					class="input" 
					:type="showConfirmPassword ? 'text' : 'password'" 
					v-model="form.confirmPassword" 
					placeholder="确认新密码"
				/>
				<uni-icons 
					:type="showConfirmPassword ? 'eye' : 'eye-slash'" 
					size="20" 
					color="#999" 
					class="eye-icon"
					@click="showConfirmPassword = !showConfirmPassword"
				></uni-icons>
			</view>
			
			<button class="btn" @click="changePassword" :disabled="loading">
				{{loading ? '修改中...' : '确认修改'}}
			</button>
		</view>
	</view>
</template>

<script>
import request from '../../../utils/request.js';
import { getUserId } from '../../../utils/user.js';

export default {
	data() {
		return {
			form: {
				oldPassword: '',
				newPassword: '',
				confirmPassword: ''
			},
			loading: false,
			showOldPassword: false,
			showNewPassword: false,
			showConfirmPassword: false
		}
	},
	
	methods: {
		validate() {
			const { oldPassword, newPassword, confirmPassword } = this.form;
			
			if (!oldPassword) return '请输入旧密码';
			if (!newPassword) return '请输入新密码';
			if (newPassword.length < 6) return '新密码长度至少6位';
			if (newPassword !== confirmPassword) return '两次输入的密码不一致';
			if (oldPassword === newPassword) return '新密码不能与旧密码相同';
			
			return null;
		},
		
		async changePassword() {
			if (this.loading) return;
			
			const error = this.validate();
			if (error) {
				uni.showToast({ title: error, icon: 'none' });
				return;
			}
			
			const userId = getUserId({ showLoginPrompt: true });
			if (!userId) return;
			
			this.loading = true;
			
			try {
				const res = await request.post('/revise/changePassword', {
					user_id: userId,
					old_password: this.form.oldPassword,
					new_password: this.form.newPassword
				});
				
				if (res.code === 200) {
					uni.showToast({ title: '密码修改成功', icon: 'success' });
					setTimeout(() => uni.navigateBack(), 1500);
				} else {
					uni.showToast({ title: res.message || '修改失败', icon: 'none' });
				}
			} catch (err) {
				uni.showToast({ title: '网络请求失败', icon: 'none' });
			} finally {
				this.loading = false;
			}
		}
	}
}
</script>

<style scoped>
.container {
    padding: 20px;
    background: #f5f5f5;
    min-height: 100vh;
}

.input-wrapper {
    position: relative;
    margin-bottom: 15px;
}

.input {
    width: 100%;
    height: 45px;
    border: 1px solid #ddd;
    border-radius: 5px;
    padding: 0 45px 0 15px;
    background: #fff;
    box-sizing: border-box;
}

.eye-icon {
    position: absolute;
    right: 15px;
    top: 50%;
    transform: translateY(-50%);
    cursor: pointer;
}

.btn {
    width: 100%;
    height: 45px;
    background: #007aff;
    color: #fff;
    border: none;
    border-radius: 5px;
    margin-top: 10px;
}

.btn:disabled {
    opacity: 0.6;
}
</style>




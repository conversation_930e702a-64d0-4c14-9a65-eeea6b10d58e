
const express = require('express');
const router = express.Router();
const db = require('../config/db');

// 查询学校信息接口
router.post('/school', (req, res) => {
    const sql = 'SELECT * FROM school';
    db.query(sql, (err, result) => {
        if (err) { 
            res.status(500).send({
                code: 201,
                success: "失败",
                message: "查询失败",
                error: err.message
            });
        } else {
            res.send({
                code: 200,
                success: "成功",
                message: "查询成功",
                result: result
            });
        }
    });
});

// 创建学校专业分数表
router.post('/createSchoolSpecializedScore', (req, res) => {
    const { school_id, specialized_id, min_score, max_score, year } = req.body;
    
    if (!school_id || !specialized_id || !min_score) {
        return res.status(400).send({
            code: 201,
            success: "失败",
            message: "学校ID、专业ID和最低分数不能为空"
        });
    }
    
    const currentYear = year || new Date().getFullYear();
    
    const sql = `
        INSERT INTO school_scores 
        (school_id, specialized_id, min_score, max_score, year) 
        VALUES (?, ?, ?, ?, ?)
    `;
    
    db.query(sql, [school_id, specialized_id, min_score, max_score || null, currentYear], (err, result) => {
        if (err) {
            res.status(500).send({
                code: 201,
                success: "失败",
                message: "创建失败",
                error: err.message
            });
        } else {
            res.send({
                code: 200,
                success: "成功",
                message: "创建成功",
                data: {
                    id: result.insertId,
                    school_id,
                    specialized_id,
                    min_score,
                    max_score: max_score || null,
                    year: currentYear
                }
            });
        }
    });
});

// 更新学校专业分数
router.post('/updateSchoolSpecializedScore', (req, res) => {
    const { id, school_id, specialized_id, min_score, max_score, year } = req.body;
    
    if (!id) {
        return res.status(400).send({
            code: 201,
            success: "失败",
            message: "ID不能为空"
        });
    }
    
    const updateFields = [];
    const values = [];
    
    if (school_id !== undefined) {
        updateFields.push('school_id = ?');
        values.push(school_id);
    }
    
    if (specialized_id !== undefined) {
        updateFields.push('specialized_id = ?');
        values.push(specialized_id);
    }
    
    if (min_score !== undefined) {
        updateFields.push('min_score = ?');
        values.push(min_score);
    }
    
    if (max_score !== undefined) {
        updateFields.push('max_score = ?');
        values.push(max_score);
    }
    
    if (year !== undefined) {
        updateFields.push('year = ?');
        values.push(year);
    }
    
    if (updateFields.length === 0) {
        return res.status(400).send({
            code: 201,
            success: "失败",
            message: "没有要更新的字段"
        });
    }
    
    values.push(id);
    const sql = `UPDATE school_scores SET ${updateFields.join(', ')} WHERE id = ?`;
    
    db.query(sql, values, (err, result) => {
        if (err) {
            res.status(500).send({
                code: 201,
                success: "失败",
                message: "更新失败",
                error: err.message
            });
        } else if (result.affectedRows === 0) {
            res.status(404).send({
                code: 201,
                success: "失败",
                message: "未找到该记录"
            });
        } else {
            res.send({
                code: 200,
                success: "成功",
                message: "更新成功"
            });
        }
    });
});

// 删除学校专业分数
router.post('/deleteSchoolSpecializedScore', (req, res) => {
    const { id } = req.body;
    
    if (!id) {
        return res.status(400).send({
            code: 201,
            success: "失败",
            message: "ID不能为空"
        });
    }
    
    const sql = 'DELETE FROM school_scores WHERE id = ?';
    
    db.query(sql, [id], (err, result) => {
        if (err) {
            res.status(500).send({
                code: 201,
                success: "失败",
                message: "删除失败",
                error: err.message
            });
        } else if (result.affectedRows === 0) {
            res.status(404).send({
                code: 201,
                success: "失败",
                message: "未找到该记录"
            });
        } else {
            res.send({
                code: 200,
                success: "成功",
                message: "删除成功"
            });
        }
    });
});

// 查询学校专业分数
router.post('/getSchoolSpecializedScore', (req, res) => {
    const { school_id, specialized_id, year } = req.body;
    
    let sql = `
        SELECT 
            sss.*,
            s.school_name,
            s.school_image,
            sp.specialized_content
        FROM 
            school_scores sss
        LEFT JOIN 
            school s ON sss.school_id = s.school_id
        LEFT JOIN 
            specialized sp ON sss.specialized_id = sp.specialized_id
        WHERE 1=1
    `;
    
    const params = [];
    
    if (school_id) {
        sql += ' AND sss.school_id = ?';
        params.push(school_id);
    }
    
    if (specialized_id) {
        sql += ' AND sss.specialized_id = ?';
        params.push(specialized_id);
    }
    
    if (year) {
        sql += ' AND sss.year = ?';
        params.push(year);
    }
    
    sql += ' ORDER BY sss.min_score DESC';
    
    db.query(sql, params, (err, result) => {
        if (err) {
            res.status(500).send({
                code: 201,
                success: "失败",
                message: "查询失败",
                error: err.message
            });
        } else {
            res.send({
                code: 200,
                success: "成功",
                message: "查询成功",
                result: result
            });
        }
    });
});

// 查询某个学校的所有专业分数线
router.post('/getSchoolAllSpecializedScores', (req, res) => {
    const { school_id, year } = req.body;
    
    if (!school_id) {
        return res.status(400).send({
            code: 201,
            success: "失败",
            message: "学校ID不能为空"
        });
    }
    
    let sql = `
        SELECT 
            sss.*,
            s.school_name,
            s.school_image,
            sp.specialized_content
        FROM 
            school_scores sss
        LEFT JOIN 
            school s ON sss.school_id = s.school_id
        LEFT JOIN 
            specialized sp ON sss.specialized_id = sp.specialized_id
        WHERE 
            sss.school_id = ?
    `;
    
    const params = [school_id];
    
    if (year) {
        sql += ' AND sss.year = ?';
        params.push(year);
    }
    
    sql += ' ORDER BY sss.min_score DESC';
    
    db.query(sql, params, (err, result) => {
        if (err) {
            res.status(500).send({
                code: 201,
                success: "失败",
                message: "查询失败",
                error: err.message
            });
        } else {
            res.send({
                code: 200,
                success: "成功",
                message: "查询成功",
                result: result
            });
        }
    });
});

// 查询某个专业在不同学校的分数线
router.post('/getSpecializedAllSchoolScores', (req, res) => {
    const { specialized_id, year } = req.body;
    
    if (!specialized_id) {
        return res.status(400).send({
            code: 201,
            success: "失败",
            message: "专业ID不能为空"
        });
    }
    
    let sql = `
        SELECT 
            sss.*,
            s.school_name,
            s.school_image,
            sp.specialized_content
        FROM 
            school_scores sss
        LEFT JOIN 
            school s ON sss.school_id = s.school_id
        LEFT JOIN 
            specialized sp ON sss.specialized_id = sp.specialized_id
        WHERE 
            sss.specialized_id = ?
    `;
    
    const params = [specialized_id];
    
    if (year) {
        sql += ' AND sss.year = ?';
        params.push(year);
    }
    
    sql += ' ORDER BY sss.min_score DESC';
    
    db.query(sql, params, (err, result) => {
        if (err) {
            res.status(500).send({
                code: 201,
                success: "失败",
                message: "查询失败",
                error: err.message
            });
        } else {
            res.send({
                code: 200,
                success: "成功",
                message: "查询成功",
                result: result
            });
        }
    });
});

// 根据用户分数推荐可报考的学校和专业
router.post('/recommendSchoolsAndSpecialized', (req, res) => {
    const { user_fraction, specialized_id, year } = req.body;
    
    if (!user_fraction) {
        return res.status(400).send({
            code: 201,
            success: "失败",
            message: "用户分数不能为空"
        });
    }
    
    let sql = `
        SELECT 
            sss.*,
            s.school_name,
            s.school_image,
            s.school_address,
            s.school_characteristic,
            sp.specialized_content,
            (sss.min_score - ?) as score_diff
        FROM 
            school_scores sss
        LEFT JOIN 
            school s ON sss.school_id = s.school_id
        LEFT JOIN 
            specialized sp ON sss.specialized_id = sp.specialized_id
        WHERE 
            ? >= sss.min_score
    `;
    
    const params = [user_fraction, user_fraction];
    
    if (specialized_id) {
        sql += ' AND sss.specialized_id = ?';
        params.push(specialized_id);
    }
    
    if (year) {
        sql += ' AND sss.year = ?';
        params.push(year);
    } else {
        // 默认使用最新年份的数据
        sql += ' AND sss.year = (SELECT MAX(year) FROM school_scores)';
    }
    
    sql += ' ORDER BY sss.min_score DESC';
    
    db.query(sql, params, (err, result) => {
        if (err) {
            res.status(500).send({
                code: 201,
                success: "失败",
                message: "查询失败",
                error: err.message
            });
        } else {
            res.send({
                code: 200,
                success: "成功",
                message: "查询成功",
                result: result
            });
        }
    });
});

// 添加多表联查接口
router.post('/comprehensiveQuery', (req, res) => {
    const { 
        user_id, 
        school_id, 
        specialized_id, 
        admission_id, 
        year,
        min_score,
        max_score
    } = req.body;
    
    let sql = `
        SELECT 
            u.*,
            s.*,
            sp.*,
            a.*,
            sss.*
        FROM 
            admission a
        LEFT JOIN 
            user u ON a.user_id = u.user_id
        LEFT JOIN 
            school s ON a.school_id = s.school_id
        LEFT JOIN 
            specialized sp ON a.specialized_id = sp.specialized_id
        LEFT JOIN 
            school_scores sss ON (s.school_id = sss.school_id AND sp.specialized_id = sss.specialized_id)
        WHERE 1=1
    `;
    
    const params = [];
    
    // 添加过滤条件
    if (user_id) {
        sql += ' AND u.user_id = ?';
        params.push(user_id);
    }
    
    if (school_id) {
        sql += ' AND s.school_id = ?';
        params.push(school_id);
    }
    
    if (specialized_id) {
        sql += ' AND sp.specialized_id = ?';
        params.push(specialized_id);
    }
    
    if (admission_id) {
        sql += ' AND a.admission_id = ?';
        params.push(admission_id);
    }
    
    if (year) {
        sql += ' AND sss.year = ?';
        params.push(year);
    }
    
    if (min_score) {
        sql += ' AND sss.min_score >= ?';
        params.push(min_score);
    }
    
    if (max_score) {
        sql += ' AND sss.max_score <= ?';
        params.push(max_score);
    }
    
    // 添加排序
    sql += ' ORDER BY u.user_id, s.school_id, sp.specialized_id';
    
    db.query(sql, params, (err, result) => {
        if (err) {
            res.status(500).send({
                code: 201,
                success: "失败",
                message: "查询失败",
                error: err.message
            });
        } else {
            res.send({
                code: 200,
                success: "成功",
                message: "查询成功",
                result: result
            });
        }
    });
});

// 根据用户ID查询用户可以报考的学校和专业
router.post('/getUserEligibleSchoolsAndSpecialized', (req, res) => {
    const { user_id, year } = req.body;
    
    if (!user_id) {
        return res.status(400).send({
            code: 201,
            success: "失败",
            message: "用户ID不能为空"
        });
    }
    
    // 先查询用户分数
    const userSql = 'SELECT user_fraction FROM user WHERE user_id = ?';
    
    db.query(userSql, [user_id], (err, userResult) => {
        if (err) {
            return res.status(500).send({
                code: 201,
                success: "失败",
                message: "查询用户信息失败",
                error: err.message
            });
        }
        
        if (userResult.length === 0) {
            return res.status(404).send({
                code: 201,
                success: "失败",
                message: "未找到该用户"
            });
        }
        
        const userFraction = userResult[0].user_fraction;
        
        if (!userFraction) {
            return res.status(400).send({
                code: 201,
                success: "失败",
                message: "用户分数未设置"
            });
        }
        
        // 查询用户可以报考的学校和专业
        let sql = `
            SELECT 
                s.school_id,
                s.school_name,
                s.school_image,
                s.school_address,
                s.school_characteristic,
                s.school_idea,
                s.school_email,
                
                sp.specialized_id,
                sp.specialized_content,
                
                sss.min_score,
                sss.max_score,
                sss.year,
                
                (? - sss.min_score) as score_diff
            FROM 
                school_scores sss
            LEFT JOIN 
                school s ON sss.school_id = s.school_id
            LEFT JOIN 
                specialized sp ON sss.specialized_id = sp.specialized_id
            WHERE 
                ? >= sss.min_score
        `;
        
        const params = [userFraction, userFraction];
        
        if (year) {
            sql += ' AND sss.year = ?';
            params.push(year);
        } else {
            // 默认使用最新年份的数据
            sql += ' AND sss.year = (SELECT MAX(year) FROM school_scores)';
        }
        
        sql += ' ORDER BY sss.min_score DESC';
        
        db.query(sql, params, (err, result) => {
            if (err) {
                res.status(500).send({
                    code: 201,
                    success: "失败",
                    message: "查询失败",
                    error: err.message
                });
            } else {
                res.send({
                    code: 200,
                    success: "成功",
                    message: "查询成功",
                    user_fraction: userFraction,
                    result: result
                });
            }
        });
    });
});

// 查询用户的录取情况和对应的学校专业信息
router.post('/getUserAdmissionDetails', (req, res) => {
    const { user_id } = req.body;
    
    if (!user_id) {
        return res.status(400).send({
            code: 201,
            success: "失败",
            message: "用户ID不能为空"
        });
    }
    
    const sql = `
        SELECT 
            u.user_id,
            u.username,
            u.nick_name,
            u.user_fraction,
            
            a.admission_id,
            a.admission_state,
            
            
            s.school_id,
            s.school_name,
            s.school_image,
            s.school_address,
            s.school_characteristic,
            
            sp.specialized_id,
            sp.specialized_content,
            
            sss.min_score,
            sss.max_score,
            sss.year,
            
            (u.user_fraction - sss.min_score) as score_diff
        FROM 
            admission a
        LEFT JOIN 
            user u ON a.user_id = u.user_id
        LEFT JOIN 
            school s ON a.school_id = s.school_id
        LEFT JOIN 
            specialized sp ON a.specialized_id = sp.specialized_id
        LEFT JOIN 
            school_scores sss ON (s.school_id = sss.school_id AND sp.specialized_id = sss.specialized_id)
        WHERE 
            a.user_id = ?
        ORDER BY 
            a.admission_state DESC, sss.min_score DESC
    `;
    
    db.query(sql, [user_id], (err, result) => {
        if (err) {
            res.status(500).send({
                code: 201,
                success: "失败",
                message: "查询失败",
                error: err.message
            });
        } else {
            res.send({
                code: 200,
                success: "成功",
                message: "查询成功",
                result: result
            });
        }
    });
});



module.exports = router;

<template>
  <div class="settings-container">
    <el-card class="settings-card">
      <template #header>
        <div class="card-header">
          <h3>系统设置</h3>
        </div>
      </template>

      <el-tabs v-model="activeTab">
        <!-- 基本设置 -->
        <el-tab-pane label="基本设置" name="basic">
          <el-form :model="basicForm" label-width="120px">
            <el-form-item label="系统名称">
              <el-input v-model="basicForm.systemName" placeholder="请输入系统名称" />
            </el-form-item>
            <el-form-item label="系统Logo">
              <el-upload
                class="logo-uploader"
                action="/api/upload"
                :show-file-list="false"
                :on-success="handleLogoSuccess"
                :before-upload="beforeLogoUpload">
                <img v-if="basicForm.logo" :src="basicForm.logo" class="logo" />
                <el-icon v-else class="logo-uploader-icon"><Plus /></el-icon>
              </el-upload>
            </el-form-item>
            <el-form-item label="系统描述">
              <el-input
                v-model="basicForm.description"
                type="textarea"
                rows="4"
                placeholder="请输入系统描述"
              />
            </el-form-item>
            <el-form-item label="系统信息">
              <el-input
                v-model="basicForm.message"
                type="textarea"
                rows="4"
                placeholder="请输入系统信息"
              />
            </el-form-item>
            <el-form-item label="技术支持">
              <el-input
                v-model="basicForm.technology"
                type="textarea"
                rows="4"
                placeholder="请输入技术支持信息"
              />
            </el-form-item>
          </el-form>
        </el-tab-pane>

        <!-- 邮件设置 -->
        <el-tab-pane label="邮件设置" name="email">
          <el-form :model="emailForm" label-width="120px">
            <el-form-item label="SMTP服务器">
              <el-input v-model="emailForm.smtpServer" placeholder="请输入SMTP服务器地址" />
            </el-form-item>
            <el-form-item label="SMTP端口">
              <el-input v-model="emailForm.smtpPort" placeholder="请输入SMTP端口" />
            </el-form-item>
            <el-form-item label="发件人邮箱">
              <el-input v-model="emailForm.senderEmail" placeholder="请输入发件人邮箱" />
            </el-form-item>
            <el-form-item label="邮箱密码">
              <el-input v-model="emailForm.password" type="password" placeholder="请输入邮箱密码" />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="testEmailConnection">测试连接</el-button>
            </el-form-item>
          </el-form>
        </el-tab-pane>

        <!-- 安全设置 -->
        <el-tab-pane label="安全设置" name="security">
          <el-form :model="securityForm" label-width="120px">
            <el-form-item label="登录失败次数">
              <el-input-number v-model="securityForm.maxLoginAttempts" :min="1" :max="10" />
            </el-form-item>
            <el-form-item label="锁定时间(分钟)">
              <el-input-number v-model="securityForm.lockDuration" :min="5" :max="60" />
            </el-form-item>
            <el-form-item label="密码有效期(天)">
              <el-input-number v-model="securityForm.passwordExpiry" :min="30" :max="180" />
            </el-form-item>
            <el-form-item label="启用验证码">
              <el-switch v-model="securityForm.enableCaptcha" />
            </el-form-item>
            <el-form-item label="启用双因素认证">
              <el-switch v-model="securityForm.enable2FA" />
            </el-form-item>
          </el-form>
        </el-tab-pane>
      </el-tabs>

      <div class="settings-footer">
        <el-button type="primary" @click="saveSettings">保存设置</el-button>
      </div>
    </el-card>
  </div>
</template>

<script>
import { ref, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Plus } from '@element-plus/icons-vue'
import { post } from '@/utils/request'

export default {
  name: 'Settings',
  components: {
    Plus
  },
  setup() {
    const activeTab = ref('basic')
    const systemId = ref(null)

    const basicForm = ref({
      systemName: '',
      logo: '',
      description: '',
      message: '',
      technology: ''
    })

    const emailForm = ref({
      smtpServer: '',
      smtpPort: '',
      senderEmail: '',
      password: ''
    })

    const securityForm = ref({
      maxLoginAttempts: 5,
      lockDuration: 30,
      passwordExpiry: 90,
      enableCaptcha: true,
      enable2FA: false
    })

    // 获取系统设置数据
    const getSystemSettings = async () => {
      try {
        const res = await post('/system/')
        if (res.code === 200 && res.data.length > 0) {
          const systemData = res.data[0]
          systemId.value = systemData.system_id
          basicForm.value = {
            systemName: systemData.system_name,
            logo: systemData.system_logo,
            description: systemData.system_content,
            message: systemData.system_message,
            technology: systemData.system_technology
          }
        }
      } catch (error) {
        console.error('获取系统设置失败：', error)
        ElMessage.error('获取系统设置失败')
      }
    }

    const handleLogoSuccess = (res) => {
      basicForm.value.logo = res.url
      ElMessage.success('Logo上传成功')
    }

    const beforeLogoUpload = (file) => {
      const isImage = file.type.startsWith('image/')
      const isLt2M = file.size / 1024 / 1024 < 2

      if (!isImage) {
        ElMessage.error('只能上传图片文件!')
      }
      if (!isLt2M) {
        ElMessage.error('上传图片大小不能超过 2MB!')
      }
      return isImage && isLt2M
    }

    const testEmailConnection = () => {
      // TODO: 实现邮件连接测试
      ElMessage.success('邮件服务器连接测试成功')
    }

    const saveSettings = () => {
      // TODO: 实现保存设置
      ElMessage.success('设置保存成功')
    }

    // 组件挂载时获取数据
    onMounted(() => {
      getSystemSettings()
    })

    return {
      activeTab,
      basicForm,
      emailForm,
      securityForm,
      handleLogoSuccess,
      beforeLogoUpload,
      testEmailConnection,
      saveSettings
    }
  }
}
</script>

<style scoped>
.settings-container {
  padding: 20px;
}

.settings-card {
  margin: 0 auto;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.logo-uploader {
  border: 1px dashed var(--el-border-color);
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: var(--el-transition-duration-fast);
}

.logo-uploader:hover {
  border-color: var(--el-color-primary);
}

.logo-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 178px;
  height: 178px;
  text-align: center;
  line-height: 178px;
}

.logo {
  width: 178px;
  height: 178px;
  display: block;
}

.settings-footer {
  margin-top: 20px;
  text-align: center;
}

:deep(.el-tabs__content) {
  padding: 20px 0;
}
</style> 
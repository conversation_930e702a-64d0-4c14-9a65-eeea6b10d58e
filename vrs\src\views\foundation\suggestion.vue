<!-- 反馈建议页面 -->

<template>
  <div class="suggestion-container">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <span>反馈建议管理</span>
        </div>
      </template>
      
      <el-table :data="tableData" style="width: 100%" v-loading="loading">
        <el-table-column prop="message_id" label="ID" width="80" />
        <el-table-column prop="user_id" label="用户ID" width="100" />
        <el-table-column prop="nick_name" label="用户昵称" width="120" />
        <el-table-column prop="message_content" label="内容" show-overflow-tooltip />
        <el-table-column prop="message_peply" label="回复内容" show-overflow-tooltip />
        <el-table-column label="状态" width="100">
          <template #default="scope">
            <el-tag :type="scope.row.message_peply ? 'success' : 'warning'">
              {{ scope.row.message_peply ? '已回复' : '待回复' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="250" fixed="right">
          <template #default="scope">
            <el-button-group>
              <el-button type="primary" link @click="handleView(scope.row)">查看</el-button>
              <el-button type="success" link @click="handleReply(scope.row)" v-if="!scope.row.message_peply">回复</el-button>
              <el-button type="warning" link @click="handleEdit(scope.row)" v-if="scope.row.message_peply">修改</el-button>
              <el-button type="danger" link @click="handleDelete(scope.row)">删除</el-button>
            </el-button-group>
          </template>
        </el-table-column>
      </el-table>

      <div class="pagination-container">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 30, 50]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 查看对话框 -->
    <el-dialog
      v-model="viewDialogVisible"
      width="600px"
    >
      <div class="feedback-detail">
        <h3>{{ currentFeedback.title }}</h3>
        <div class="feedback-info">
          <span>提交人：{{ currentFeedback.user_name }}</span>
        </div>
        <div class="feedback-content">
          <p>{{ currentFeedback.content }}</p>
        </div>
        <div class="feedback-reply" v-if="currentFeedback.reply">
          <h4>回复内容：</h4>
          <p>{{ currentFeedback.reply }}</p>

        </div>
      </div>
    </el-dialog>

    <!-- 回复对话框 -->
    <el-dialog
      v-model="replyDialogVisible"
      title="回复反馈"
      width="500px"
    >
      <el-form :model="replyForm" :rules="rules" ref="replyFormRef" label-width="80px">
        <el-form-item label="回复内容" prop="reply">
          <el-input
            v-model="replyForm.reply"
            type="textarea"
            :rows="4"
            placeholder="请输入回复内容"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="replyDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSubmitReply">确定</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 修改回复对话框 -->
    <el-dialog
      v-model="editDialogVisible"
      title="修改回复"
      width="500px"
    >
      <el-form :model="editForm" :rules="rules" ref="editFormRef" label-width="80px">
        <el-form-item label="回复内容" prop="reply">
          <el-input
            v-model="editForm.reply"
            type="textarea"
            :rows="4"
            placeholder="请输入回复内容"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="editDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSubmitEdit">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { post } from '@/utils/request'

const loading = ref(false)
const tableData = ref([])
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)
const viewDialogVisible = ref(false)
const replyDialogVisible = ref(false)
const editDialogVisible = ref(false)
const currentFeedback = ref({})
const replyFormRef = ref(null)
const editFormRef = ref(null)

const replyForm = ref({
  id: '',
  reply: ''
})

const editForm = ref({
  id: '',
  reply: ''
})

const rules = {
  reply: [
    { required: false, validator: (rule, value, callback) => {
      if (!value || value.trim() === '') {
        callback();
      } else if (value.length < 2 || value.length > 500) {
        callback(new Error('长度在 2 到 500 个字符'));
      } else {
        callback();
      }
    }, trigger: 'blur' }
  ]
}

// 获取状态类型
const getStatusType = (status) => {
  const types = {
    0: 'warning',
    1: 'success',
    2: 'danger'
  }
  return types[status] || 'info'
}

// 获取状态文本
const getStatusText = (status) => {
  const texts = {
    0: '待回复',
    1: '已回复',
    2: '已关闭'
  }
  return texts[status] || '未知'
}

// 获取列表数据
const getList = async () => {
  loading.value = true
  try {
    const res = await post('/message/')
    if (res.code === 200) {
      tableData.value = res.result
      total.value = res.result.length
    }
  } catch (error) {
    console.error(error)
    ElMessage.error('获取数据失败')
  } finally {
    loading.value = false
  }
}

// 查看详情
const handleView = (row) => {
  currentFeedback.value = {
    title: `反馈建议 #${row.message_id}`,
    user_name: `用户${row.user_id}`,
    content: row.message_content,
    reply: row.message_peply
  }
  viewDialogVisible.value = true
}

// 回复
const handleReply = (row) => {
  replyForm.value = {
    id: row.message_id,
    reply: ''
  }
  replyDialogVisible.value = true
}

// 提交回复
const handleSubmitReply = async () => {
  if (!replyFormRef.value) return
  await replyFormRef.value.validate(async (valid) => {
    if (valid) {
      try {
        const res = await post('/message/reply', {
          message_id: replyForm.value.id,
          message_peply: replyForm.value.reply
        })
        
        if (res.code === 200) {
          ElMessage.success('回复成功')
          replyDialogVisible.value = false
          getList()
        }
      } catch (error) {
        console.error(error)
        ElMessage.error('回复失败')
      }
    }
  })
}

// 删除
const handleDelete = (row) => {
  ElMessageBox.confirm('确认删除该反馈吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      const res = await post('/message/delete', {
        message_id: row.message_id
      })
      if (res.code === 200) {
        ElMessage.success('删除成功')
        getList()
      }
    } catch (error) {
      console.error(error)
      ElMessage.error('删除失败')
    }
  })
}

// 修改回复
const handleEdit = (row) => {
  editForm.value = {
    id: row.message_id,
    reply: row.message_peply
  }
  editDialogVisible.value = true
}

// 提交修改
const handleSubmitEdit = async () => {
  if (!editFormRef.value) return
  await editFormRef.value.validate(async (valid) => {
    if (valid) {
      try {
        const res = await post('/message/reply', {
          message_id: editForm.value.id,
          message_peply: editForm.value.reply || '' // 允许空字符串
        })
        
        if (res.code === 200) {
          ElMessage.success('修改成功')
          editDialogVisible.value = false
          getList()
        }
      } catch (error) {
        console.error(error)
        ElMessage.error('修改失败')
      }
    }
  })
}

// 分页
const handleSizeChange = (val) => {
  pageSize.value = val
  getList()
}

const handleCurrentChange = (val) => {
  currentPage.value = val
  getList()
}

onMounted(() => {
  getList()
})
</script>

<style scoped>
.suggestion-container {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

.feedback-detail {
  padding: 0 20px;
}

.feedback-detail h3 {
  margin: 0 0 15px 0;
  color: var(--el-text-color-primary);
}

.feedback-info {
  display: flex;
  gap: 20px;
  color: var(--el-text-color-secondary);
  font-size: 14px;
  margin-bottom: 15px;
}

.feedback-content {
  padding: 15px;
  background: var(--el-fill-color-lighter);
  border-radius: 4px;
  margin-bottom: 20px;
}

.feedback-content p {
  margin: 0;
  line-height: 1.6;
}

.feedback-reply {
  border-top: 1px solid var(--el-border-color-lighter);
  padding-top: 15px;
}

.feedback-reply h4 {
  margin: 0 0 10px 0;
  color: var(--el-text-color-primary);
}

</style> 

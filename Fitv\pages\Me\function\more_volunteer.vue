<template>
	<view class="volunteer-container">

		<!-- 志愿列表 -->
		<view class="volunteer-list" v-if="!loading">
			<view v-if="volunteerList.length === 0" class="no-data">
				<uni-icons type="info" size="40" color="#ccc"></uni-icons>
				<text class="no-data-text">暂无志愿记录</text>
				<button class="add-btn" @click="navigateToInsert">立即填报</button>
			</view>
			
			<view v-else>
				<uni-card 
					v-for="(item, index) in volunteerList" 
					:key="index" 
					:title="'志愿' + (index + 1)" 
					:extra="getStatusText(item.admission_state)"
					@click="viewVolunteerDetail(item)"
				>
					<view class="volunteer-item">
						<view class="school-info">
							<image 
								:src="item.school_image || '/static/c1.png'" 
								class="school-logo"
								mode="aspectFill"
							></image>
							<view class="info">
								<text class="school-name">{{item.school_name}}</text>
								<text class="specialized-name">{{item.specialized_content || '暂无专业信息'}}</text>
								<text class="specialized-part" v-if="item.specialized_part">{{item.specialized_part}}</text>
							</view>
							<view class="status-tag" :class="getStatusClass(item.admission_state)">
								{{getStatusText(item.admission_state)}}
							</view>
						</view>
						
						<view class="volunteer-details">
							<view class="detail-item" v-if="item.user_fraction">
								<text class="major-score">分数线: {{item.min_scores || '未知'}}</text>
							</view>
							<view class="detail-item" v-if="item.Score_line">
								<text class="label">分数线：</text>
								<text class="value">{{item.Score_line}}</text>
							</view>
						</view>
						
						<view class="action-buttons">
							<button class="detail-btn" @click.stop="viewVolunteerDetail(item)">查看详情</button>
							<button class="cancel-btn" @click.stop="cancelVolunteer(item)">取消志愿</button>
						</view>
					</view>
				</uni-card>
			</view>
		</view>

		<!-- 加载状态 -->
		<view v-if="loading" class="loading-container">
			<uni-load-more status="loading"></uni-load-more>
		</view>
		
		<!-- 底部操作 -->
		<view class="bottom-actions" v-if="!loading">
			<button class="add-more-btn" @click="navigateToInsert">
				<uni-icons type="plus" size="16" color="#fff"></uni-icons>
				<text>添加更多志愿</text>
			</button>
		</view>
	</view>
</template>

<script>
	import request from '../../../utils/request.js';
	import { getUserId } from '../../../utils/user.js';

	export default {
		data() {
			return {
				loading: true,
				volunteerList: [],
				userId: null
			}
		},
		
		onLoad() {
			this.getUserInfo();
		},
		
		onShow() {
			// 页面显示时刷新数据
			if (this.userId) {
				this.getVolunteerList();
			}
		},
		
		methods: {
			// 获取用户信息
			getUserInfo() {
				this.userId = getUserId({ showLoginPrompt: true });
				if (this.userId) {
					this.getVolunteerList();
				} else {
					this.loading = false;
				}
			},
			
			// 获取志愿列表
			getVolunteerList() {
				if (!this.userId) {
					console.warn('用户ID为空，无法获取志愿信息');
					this.loading = false;
					return;
				}
				
				this.loading = true;
				
				request.post('/admission/getUserAdmissions', {
					user_id: this.userId
				})
				.then(res => {
					console.log('获取志愿列表成功:', res);
					
					if (res.code === 200 && res.result) {
						this.volunteerList = res.result;
						// 处理数据，确保字段存在
						this.volunteerList.forEach(item => {
							if (!item.specialized_part && item.school_characteristic) {
								item.specialized_part = item.school_characteristic;
							}
						});
					} else {
						this.volunteerList = [];
						console.warn('获取志愿列表失败:', res.message);
					}
				})
				.catch(err => {
					console.error('获取志愿列表请求失败:', err);
					this.volunteerList = [];
					uni.showToast({
						title: '获取数据失败',
						icon: 'none'
					});
				})
				.finally(() => {
					this.loading = false;
				});
			},
			
			// 获取状态文本
			getStatusText(status) {
				if (typeof status === 'string') {
					return status;
				}
				switch(status) {
					case '1': return '已录取';
					case '0': return '待录取';
					case '2': return '未录取';
					default: return '待录取';
				}
			},
			
			// 获取状态样式类
			getStatusClass(status) {
				const statusText = this.getStatusText(status);
				if (statusText.includes('已录取')) {
					return 'admitted';
				} else if (statusText.includes('未录取')) {
					return 'rejected';
				} else {
					return 'pending';
				}
			},
			
			// 查看志愿详情
			viewVolunteerDetail(item) {
				uni.navigateTo({
					url: `/pages/Me/function/Volunteer_details?id=${item.admission_id}`
				});
			},
			
			// 取消志愿
			cancelVolunteer(item) {
				uni.showModal({
					title: '确认取消',
					content: `确定要取消 ${item.school_name} 的志愿申请吗？`,
					confirmText: '确定取消',
					cancelText: '保留',
					confirmColor: '#ff3b30',
					success: (res) => {
						if (res.confirm) {
							this.performCancelVolunteer(item);
						}
					}
				});
			},
			
			// 执行取消志愿
			performCancelVolunteer(item) {
				request.post('/admission/delete', {
					admission_id: item.admission_id
				})
				.then(res => {
					console.log('取消志愿成功:', res);
					if (res.code === 200) {
						uni.showToast({
							title: '志愿已取消',
							icon: 'success'
						});
						// 刷新志愿列表
						this.getVolunteerList();
					} else {
						uni.showToast({
							title: res.message || '取消失败',
							icon: 'none'
						});
					}
				})
				.catch(err => {
					console.error('取消志愿失败:', err);
					uni.showToast({
						title: '取消失败，请重试',
						icon: 'none'
					});
				});
			},
			
			// 跳转到志愿填报页面
			navigateToInsert() {
				uni.navigateTo({
					url: '/pages/Volunteer/function/insert'
				});
			}
		}
	}
</script>

<style scoped>
.volunteer-container {
	background: #f8f9fa;
	min-height: 100vh;
	padding-bottom: 80px;
}
.volunteer-list {
	padding: 16px;
}

.no-data {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 60px 0;
	color: #999;
}

.no-data-text {
	margin: 12px 0;
	font-size: 14px;
}

.add-btn {
	background: #d4237a;
	color: #fff;
	border: none;
	border-radius: 20px;
	padding: 8px 20px;
	font-size: 14px;
}

.volunteer-item {
	padding: 0;
}

.school-info {
	display: flex;
	align-items: center;
	margin-bottom: 12px;
}

.school-logo {
	width: 50px;
	height: 50px;
	border-radius: 8px;
	margin-right: 12px;
}

.info {
	flex: 1;
	display: flex;
	flex-direction: column;
	gap: 4px;
}

.school-name {
	font-size: 16px;
	font-weight: bold;
	color: #333;
}

.specialized-name {
	font-size: 14px;
	color: #666;
}

.specialized-part {
	font-size: 12px;
	color: #d4237a;
}

.status-tag {
	padding: 4px 8px;
	border-radius: 12px;
	font-size: 12px;
	font-weight: bold;
}

.status-tag.admitted {
	background: #e8f5e8;
	color: #52c41a;
}

.status-tag.pending {
	background: #fff7e6;
	color: #fa8c16;
}

.status-tag.rejected {
	background: #fff2f0;
	color: #ff4d4f;
}

.volunteer-details {
	display: flex;
	gap: 16px;
	margin-bottom: 12px;
}

.detail-item {
	display: flex;
	align-items: center;
	gap: 4px;
}

.label {
	font-size: 12px;
	color: #999;
}

.value {
	font-size: 12px;
	color: #333;
	font-weight: bold;
}

.action-buttons {
	display: flex;
	gap: 8px;
}

.detail-btn {
	flex: 1;
	background: #f0f0f0;
	color: #666;
	border: none;
	border-radius: 4px;
	padding: 6px 12px;
	font-size: 12px;
}

.cancel-btn {
	flex: 1;
	background: #fff2f0;
	color: #ff4d4f;
	border: none;
	border-radius: 4px;
	padding: 6px 12px;
	font-size: 12px;
}

.loading-container {
	padding: 20px;
}

.bottom-actions {
	position: fixed;
	bottom: 0;
	left: 0;
	right: 0;
	background: #fff;
	padding: 16px;
	border-top: 1px solid #f0f0f0;
}

.add-more-btn {
	width: 100%;
	background: #d4237a;
	color: #fff;
	border: none;
	border-radius: 8px;
	padding: 12px;
	font-size: 16px;
	display: flex;
	align-items: center;
	justify-content: center;
	gap: 8px;
}
</style>




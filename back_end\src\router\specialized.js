// 专业接口

const express = require('express');
const router = express.Router();
const db = require('../config/db');
const multer = require('multer');
const path = require('path');
const fs = require('fs');

// 配置multer存储
const storage = multer.diskStorage({
    destination: function (req, file, cb) {
        const uploadDir = path.join(__dirname, '../../public/uploads');
        // 确保上传目录存在
        if (!fs.existsSync(uploadDir)) {
            fs.mkdirSync(uploadDir, { recursive: true });
        }
        cb(null, uploadDir);
    },
    filename: function (req, file, cb) {
        // 生成唯一文件名
        const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
        const ext = path.extname(file.originalname);
        cb(null, uniqueSuffix + ext);
    }
});

// 文件过滤器，只允许上传图片
const fileFilter = (req, file, cb) => {
    const allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
    if (allowedTypes.includes(file.mimetype)) {
        cb(null, true);
    } else {
        cb(new Error('只允许上传JPG, PNG, GIF, WEBP格式的图片!'), false);
    }
};

// 创建multer实例
const upload = multer({ 
    storage: storage,
    fileFilter: fileFilter,
    limits: {
        fileSize: 5 * 1024 * 1024 // 限制文件大小为5MB
    }
});

// 获取所有专业类型
router.post('/', (req, res) => {
    const sql = 'SELECT * FROM specialized';
    
    db.query(sql, (err, result) => {
        if (err) {
            res.status(500).send({
                code: 201,
                success: "失败",
                message: "查询失败",
                error: err.message
            });
        } else {
            res.send({
                code: 200,
                success: "成功",
                message: "查询成功",
                result: result
            });
        }
    });
});

// 新增专业类型
router.post('/add', (req, res) => {
    const { specialized_content } = req.body;
    
    // 参数验证
    if (!specialized_content) {
        return res.status(400).send({
            code: 201,
            success: "失败",
            message: "专业内容不能为空"
        });
    }

    const sql = 'INSERT INTO specialized (specialized_content) VALUES (?)';
    
    db.query(sql, [specialized_content], (err, result) => {
        if (err) {
            return res.status(500).send({
                code: 201,
                success: "失败",
                message: "添加失败",
                error: err.message
            });
        }

        res.send({
            code: 200,
            success: "成功",
            message: "添加成功",
            result: {
                specialized_id: result.insertId,
                specialized_content
            }
        });
    });
});

// 查询学校的专业数量
router.post('/getSchoolSpecialized', (req, res) => {
    const sql = `
        SELECT 
            s.school_id,
            s.school_name,
            s.school_image,
            COUNT(ss.school_specialized) as specialized_count,
            GROUP_CONCAT(sp.specialized_content) as specialized_contents
        FROM school s
        LEFT JOIN school_specialized ss ON s.school_id = ss.school_id
        LEFT JOIN specialized sp ON ss.specialized_id = sp.specialized_id
        GROUP BY s.school_id, s.school_name, s.school_image
        ORDER BY s.school_id ASC
    `;
    
    db.query(sql, (err, result) => {
        if (err) {
            res.status(500).send({
                code: 201,
                success: "失败",
                message: "查询失败",
                error: err.message
            });
        } else {
            res.send({
                code: 200,
                success: "成功",
                message: "查询成功",
                result: result
            });
        }
    });
});

// 查询指定学校的专业
router.post('/getSchoolSpecializedById', (req, res) => {
    const { school_id } = req.body;
    
    if (!school_id) {
        return res.status(400).send({
            code: 201,
            success: "失败",
            message: "学校ID不能为空"
        });
    }

    const sql = `
        SELECT 
            s.school_id,
            s.school_name,
            COUNT(ss.school_specialized) as specialized_count,
            GROUP_CONCAT(sp.specialized_content) as specialized_contents
        FROM school s
        LEFT JOIN school_specialized ss ON s.school_id = ss.school_id
        LEFT JOIN specialized sp ON ss.specialized_id = sp.specialized_id
        WHERE s.school_id = ?
        GROUP BY s.school_id, s.school_name
    `;
    
    db.query(sql, [school_id], (err, result) => {
        if (err) {
            res.status(500).send({
                code: 201,
                success: "失败",
                message: "查询失败",
                error: err.message
            });
        } else {
            res.send({
                code: 200,
                success: "成功",
                message: "查询成功",
                result: result
            });
        }
    });
});

//删除这个专业类型
router.post('/delete',(req,res)=>{
    const { specialized_id } = req.body;
    
    if (!specialized_id) {
        return res.status(400).send({
            code: 201,
            success: "失败",
            message: "专业ID不能为空"
        });
    }

    const sql = 'DELETE FROM specialized WHERE specialized_id = ?';
    db.query(sql, [specialized_id], (err, result) => {
        if (err) {
            res.status(500).send({
                code: 201,
                success: "失败",
                message: "删除失败",
                error: err.message
            });
        } else if (result.affectedRows === 0) {
            res.status(404).send({
                code: 201,
                success: "失败",
                message: "未找到该专业类型"
            });
        } else {
            res.send({
                code: 200,
                success: "成功",
                message: "删除成功"
            });
        }
    });
});

// 修改专业类型
router.post('/update', (req, res) => {
    const { specialized_id, specialized_content } = req.body;
    
    if (!specialized_id) {
        return res.status(400).send({
            code: 201,
            success: "失败",
            message: "专业ID不能为空"
        });
    }

    const sql = 'UPDATE specialized SET specialized_content = ? WHERE specialized_id = ?';
    
    db.query(sql, [specialized_content, specialized_id], (err, result) => {
        if (err) {
            return res.status(500).send({
                code: 201,
                success: "失败",
                message: "修改失败",
                error: err.message
            });
        }

        if (result.affectedRows === 0) {
            return res.status(404).send({
                code: 201,
                success: "失败",
                message: "未找到该专业类型"
            });
        }

        res.send({
            code: 200,
            success: "成功",
            message: "修改成功"
        });
    });
});

// 查询专业详情
router.post('/getSpecializedDetail', (req, res) => {
    const { specialized_id } = req.body;
    
    // 参数验证
    if (!specialized_id) {
        return res.status(400).send({
            code: 201,
            success: "失败",
            message: "专业ID不能为空"
        });
    }
    
    const sql = `
        SELECT 
            s.specialized_id,
            s.specialized_content,
            sd.specialized_part,
            sd.specialized_degree,
            sd.specialized_type,
            sd.specialized_introduce,
            sd.specialized_course,
            sd.specialized_employment,
            sd.specialized_image
        FROM specialized s
        LEFT JOIN specialized_detail sd ON s.specialized_id = sd.specialized_id
        WHERE s.specialized_id = ?
    `;
    
    db.query(sql, [specialized_id], (err, result) => {
        if (err) {
            console.error('数据库查询错误:', err);
            return res.status(500).send({
                code: 201,
                success: "失败",
                message: "查询失败",
                error: err.message
            });
        }
        
        if (result.length === 0) {
            return res.status(404).send({
                code: 201,
                success: "失败",
                message: "未找到该专业信息"
            });
        }
        
        res.send({
            code: 200,
            success: "成功",
            message: "查询成功",
            result: result[0]
        });
    });
});

// 添加专业类型和详情
router.post('/addWithDetail', (req, res) => {
    const { 
        specialized_content,
        specialized_part,
        specialized_degree,
        specialized_type,
        specialized_introduce,
        specialized_course,
        specialized_employment,
        specialized_image
    } = req.body;
    
    // 参数验证
    if (!specialized_content) {
        return res.status(400).send({
            code: 201,
            success: "失败",
            message: "专业内容不能为空"
        });
    }

    // 1. 插入专业类型
    const insertSpecializedSql = 'INSERT INTO specialized (specialized_content) VALUES (?)';
    
    db.query(insertSpecializedSql, [specialized_content], (err, specializedResult) => {
        if (err) {
            return res.status(500).send({
                code: 201,
                success: "失败",
                message: "添加专业类型失败",
                error: err.message
            });
        }

        const specialized_id = specializedResult.insertId;

        // 2. 插入专业详情
        const insertDetailSql = `
            INSERT INTO specialized_detail (
                specialized_id,
                specialized_part,
                specialized_degree,
                specialized_type,
                specialized_introduce,
                specialized_course,
                specialized_employment,
                specialized_image
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        `;

        db.query(
            insertDetailSql, 
            [
                specialized_id,
                specialized_part || '',
                specialized_degree || '',
                specialized_type || '',
                specialized_introduce || '',
                specialized_course || '',
                specialized_employment || '',
                specialized_image || ''
            ], 
            (err, detailResult) => {
                if (err) {
                    // 如果添加详情失败，尝试删除已添加的专业类型
                    const deleteSpecializedSql = 'DELETE FROM specialized WHERE specialized_id = ?';
                    db.query(deleteSpecializedSql, [specialized_id], () => {
                        // 无论删除成功与否，都返回错误信息
                        res.status(500).send({
                            code: 201,
                            success: "失败",
                            message: "添加专业详情失败",
                            error: err.message
                        });
                    });
                    return;
                }

                // 添加成功
                res.send({
                    code: 200,
                    success: "成功",
                    message: "添加专业类型和详情成功",
                    result: {
                        specialized_id,
                        specialized_content,
                        specialized_part,
                        specialized_degree,
                        specialized_type,
                        specialized_introduce,
                        specialized_course,
                        specialized_employment,
                        specialized_image
                    }
                });
            }
        );
    });
});

// 修改专业类型和详情
router.post('/updateWithDetail', (req, res) => {
    const { 
        specialized_id,
        specialized_content,
        specialized_part,
        specialized_degree,
        specialized_type,
        specialized_introduce,
        specialized_course,
        specialized_employment,
        specialized_image
    } = req.body;
    
    if (!specialized_id) {
        return res.status(400).send({
            code: 201,
            success: "失败",
            message: "专业ID不能为空"
        });
    }

    // 1. 更新专业类型
    const updateSpecializedSql = 'UPDATE specialized SET specialized_content = ? WHERE specialized_id = ?';
    
    db.query(updateSpecializedSql, [specialized_content, specialized_id], (err, specializedResult) => {
        if (err) {
            return res.status(500).send({
                code: 201,
                success: "失败",
                message: "更新专业类型失败",
                error: err.message
            });
        }

        // 检查专业详情是否存在
        const checkDetailSql = 'SELECT * FROM specialized_detail WHERE specialized_id = ?';
        db.query(checkDetailSql, [specialized_id], (err, checkResult) => {
            if (err) {
                return res.status(500).send({
                    code: 201,
                    success: "失败",
                    message: "查询专业详情失败",
                    error: err.message
                });
            }

            let detailSql = '';
            let detailParams = [];

            if (checkResult.length > 0) {
                // 2a. 更新专业详情
                detailSql = `
                    UPDATE specialized_detail SET 
                    specialized_part = ?,
                    specialized_degree = ?,
                    specialized_type = ?,
                    specialized_introduce = ?,
                    specialized_course = ?,
                    specialized_employment = ?,
                    specialized_image = ?
                    WHERE specialized_id = ?
                `;
                detailParams = [
                    specialized_part || '',
                    specialized_degree || '',
                    specialized_type || '',
                    specialized_introduce || '',
                    specialized_course || '',
                    specialized_employment || '',
                    specialized_image || '',
                    specialized_id
                ];
            } else {
                // 2b. 插入专业详情
                detailSql = `
                    INSERT INTO specialized_detail (
                        specialized_id,
                        specialized_part,
                        specialized_degree,
                        specialized_type,
                        specialized_introduce,
                        specialized_course,
                        specialized_employment,
                        specialized_image
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                `;
                detailParams = [
                    specialized_id,
                    specialized_part || '',
                    specialized_degree || '',
                    specialized_type || '',
                    specialized_introduce || '',
                    specialized_course || '',
                    specialized_employment || '',
                    specialized_image || ''
                ];
            }

            db.query(detailSql, detailParams, (err, detailResult) => {
                if (err) {
                    return res.status(500).send({
                        code: 201,
                        success: "失败",
                        message: checkResult.length > 0 ? "更新专业详情失败" : "添加专业详情失败",
                        error: err.message
                    });
                }

                // 更新成功
                res.send({
                    code: 200,
                    success: "成功",
                    message: "更新专业类型和详情成功",
                    result: {
                        specialized_id,
                        specialized_content,
                        specialized_part,
                        specialized_degree,
                        specialized_type,
                        specialized_introduce,
                        specialized_course,
                        specialized_employment,
                        specialized_image
                    }
                });
            });
        });
    });
});

// 上传专业图片
router.post('/uploadImage', upload.single('image'), (req, res) => {
    if (!req.file) {
        return res.status(400).send({
            code: 201,
            success: "失败",
            message: "请选择要上传的图片"
        });
    }
    
    // 返回相对路径，不包含域名和端口
    const imageUrl = '/uploads/' + req.file.filename;
    
    res.send({
        code: 200,
        success: "成功",
        message: "图片上传成功",
        result: {
            imageUrl: imageUrl,
            filename: req.file.filename
        }
    });
});

// 获取所有专业类型和详情
router.post('/getAllWithDetail', (req, res) => {
    const sql = `
        SELECT 
            s.specialized_id,
            s.specialized_content,
            sd.specialized_part,
            sd.specialized_degree,
            sd.specialized_type,
            sd.specialized_introduce,
            sd.specialized_course,
            sd.specialized_employment,
            sd.specialized_image
        FROM specialized s
        LEFT JOIN specialized_detail sd ON s.specialized_id = sd.specialized_id
        ORDER BY s.specialized_id ASC
    `;
    
    db.query(sql, (err, result) => {
        if (err) {
            console.error('数据库查询错误:', err);
            return res.status(500).send({
                code: 201,
                success: "失败",
                message: "查询失败",
                error: err.message
            });
        }
        
        res.send({
            code: 200,
            success: "成功",
            message: "查询成功",
            result: result
        });
    });
});

// 获取用户志愿录取情况
router.post('/getUserAdmissionStatus', (req, res) => {
    const { user_id } = req.body;
    
    // 参数验证
    if (!user_id) {
        return res.status(400).send({
            code: 201,
            success: "失败",
            message: "用户ID不能为空"
        });
    }

    const sql = `
        SELECT 
            a.admission_id,
            a.user_id,
            a.school_id,
            a.specialized_id,
            a.admission_state,
            
            u.user_image,
            u.username,
            u.nick_name,
            u.sex,
            u.birth,
            u.phone,
            u.identity,
            u.user_school,
            u.user_fraction,
            u.user_form,
            
            s.school_name,
            s.school_image,
            s.school_phone,
            s.school_address,
            s.school_characteristic,
            s.school_idea,
            s.school_email,
            s.Score_line,
            s.school_number,
            
            sp.specialized_content,
            
            sd.specialized_part,
            sd.specialized_type,
            sd.specialized_introduce,
            sd.specialized_course,
            sd.specialized_employment,
            
            (SELECT min_scores FROM school_scores WHERE school_id = a.school_id LIMIT 1) as min_scores,
            (SELECT max_scores FROM school_scores WHERE school_id = a.school_id LIMIT 1) as max_scores,
            (SELECT year FROM school_scores WHERE school_id = a.school_id LIMIT 1) as year
        FROM admission a
        LEFT JOIN user u ON a.user_id = u.user_id
        LEFT JOIN school s ON a.school_id = s.school_id
        LEFT JOIN specialized sp ON a.specialized_id = sp.specialized_id
        LEFT JOIN specialized_detail sd ON sp.specialized_id = sd.specialized_id
        WHERE a.user_id = ?
    `;
    
    db.query(sql, [user_id], (err, result) => {
        if (err) {
            return res.status(500).send({
                code: 201,
                success: "失败",
                message: "查询失败",
                error: err.message
            });
        }
        
        res.send({
            code: 200,
            success: "成功",
            message: "查询成功",
            result: result
        });
    });
});

// 更新志愿录取状态（管理员使用）
router.post('/updateAdmissionStatus', (req, res) => {
    const { 
        admission_id, 
        admission_state
    } = req.body;
    
    // 参数验证
    if (!admission_id || !admission_state) {
        return res.status(400).send({
            code: 201,
            success: "失败",
            message: "录取ID和录取状态不能为空"
        });
    }

    const sql = `
        UPDATE admission 
        SET 
            admission_state = ?
        WHERE admission_id = ?
    `;
    
    db.query(sql, [
        admission_state, 
        admission_id
    ], (err, result) => {
        if (err) {
            return res.status(500).send({
                code: 201,
                success: "失败",
                message: "更新失败",
                error: err.message
            });
        }

        if (result.affectedRows === 0) {
            return res.status(404).send({
                code: 201,
                success: "失败",
                message: "未找到该录取记录"
            });
        }
        
        res.send({
            code: 200,
            success: "成功",
            message: "更新录取状态成功"
        });
    });
});

// 批量更新志愿录取状态（管理员使用）
router.post('/batchUpdateAdmissionStatus', (req, res) => {
    const { admissionData } = req.body;
    
    // 参数验证
    if (!admissionData || !Array.isArray(admissionData) || admissionData.length === 0) {
        return res.status(400).send({
            code: 201,
            success: "失败",
            message: "录取数据不能为空且必须是数组"
        });
    }
    
    // 使用事务确保批量更新的原子性
    db.pool().getConnection((err, connection) => {
        if (err) {
            return res.status(500).send({
                code: 201,
                success: "失败",
                message: "数据库连接失败",
                error: err.message
            });
        }

        // 开始事务
        connection.beginTransaction(err => {
            if (err) {
                connection.release();
                return res.status(500).send({
                    code: 201,
                    success: "失败",
                    message: "开始事务失败",
                    error: err.message
                });
            }

            // 创建一个Promise数组来处理每个更新
            const updatePromises = admissionData.map(item => {
                return new Promise((resolve, reject) => {
                    const { 
                        admission_id, 
                        admission_state
                    } = item;

                    if (!admission_id || !admission_state) {
                        return reject(new Error("录取ID和录取状态不能为空"));
                    }

                    const sql = `
                        UPDATE admission 
                        SET 
                            admission_state = ?
                        WHERE admission_id = ?
                    `;
                    
                    connection.query(sql, [
                        admission_state, 
                        admission_id
                    ], (err, result) => {
                        if (err) {
                            return reject(err);
                        }
                        resolve(result);
                    });
                });
            });

            // 执行所有更新
            Promise.all(updatePromises)
                .then(results => {
                    // 提交事务
                    connection.commit(err => {
                        if (err) {
                            return connection.rollback(() => {
                                connection.release();
                                res.status(500).send({
                                    code: 201,
                                    success: "失败",
                                    message: "提交事务失败",
                                    error: err.message
                                });
                            });
                        }
                        
                        connection.release();
                        res.send({
                            code: 200,
                            success: "成功",
                            message: "批量更新录取状态成功",
                            result: {
                                updatedCount: results.length
                            }
                        });
                    });
                })
                .catch(error => {
                    // 回滚事务
                    connection.rollback(() => {
                        connection.release();
                        res.status(500).send({
                            code: 201,
                            success: "失败",
                            message: "批量更新失败",
                            error: error.message
                        });
                    });
                });
        });
    });
});

// 获取单个志愿详情
router.post('/getAdmissionDetail', (req, res) => {
    const { admission_id } = req.body;
    
    // 参数验证
    if (!admission_id) {
        return res.status(400).send({
            code: 201,
            success: "失败",
            message: "志愿ID不能为空"
        });
    }

    const sql = `
        SELECT 
            a.admission_id,
            a.user_id,
            a.school_id,
            a.specialized_id,
            a.admission_state,
            
            u.user_image,
            u.username,
            u.nick_name,
            u.sex,
            u.birth,
            u.phone,
            u.identity,
            u.user_school,
            u.user_fraction,
            u.user_form,
            
            s.school_name,
            s.school_image,
            s.school_phone,
            s.school_address,
            s.school_characteristic,
            s.school_idea,
            s.school_email,
            s.Score_line,
            s.school_number,
            
            sp.specialized_content,
            
            sd.specialized_part,
            sd.specialized_type,
            sd.specialized_introduce,
            sd.specialized_course,
            sd.specialized_employment,
            
            (SELECT min_scores FROM school_scores WHERE school_id = a.school_id LIMIT 1) as min_scores,
            (SELECT max_scores FROM school_scores WHERE school_id = a.school_id LIMIT 1) as max_scores,
            (SELECT year FROM school_scores WHERE school_id = a.school_id LIMIT 1) as year
        FROM admission a
        LEFT JOIN user u ON a.user_id = u.user_id
        LEFT JOIN school s ON a.school_id = s.school_id
        LEFT JOIN specialized sp ON a.specialized_id = sp.specialized_id
        LEFT JOIN specialized_detail sd ON sp.specialized_id = sd.specialized_id
        WHERE a.admission_id = ?
    `;
    
    db.query(sql, [admission_id], (err, result) => {
        if (err) {
            return res.status(500).send({
                code: 201,
                success: "失败",
                message: "查询失败",
                error: err.message
            });
        }
        
        if (result.length === 0) {
            return res.status(404).send({
                code: 201,
                success: "失败",
                message: "未找到该志愿记录"
            });
        }
        
        res.send({
            code: 200,
            success: "成功",
            message: "查询成功",
            result: result[0]
        });
    });
});

module.exports = router;

// 收藏接口
const express = require('express');
const router = express.Router();
const db = require('../config/db');

// 获取用户收藏列表
router.post('/', (req, res) => {
    const { user_id } = req.body;
    
    if (!user_id) {
        return res.status(400).send({
            code: 201,
            success: "失败",
            message: "用户ID不能为空"
        });
    }
    
    const sql = `
        SELECT 
            c.collection_id,
            c.user_id,
            c.school_id,
            c.collection_time,
            s.school_name,
            s.school_image,
            s.school_address,
            s.Score_line as score_line,
            s.school_number
        FROM collection c
        LEFT JOIN school s ON c.school_id = s.school_id
        WHERE c.user_id = ?
        ORDER BY c.collection_time DESC
    `;
    
    db.query(sql, [user_id], (err, result) => {
        if (err) {
            res.status(500).send({
                code: 201,
                success: "失败",
                message: "查询收藏列表失败",
                error: err.message
            });
        } else {
            res.send({
                code: 200,
                success: "成功",
                message: "查询收藏列表成功",
                result: result
            });
        }
    });
});

// 添加收藏
router.post('/addCollection', (req, res) => {
    const { user_id, school_id } = req.body;
    
    if (!user_id || !school_id) {
        return res.status(400).send({
            code: 201,
            success: "失败",
            message: "用户ID和学校ID不能为空"
        });
    }
    
    // 先检查是否已经收藏
    const checkSql = 'SELECT * FROM collection WHERE user_id = ? AND school_id = ?';
    db.query(checkSql, [user_id, school_id], (err, result) => {
        if (err) {
            return res.status(500).send({
                code: 201,
                success: "失败",
                message: "检查收藏状态失败",
                error: err.message
            });
        }
        
        if (result.length > 0) {
            return res.send({
                code: 201,
                success: "失败",
                message: "该学校已在收藏列表中"
            });
        }
        
        // 添加收藏
        const insertSql = 'INSERT INTO collection (user_id, school_id, collection_time) VALUES (?, ?, NOW())';
        db.query(insertSql, [user_id, school_id], (insertErr, insertResult) => {
            if (insertErr) {
                res.status(500).send({
                    code: 201,
                    success: "失败",
                    message: "添加收藏失败",
                    error: insertErr.message
                });
            } else {
                res.send({
                    code: 200,
                    success: "成功",
                    message: "收藏成功",
                    collection_id: insertResult.insertId
                });
            }
        });
    });
});

// 取消收藏
router.post('/removeCollection', (req, res) => {
    const { user_id, school_id } = req.body;
    
    if (!user_id || !school_id) {
        return res.status(400).send({
            code: 201,
            success: "失败",
            message: "用户ID和学校ID不能为空"
        });
    }
    
    const sql = 'DELETE FROM collection WHERE user_id = ? AND school_id = ?';
    db.query(sql, [user_id, school_id], (err, result) => {
        if (err) {
            res.status(500).send({
                code: 201,
                success: "失败",
                message: "取消收藏失败",
                error: err.message
            });
        } else if (result.affectedRows === 0) {
            res.send({
                code: 201,
                success: "失败",
                message: "该学校不在收藏列表中"
            });
        } else {
            res.send({
                code: 200,
                success: "成功",
                message: "取消收藏成功"
            });
        }
    });
});

// 检查收藏状态
router.post('/checkCollection', (req, res) => {
    const { user_id, school_id } = req.body;
    
    if (!user_id || !school_id) {
        return res.status(400).send({
            code: 201,
            success: "失败",
            message: "用户ID和学校ID不能为空"
        });
    }
    
    const sql = 'SELECT collection_id FROM collection WHERE user_id = ? AND school_id = ?';
    db.query(sql, [user_id, school_id], (err, result) => {
        if (err) {
            res.status(500).send({
                code: 201,
                success: "失败",
                message: "检查收藏状态失败",
                error: err.message
            });
        } else {
            res.send({
                code: 200,
                success: "成功",
                message: "查询成功",
                is_collected: result.length > 0,
                collection_id: result.length > 0 ? result[0].collection_id : null
            });
        }
    });
});

module.exports = router;

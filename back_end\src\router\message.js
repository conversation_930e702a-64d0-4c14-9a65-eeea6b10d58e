// 用户消息接口

const express = require('express');
const router = express.Router();
const db = require('../config/db');
const multer = require('multer');
const path = require('path');
const fs = require('fs');

// 简化文件上传配置
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    const uploadDir = path.join(__dirname, '../../public/uploads');
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true });
    }
    cb(null, uploadDir);
  },
  filename: function (req, file, cb) {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    const ext = path.extname(file.originalname);
    cb(null, uniqueSuffix + ext);
  }
});

const upload = multer({ storage: storage });

router.post('/',(req,res)=>{
    // 使用左连接查询用户表获取用户姓名
    const sql = `
      SELECT m.*, u.nick_name, u.user_image
      FROM message m
      LEFT JOIN user u ON m.user_id = u.user_id
      ORDER BY m.message_id DESC
    `;
    
    db.query(sql,(err,result)=>{
        if(err){
            console.error('查询消息列表失败:', err);
            res.send({
                code: 201,
                success: "失败",
                message: err.message
            });
        } else {
            res.send({
                code: 200,
                success: "成功",
                result: result
            });
        }
    });
});

// 使用 any() 接收任意字段的文件
router.post('/add', upload.any(), (req, res) => {
  try {
    // 获取表单数据
    const { user_id, message_content } = req.body;
    
    // 处理上传的图片
    let message_image = null;
    let message_imageone = null;
    let message_imagetwo = null;
    
    // 处理所有上传的文件
    if (req.files && req.files.length > 0) {
      // 按照上传顺序分配到不同字段
      if (req.files[0]) message_image = '/uploads/' + req.files[0].filename;
      if (req.files[1]) message_imageone = '/uploads/' + req.files[1].filename;
      if (req.files[2]) message_imagetwo = '/uploads/' + req.files[2].filename;
    }
    
    // 插入数据库
    const sql = `
      INSERT INTO message 
      (user_id, message_content, message_image, message_imageone, message_imagetwo) 
      VALUES (?, ?, ?, ?, ?)
    `;
    
    db.query(sql, [
      user_id || null, 
      message_content || null, 
      message_image, 
      message_imageone, 
      message_imagetwo
    ], (err, result) => {
      if (err) {
        console.error('插入数据库失败:', err);
        res.send({
          code: 201,
          success: "失败",
          message: err.message
        });
      } else {
        res.send({
          code: 200,
          success: "成功",
          result: {
            message_id: result.insertId,
            user_id: user_id || null,
            message_content: message_content || null,
            message_image,
            message_imageone,
            message_imagetwo
          }
        });
      }
    });
  } catch (error) {
    console.error('处理请求失败:', error);
    res.send({
      code: 201,
      success: "失败",
      message: "服务器内部错误"
    });
  }
});

// 获取消息的评论列表
router.post('/comments', (req, res) => {
    const { message_id } = req.body;
    
    if (!message_id) {
        return res.send({
            code: 201,
            success: "失败",
            message: "消息ID不能为空"
        });
    }
    
    const sql = `
        SELECT c.*, u.nick_name, u.user_image
        FROM message_comments c
        LEFT JOIN user u ON c.user_id = u.user_id
        WHERE c.message_id = ?
        ORDER BY c.comment_id DESC
    `;
    
    db.query(sql, [message_id], (err, result) => {
        if (err) {
            res.send({
                code: 201,
                success: "失败",
                message: err.message
            });
        } else {
            res.send({
                code: 200,
                success: "成功",
                result: result
            });
        }
    });
});

// 发表评论
router.post('/addComment', upload.single('comment_image'), (req, res) => {
    const { message_id, user_id, comment_content } = req.body;
    
    if (!message_id || !user_id || !comment_content) {
        return res.send({
            code: 201,
            success: "失败",
            message: "消息ID、用户ID和评论内容不能为空"
        });
    }
    
    let comment_image = null;
    if (req.file) {
        comment_image = '/uploads/' + req.file.filename;
    }
    
    const sql = `
        INSERT INTO message_comments (message_id, user_id, comment_content, comment_image)
        VALUES (?, ?, ?, ?)
    `;
    
    db.query(sql, [message_id, user_id, comment_content, comment_image], (err, result) => {
        if (err) {
            res.send({
                code: 201,
                success: "失败",
                message: err.message
            });
        } else {
            res.send({
                code: 200,
                success: "成功",
                result: {
                    comment_id: result.insertId,
                    message_id,
                    user_id,
                    comment_content,
                    comment_image
                }
            });
        }
    });
});

// 更新消息的额外图片
router.post('/updateImages', upload.single('images'), (req, res) => {
    try {
        const { message_id, image_index } = req.body;
        
        if (!message_id || !req.file) {
            return res.send({
                code: 201,
                success: "失败",
                message: "消息ID和图片不能为空"
            });
        }
        
        const imageUrl = '/uploads/' + req.file.filename;
        let updateField = '';
        
        // 根据索引确定更新哪个字段
        switch(parseInt(image_index)) {
            case 1:
                updateField = 'message_imageone';
                break;
            case 2:
                updateField = 'message_imagetwo';
                break;
            default:
                return res.send({
                    code: 201,
                    success: "失败",
                    message: "无效的图片索引"
                });
        }
        
        const sql = `UPDATE message SET ${updateField} = ? WHERE message_id = ?`;
        
        db.query(sql, [imageUrl, message_id], (err, result) => {
            if (err) {
                res.send({
                    code: 201,
                    success: "失败",
                    message: err.message
                });
            } else {
                res.send({
                    code: 200,
                    success: "成功",
                    result: { message_id, imageUrl }
                });
            }
        });
    } catch (error) {
        res.send({
            code: 201,
            success: "失败",
            message: "服务器内部错误"
        });
    }
});

// 获取单个消息详情
router.post('/detail', (req, res) => {
    const { message_id } = req.body;
    
    if (!message_id) {
        return res.send({
            code: 201,
            success: "失败",
            message: "消息ID不能为空"
        });
    }
    
    const sql = `
        SELECT m.*, u.nick_name, u.user_image
        FROM message m
        LEFT JOIN user u ON m.user_id = u.user_id
        WHERE m.message_id = ?
    `;
    
    db.query(sql, [message_id], (err, result) => {
        if (err) {
            console.error('查询消息详情失败:', err);
            res.send({
                code: 201,
                success: "失败",
                message: err.message
            });
        } else if (result.length === 0) {
            res.send({
                code: 201,
                success: "失败",
                message: "消息不存在"
            });
        } else {
            res.send({
                code: 200,
                success: "成功",
                result: result[0]
            });
        }
    });
});

// 删除消息/评论
router.post('/delete', (req, res) => {
    const { message_id } = req.body;
    
    if (!message_id) {
        return res.send({
            code: 201,
            success: "失败",
            message: "消息ID不能为空"
        });
    }
    
    // 首先检查是消息还是评论
    const checkCommentSql = 'SELECT * FROM message_comments WHERE comment_id = ?';
    db.query(checkCommentSql, [message_id], (err, commentResult) => {
        if (err) {
            return res.send({
                code: 201,
                success: "失败",
                message: err.message
            });
        }
        
        // 如果是评论
        if (commentResult.length > 0) {
            const deleteSql = 'DELETE FROM message_comments WHERE comment_id = ?';
            db.query(deleteSql, [message_id], (err, result) => {
                if (err) {
                    console.error('删除评论失败:', err);
                    return res.send({
                        code: 201,
                        success: "失败",
                        message: err.message
                    });
                }
                
                if (result.affectedRows === 0) {
                    return res.send({
                        code: 201,
                        success: "失败",
                        message: "评论不存在"
                    });
                }
                
                res.send({
                    code: 200,
                    success: "成功",
                    message: "删除成功"
                });
            });
        } else {
            // 删除消息
            const deleteSql = 'DELETE FROM message WHERE message_id = ?';
            db.query(deleteSql, [message_id], (err, result) => {
                if (err) {
                    console.error('删除消息失败:', err);
                    return res.send({
                        code: 201,
                        success: "失败",
                        message: err.message
                    });
                }
                
                if (result.affectedRows === 0) {
                    return res.send({
                        code: 201,
                        success: "失败",
                        message: "消息不存在"
                    });
                }
                
                res.send({
                    code: 200,
                    success: "成功",
                    message: "删除成功"
                });
            });
        }
    });
});

// 修改消息/评论
router.post('/update', upload.any(), (req, res) => {
    const { message_id, message_content, comment_content } = req.body;
    
    if (!message_id) {
        return res.send({
            code: 201,
            success: "失败",
            message: "消息ID不能为空"
        });
    }
    
    // 首先检查是消息还是评论
    const checkSql = 'SELECT * FROM message_comments WHERE comment_id = ?';
    db.query(checkSql, [message_id], (err, commentResult) => {
        if (err) {
            return res.send({
                code: 201,
                success: "失败",
                message: err.message
            });
        }
        
        // 如果是评论
        if (commentResult.length > 0) {
            const updateFields = [];
            const values = [];
            
            // 更新评论内容 - 修复参数名
            if (message_content !== undefined) {
                updateFields.push('comment_content = ?');
                values.push(message_content);
            }
            
            // 处理评论图片
            if (req.files && req.files.length > 0) {
                updateFields.push('comment_image = ?');
                values.push('/uploads/' + req.files[0].filename);
            }
            
            if (updateFields.length === 0) {
                return res.send({
                    code: 201,
                    success: "失败",
                    message: "没有要更新的内容"
                });
            }
            
            values.push(message_id);
            const sql = `UPDATE message_comments SET ${updateFields.join(', ')} WHERE comment_id = ?`;
            
            db.query(sql, values, (err, result) => {
                if (err) {
                    return res.send({
                        code: 201,
                        success: "失败",
                        message: err.message
                    });
                }
                
                if (result.affectedRows === 0) {
                    return res.send({
                        code: 201,
                        success: "失败",
                        message: "评论不存在"
                    });
                }
                
                res.send({
                    code: 200,
                    success: "成功",
                    message: "修改成功",
                    result: {
                        imageUrl: req.files && req.files.length > 0 ? '/uploads/' + req.files[0].filename : null
                    }
                });
            });
        } else {
            // 原有的消息更新逻辑
            const updateFields = [];
            const values = [];
            
            if (message_content !== undefined) {
                updateFields.push('message_content = ?');
                values.push(message_content);
            }
            
            if (req.files && req.files.length > 0) {
                if (req.files[0]) {
                    updateFields.push('message_image = ?');
                    values.push('/uploads/' + req.files[0].filename);
                }
                if (req.files[1]) {
                    updateFields.push('message_imageone = ?');
                    values.push('/uploads/' + req.files[1].filename);
                }
                if (req.files[2]) {
                    updateFields.push('message_imagetwo = ?');
                    values.push('/uploads/' + req.files[2].filename);
                }
            }
            
            if (updateFields.length === 0) {
                return res.send({
                    code: 201,
                    success: "失败",
                    message: "没有要更新的内容"
                });
            }
            
            values.push(message_id);
            const sql = `UPDATE message SET ${updateFields.join(', ')} WHERE message_id = ?`;
            
            db.query(sql, values, (err, result) => {
                if (err) {
                    return res.send({
                        code: 201,
                        success: "失败",
                        message: err.message
                    });
                }
                
                if (result.affectedRows === 0) {
                    return res.send({
                        code: 201,
                        success: "失败",
                        message: "消息不存在"
                    });
                }
                
                res.send({
                    code: 200,
                    success: "成功",
                    message: "修改成功"
                });
            });
        }
    });
});

// 管理员回复消息
router.post('/reply', (req, res) => {
    const { message_id, message_peply } = req.body;
    
    if (!message_id) {
        return res.send({
            code: 201,
            success: "失败",
            message: "消息ID不能为空"
        });
    }
    
    const sql = `
        UPDATE message 
        SET message_peply = ? 
        WHERE message_id = ?
    `;
    
    db.query(sql, [message_peply || '', message_id], (err, result) => {
        if (err) {
            console.error('回复消息失败:', err);
            res.send({
                code: 201,
                success: "失败",
                message: err.message
            });
        } else if (result.affectedRows === 0) {
            res.send({
                code: 201,
                success: "失败",
                message: "消息不存在"
            });
        } else {
            res.send({
                code: 200,
                success: "成功",
                message: "回复成功"
            });
        }
    });
});

module.exports = router;

<!-- 评论 -->

<template>
	<view class="chat-container">
		<!-- 顶部分类 -->
		<scroll-view scroll-x="true" class="category-scroll">
			<view class="category-list">
				<view 
					v-for="(item, index) in categories" 
					:key="index" 
					:class="['category-item', { active: currentCategory === index }]"
					@click="selectCategory(index)"
				>
					{{item}}
				</view>
			</view>
		</scroll-view>
		
		<!-- 发布按钮 -->
		<view class="publish-btn" @click="publish">
			<uni-icons type="plus" size="20" color="#fff"></uni-icons>
		</view>
		
		<!-- 加载状态 -->
		<view class="loading-container" v-if="loading">
			<uni-icons type="spinner-cycle" size="30" color="#d4237a"></uni-icons>
			<text class="loading-text">加载中...</text>
		</view>
		
		<!-- 帖子列表 -->
		<scroll-view 
			scroll-y 
			class="post-list" 
			@scrolltolower="loadMore" 
			@refresherrefresh="onRefresh" 
			refresher-enabled 
			:refresher-triggered="refreshing"
		>
			<!-- 空状态 -->
			<view v-if="posts.length === 0 && !loading" class="empty-tips">
				<image src="/static/empty.png" mode="aspectFit" class="empty-image"></image>
				<text class="empty-text">暂无消息</text>
			</view>
			
			<!-- 帖子项 -->
			<view class="post-item" v-for="(item, index) in posts" :key="index">
				<!-- 用户信息 -->
				<view class="post-header">
					<image :src="getAvatarUrl(item.user_image)" class="avatar"></image>
					<view class="user-info">
						<text class="username">{{item.nick_name || '用户' + item.user_id}}</text>
						<text class="post-time">{{formatTimeByMessageId(item.message_id)}}</text>
					</view>
					<!-- 关注按钮 -->
					<view 
						v-if="item.user_id !== userId" 
						:class="['follow-btn', { followed: item.isFollowed }]"
						@click="followUser(index)"
					>
						{{item.isFollowed ? '已关注' : '关注'}}
					</view>
					<view v-else class="my-post-btn">我的发布</view>
				</view>
				
				<!-- 帖子内容 -->
				<view class="post-content" @click="goToDetail(item)">
					<text class="post-text">{{item.message_content}}</text>
					
					<!-- 管理员回复 -->
					<view class="admin-reply" v-if="item.message_peply">
						<view class="admin-reply-header">
							<uni-icons type="person-filled" size="16" color="#d4237a"></uni-icons>
							<text class="admin-label">管理员回复</text>
						</view>
						<text class="admin-reply-text">{{item.message_peply}}</text>
					</view>
					
					<view class="post-images" v-if="hasImages(item)">
						<image 
							v-for="(image, imgIndex) in getMessageImages(item)" 
							:key="imgIndex"
							:src="image" 
							mode="aspectFill" 
							class="post-image"
							@click.stop="previewImage(getMessageImages(item), imgIndex)"
						></image>
					</view>
				</view>
				
				<!-- 帖子标签 -->
				<view class="post-tags" v-if="item.tags && item.tags.length">
					<text class="tag" v-for="(tag, tagIndex) in item.tags" :key="tagIndex">#{{tag}}</text>
				</view>
				
				<!-- 互动栏 -->
				<view class="post-actions">
					<view class="action-item" @click="likePost(index)">
						<uni-icons 
							:type="item.isLiked ? 'heart-filled' : 'heart'" 
							size="18" 
							:color="item.isLiked ? '#d4237a' : '#666'"
						></uni-icons>
						<text :class="['action-text', { active: item.isLiked }]">{{item.likes || 0}}</text>
					</view>
					<view class="action-item" @click="commentPost(index)">
						<uni-icons type="chat" size="18" color="#666"></uni-icons>
						<text class="action-text">{{item.commentCount || 0}}</text>
					</view>
					<view class="action-item" @click="sharePost(index)">
						<uni-icons type="redo" size="18" color="#666"></uni-icons>
						<text class="action-text">分享</text>
					</view>
				</view>
				
				<!-- 评论区 -->
				<view class="comment-section" v-if="item.showComments">
					<view class="comment-item" v-for="(comment, cIndex) in item.comments" :key="cIndex">
						<image :src="comment.avatar" class="comment-avatar"></image>
						<view class="comment-content">
							<view class="comment-user">{{comment.username}}</view>
							<view class="comment-text">{{comment.content}}</view>
							<image 
								v-if="comment.image" 
								:src="comment.image" 
								mode="widthFix" 
								class="comment-image" 
								@click="previewImage([comment.image], 0)"
							></image>
							<view class="comment-time">{{comment.time}}</view>
						</view>
					</view>
					
					<!-- 评论输入框 -->
					<view class="comment-input-box">
						<view class="comment-input-wrapper">
							<input 
								type="text" 
								v-model="newComment" 
								placeholder="写评论..." 
								class="comment-input" 
							/>
							<view class="comment-image-btn" @click="chooseCommentImage">
								<uni-icons type="image" size="20" color="#666"></uni-icons>
							</view>
						</view>
						<button class="send-btn" @click="sendComment(item.message_id, index)">发送</button>
					</view>
					
					<!-- 评论图片预览 -->
					<view class="comment-preview" v-if="commentImage">
						<image :src="commentImage" mode="aspectFill" class="preview-image"></image>
						<view class="remove-image" @click="removeCommentImage">
							<uni-icons type="close" size="16" color="#fff"></uni-icons>
						</view>
					</view>
				</view>
			</view>
			
			<!-- 底部加载更多 -->
			<view class="load-more" v-if="posts.length > 0 && hasMore">
				<text class="load-more-text">加载更多...</text>
			</view>
		</scroll-view>
	</view>
</template>

<script>
	import request from '../../utils/request.js';
	import { getUserInfo, getUserId } from '../../utils/user.js';

	export default {
		data() {
			return {
				currentCategory: 0,
				categories: ['推荐', '院校', '专业', '考试', '志愿', '心得', '其他'],
				posts: [],
				loading: false,
				refreshing: false,
				userId: null,
				page: 1,
				pageSize: 10,
				hasMore: true,
				newComment: '',
				commentImage: ''
			}
		},
		onLoad() {
			// 获取用户信息
			this.initUserInfo();
			// 加载帖子
			this.loadPosts();
		},
		onShow() {
			this.checkRefreshFlag();
		},
		methods: {
			// 初始化用户信息
			initUserInfo() {
				this.userId = getUserId();
				if (this.userId) {
					console.log('使用用户ID:', this.userId);
				}
			},
			
			// 检查刷新标记
			checkRefreshFlag() {
				try {
					const needRefresh = uni.getStorageSync('needRefreshMessages');
					if (needRefresh === 'true') {
						console.log('检测到需要刷新消息数据');
						this.loadPosts(true);
						uni.removeStorageSync('needRefreshMessages');
					}
				} catch (e) {
					console.error('检查刷新标记失败:', e);
				}
			},
			
			// 加载帖子数据（使用网络请求）
			loadPosts(refresh = false) {
				if (refresh) {
					this.page = 1;
				}
				
				this.loading = true;
				
				// 构建请求参数
				const params = {
					page: this.page,
					pageSize: this.pageSize
				};
				
				// 如果不是"推荐"分类，添加标签筛选
				if (this.currentCategory > 0) {
					params.tag = this.categories[this.currentCategory];
				}
				
				// 调用API获取消息列表
				request.post('/message/', params)
				.then(res => {
					console.log('获取消息列表成功:', res);
					
					if (res.code === 200 && res.result) {
						// 处理返回的消息数据
						let messages = res.result;
						
						// 前端筛选（如果后端没有实现标签筛选）
						if (this.currentCategory > 0) {
							const selectedTag = this.categories[this.currentCategory];
							messages = messages.filter(message => {
								const tags = this.extractTags(message.message_content);
								return tags.includes(selectedTag) || 
									   message.message_content.includes(selectedTag) ||
									   this.matchCategoryKeywords(message.message_content, selectedTag);
							});
						}
						
						// 处理消息数据
						const processedPosts = messages.map(message => {
							// 生成随机的点赞数
							const likes = Math.floor(Math.random() * 100);
							
							// 从消息内容中提取标签
							const tags = this.extractTags(message.message_content);
							
							return {
								...message,
								isLiked: false,
								isFollowed: false,
								showComments: false,
								comments: [],
								commentCount: 0,
								likes: likes,
								tags: tags,
								commentsLoaded: false
							};
						});
						
						// 更新列表数据
						if (refresh) {
							this.posts = processedPosts;
						} else {
							this.posts = [...this.posts, ...processedPosts];
						}
						
						// 异步检查关注状态
						this.checkFollowStatus();
						
						// 异步加载每个帖子的评论数量
						this.loadCommentCounts();
						
						// 判断是否还有更多数据
						this.hasMore = processedPosts.length >= this.pageSize;
					} else {
						if (refresh || this.posts.length === 0) {
							this.posts = [];
						}
						
						if (!refresh) {
							this.hasMore = false;
						}
					}
				})
				.catch(err => {
					console.error('获取消息列表失败:', err);
					if (refresh) {
						this.posts = [];
					}
				})
				.finally(() => {
					this.loading = false;
					this.refreshing = false;
				});
			},
			
			// 检查关注状态
			async checkFollowStatus() {
				for (let i = 0; i < this.posts.length; i++) {
					const post = this.posts[i];
					try {
						const res = await request.post('/concern/checkConcern', {
							user_id: this.userId,
							message_id: post.message_id
						});
						
						if (res.code === 200) {
							this.posts[i].isFollowed = res.isConcerned;
						}
					} catch (err) {
						console.error('检查关注状态失败:', err);
					}
				}
			},
			
			// 加载所有帖子的评论数量
			loadCommentCounts() {
				this.posts.forEach((post, index) => {
					this.loadCommentCount(post.message_id, index);
				});
			},

			// 加载单个帖子的评论数量
			loadCommentCount(messageId, postIndex) {
				request.post('/message/comments', {
					message_id: messageId
				}).then(res => {
					if (res.code === 200) {
						this.posts[postIndex].commentCount = res.result.length;
					}
				}).catch(err => {
					console.error('加载评论数量失败:', err);
				});
			},
			
			// 从消息内容中提取标签
			extractTags(content) {
				if (!content) return [];
				
				// 尝试找出#标签
				const tags = [];
				const tagPattern = /#[\u4e00-\u9fa5a-zA-Z0-9]+/g;
				const matches = content.match(tagPattern);
				
				if (matches) {
					matches.forEach(match => {
						tags.push(match.substring(1)); // 去掉#
				});
				}
				
				// 如果没有标签，根据内容添加默认标签
				if (tags.length === 0) {
					if (content.includes('高考')) tags.push('高考');
					if (content.includes('大学')) tags.push('院校');
					if (content.includes('专业')) tags.push('专业');
					if (content.includes('考研')) tags.push('考试');
					if (content.includes('志愿')) tags.push('志愿');
					if (content.includes('学习')) tags.push('心得');
					if (content.includes('?') || content.includes('？')) tags.push('问答');
							}
				
				return tags.length > 0 ? tags : ['消息'];
			},
			
			// 判断消息是否有图片
			hasImages(message) {
				return message.message_image || message.message_imageone || message.message_imagetwo;
			},
			
			// 获取消息的所有图片URL
			getMessageImages(message) {
				const images = [];
				if (message.message_image) images.push(this.getImageUrl(message.message_image));
				if (message.message_imageone) images.push(this.getImageUrl(message.message_imageone));
				if (message.message_imagetwo) images.push(this.getImageUrl(message.message_imagetwo));
				return images;
			},
			
			// 处理头像URL
			getAvatarUrl(url) {
				if (!url) return '/static/c1.png';
				return this.getImageUrl(url);
			},
			
			// 处理图片URL
			getImageUrl(url) {
				if (!url) return '';
				if (url.startsWith('http')) return url;
				if (url.startsWith('/')) return request.baseUrl + url;
				return request.baseUrl + '/' + url;
			},
			
			// 下拉刷新
			onRefresh() {
				this.refreshing = true;
				this.loadPosts(true);
			},
			
			// 加载更多
			loadMore() {
				if (this.hasMore && !this.loading) {
					this.page++;
					this.loadPosts();
				}
			},
			
			// 选择评论图片
			chooseCommentImage() {
				uni.chooseImage({
					count: 1,
					sizeType: ['compressed'],
					sourceType: ['album', 'camera'],
					success: (res) => {
						this.commentImage = res.tempFilePaths[0];
					}
				});
			},
			
			// 移除评论图片
			removeCommentImage() {
				this.commentImage = '';
			},
			
			// 发送评论
			sendComment(messageId, postIndex) {
				if (!this.newComment.trim() && !this.commentImage) {
					uni.showToast({
						title: '评论内容不能为空',
						icon: 'none'
					});
					return;
				}
				
				uni.showLoading({ title: '发送中...' });
				
				// 如果有图片，使用上传接口
				if (this.commentImage) {
					uni.uploadFile({
						url: request.baseUrl + '/message/addComment',
						filePath: this.commentImage,
						name: 'comment_image',
						formData: {
							message_id: messageId,
							user_id: this.userId,
							comment_content: this.newComment
						},
						success: (res) => {
							this.handleCommentResponse(JSON.parse(res.data), postIndex);
						},
						fail: (err) => {
							this.handleCommentError(err);
						}
					});
				} else {
					// 纯文本评论
					request.post('/message/addComment', {
						message_id: messageId,
						user_id: this.userId,
						comment_content: this.newComment
					}).then(res => {
						this.handleCommentResponse(res, postIndex);
					}).catch(err => {
						this.handleCommentError(err);
					});
				}
			},
			
			// 处理评论响应
			handleCommentResponse(res, postIndex) {
				uni.hideLoading();
				
				if (res.code === 200) {
					// 重新加载评论列表以获取最新的评论信息（包括用户姓名和头像）
					this.loadComments(this.posts[postIndex].message_id, postIndex);
					
					// 清空输入
					this.newComment = '';
					this.commentImage = '';
					
					uni.showToast({
						title: '评论成功',
						icon: 'success'
					});
				} else {
					uni.showToast({
						title: res.message || '评论失败',
						icon: 'none'
					});
				}
			},
			
			// 处理评论错误
			handleCommentError(err) {
				uni.hideLoading();
				console.error('发送评论失败:', err);
				uni.showToast({
					title: '评论失败，请重试',
					icon: 'none'
				});
			},
			
			// 点击评论按钮
			commentPost(index) {
				const post = this.posts[index];
				post.showComments = !post.showComments;
				
				// 如果是展开评论且还没加载过评论，则加载评论
				if (post.showComments && !post.commentsLoaded) {
					this.loadComments(post.message_id, index);
				}
			},
			
			// 加载评论列表
			loadComments(messageId, postIndex) {
				request.post('/message/comments', {
					message_id: messageId
				}).then(res => {
					if (res.code === 200) {
						const comments = res.result.map(comment => ({
							username: comment.nick_name || '用户' + comment.user_id,
							avatar: this.getAvatarUrl(comment.user_image),
							content: comment.comment_content,
							image: comment.comment_image ? this.getImageUrl(comment.comment_image) : null,
							time: '刚刚',
							comment_id: comment.comment_id
						}));
						
						this.posts[postIndex].comments = comments;
						this.posts[postIndex].commentCount = comments.length;
						this.posts[postIndex].commentsLoaded = true;
					}
				}).catch(err => {
					console.error('加载评论失败:', err);
				});
			},
			
			// 根据消息ID生成时间显示
			formatTimeByMessageId(messageId) {
				if (!messageId) return '未知时间';
				
				// 根据消息ID生成相对时间，较新的消息ID显示为较近的时间
				const now = new Date();
				const hour = Math.abs(messageId % 24); // 0-23小时
				const day = Math.abs(messageId % 7); // 0-6天
				
				if (messageId > 100) {
					return '刚刚';
				} else if (messageId > 50) {
					return hour + '小时前';
				} else if (messageId > 20) {
					return day + '天前';
				} else {
					return '一周前';
				}
			},
			
			// 发布新帖子/消息
			publish() {
				console.log('发布新帖子');
				// 跳转到发布页面
				uni.navigateTo({
					url: '/pages/chat/publish'
				});
			},
			
			selectCategory(index) {
				this.currentCategory = index;
				this.page = 1;
				this.posts = [];
				// 根据分类重新加载数据
				this.loadPosts(true);
			},
			// 关注用户
			async followUser(index) {
				const post = this.posts[index];
				
				// 检查是否是自己的消息
				if (post.user_id === this.userId) {
					uni.showToast({
						title: '不能关注自己',
						icon: 'none'
					});
					return;
				}
				
				if (post.isFollowed) {
					uni.showToast({
						title: '已经关注过了',
						icon: 'none'
					});
					return;
				}
				
				try {
					uni.showLoading({ title: '关注中...' });
					
					const res = await request.post('/concern/addConcern', {
						user_id: this.userId,
						message_id: post.message_id
					});
					
					uni.hideLoading();
					
					if (res.code === 200) {
						// 更新UI状态
						this.posts[index].isFollowed = true;
						
						uni.showToast({
							title: '关注成功',
							icon: 'success'
						});
					} else {
						uni.showToast({
							title: res.message || '关注失败',
							icon: 'none'
						});
					}
				} catch (err) {
					uni.hideLoading();
					console.error('关注失败:', err);
					uni.showToast({
						title: '关注失败，请重试',
						icon: 'none'
					});
				}
			},
			likePost(index) {
				const post = this.posts[index];
				post.isLiked = !post.isLiked;
				post.likes += post.isLiked ? 1 : -1;
			},
			sharePost(index) {
				console.log('分享帖子：', index);
				// 调用分享API
				uni.share({
					provider: 'weixin',
					scene: 'WXSceneSession',
					type: 0,
					title: '分享内容',
					summary: this.posts[index].message_content,
					success: function (res) {
						console.log('分享成功：' + JSON.stringify(res));
					},
					fail: function (err) {
						console.log('分享失败：' + JSON.stringify(err));
					}
				});
			},
			previewImage(images, current) {
				uni.previewImage({
					urls: images,
					current: images[current]
				});
			},
			// 根据分类匹配关键词
			matchCategoryKeywords(content, category) {
				const categoryKeywords = {
					'院校': ['大学', '学院', '高校', '院校', '985', '211', '双一流'],
					'专业': ['专业', '学科', '系', '计算机', '医学', '工程', '文学', '理学'],
					'考试': ['高考', '考试', '分数', '成绩', '考研', '复习', '备考'],
					'志愿': ['志愿', '填报', '录取', '投档', '批次', '平行志愿'],
					'心得': ['经验', '心得', '感受', '体会', '建议', '分享', '学习'],
					'问答': ['?', '？', '怎么', '如何', '什么', '为什么', '请问']
				};
				
				const keywords = categoryKeywords[category] || [];
				return keywords.some(keyword => content.includes(keyword));
			},
			goToDetail(item) {
				console.log('跳转到详情页面，消息ID:', item.message_id);
				uni.navigateTo({
					url: `/pages/chat/chat_detail?messageId=${item.message_id}`
				});
			},
			// 删除帖子
			deletePost(index) {
				const post = this.posts[index];
				
				uni.showModal({
					title: '确认删除',
					content: '确定要删除这条消息吗？删除后无法恢复。',
					confirmText: '删除',
					cancelText: '取消',
					confirmColor: '#ff3b30',
					success: (res) => {
						if (res.confirm) {
							this.performDeletePost(post.message_id, index);
						}
					}
				});
			},
			// 执行删除操作
			performDeletePost(messageId, index) {
				uni.showLoading({ title: '删除中...' });
				
				request.post('/message/delete', {
					message_id: messageId
				})
				.then(res => {
					uni.hideLoading();
					console.log('删除消息响应:', res);
					
					if (res.code === 200) {
						// 从列表中移除该帖子
						this.posts.splice(index, 1);
						
						uni.showToast({
							title: '删除成功',
							icon: 'success'
						});
					} else {
						uni.showToast({
							title: res.message || '删除失败',
							icon: 'none'
						});
					}
				})
				.catch(err => {
					uni.hideLoading();
					console.error('删除消息失败:', err);
					uni.showToast({
						title: '删除失败，请重试',
						icon: 'none'
					});
				});
			},
			// 修改帖子
			editPost(index) {
				const post = this.posts[index];
				
				uni.showModal({
					title: '修改消息',
					editable: true,
					placeholderText: '请输入新的消息内容',
					content: post.message_content,
					confirmText: '保存',
					cancelText: '取消',
					success: (res) => {
						if (res.confirm && res.content && res.content.trim()) {
							this.performEditPost(post.message_id, res.content.trim(), index);
						} else if (res.confirm && (!res.content || !res.content.trim())) {
							uni.showToast({
								title: '消息内容不能为空',
								icon: 'none'
							});
						}
					}
				});
			},
			// 执行修改操作
			performEditPost(messageId, newContent, index) {
				uni.showLoading({ title: '修改中...' });
				
				request.post('/message/update', {
					message_id: messageId,
					message_content: newContent
				})
				.then(res => {
					uni.hideLoading();
					console.log('修改消息响应:', res);
					
					if (res.code === 200) {
						// 更新本地数据
						this.posts[index].message_content = newContent;
						
						uni.showToast({
							title: '修改成功',
							icon: 'success'
						});
					} else {
						uni.showToast({
							title: res.message || '修改失败',
							icon: 'none'
						});
					}
				})
				.catch(err => {
					uni.hideLoading();
					console.error('修改消息失败:', err);
					uni.showToast({
						title: '修改失败，请重试',
						icon: 'none'
					});
				});
			}
		}
	}
</script>

<style scoped>
/* 基础布局 */
.chat-container {
    padding-bottom: 20px;
}

.category-scroll {
    background: #fff;
    padding: 10px 0;
    border-bottom: 1px solid #f0f0f0;
}

.category-list {
    display: flex;
    padding: 0 15px;
    white-space: nowrap;
}

.category-item {
    padding: 8px 16px;
    margin-right: 10px;
    background: #f5f5f5;
    border-radius: 20px;
    font-size: 14px;
    color: #666;
    white-space: nowrap;
}

.category-item.active {
    background: #d4237a;
    color: #fff;
}

.publish-btn {
    position: fixed;
    right: 20px;
    bottom: 80px;
    width: 50px;
    height: 50px;
    background: #d4237a;
    border-radius: 25px;
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 999;
    box-shadow: 0 4px 12px rgba(212, 35, 122, 0.3);
}

/* 加载和空状态 */
.loading-container, .empty-tips {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 50px 20px;
}

.loading-text, .empty-text {
    margin-top: 10px;
    font-size: 14px;
    color: #999;
}

.empty-image {
    width: 120px;
    height: 120px;
}

/* 帖子列表 */
.post-list {
    padding: 10px;
    height: calc(100vh - 50px);
}

.post-item {
    background: #fff;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 10px;
}

/* 帖子头部 */
.post-header {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
}

.avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    margin-right: 10px;
}

.user-info {
    flex: 1;
}

.username {
    font-size: 15px;
    font-weight: bold;
}

.post-time {
    font-size: 12px;
    color: #999;
    display: block;
}

/* 按钮样式 */
.follow-btn, .my-post-btn {
    padding: 10rpx 20rpx;
    border-radius: 30rpx;
    font-size: 24rpx;
}

.follow-btn {
    background: linear-gradient(45deg, #667eea, #764ba2);
    color: white;
}

.follow-btn.followed {
    background: #f0f0f0;
    color: #999;
}

.my-post-btn {
    background: #f0f0f0;
    color: #999;
}

/* 帖子内容 */
.post-content {
    margin-bottom: 10px;
}

.post-text {
    font-size: 15px;
    line-height: 1.5;
    color: #333;
}

.post-images {
    display: flex;
    flex-wrap: wrap;
    gap: 5px;
    margin-top: 10px;
}

.post-image {
    width: 100px;
    height: 100px;
    border-radius: 5px;
}

/* 标签 */
.post-tags {
    margin-bottom: 10px;
}

.tag {
    display: inline-block;
    background: #f0f8ff;
    color: #007aff;
    padding: 2px 8px;
    border-radius: 10px;
    font-size: 12px;
    margin-right: 5px;
}

/* 互动栏 */
.post-actions {
    display: flex;
    justify-content: space-around;
    padding: 10px 0;
    border-top: 1px solid #f5f5f5;
}

.action-item {
    display: flex;
    align-items: center;
    gap: 5px;
}

.action-text {
    font-size: 14px;
    color: #666;
}

.action-text.active {
    color: #d4237a;
}

/* 评论区 */
.comment-section {
    margin-top: 10px;
    border-top: 1px solid #f5f5f5;
    padding-top: 10px;
}

.comment-item {
    display: flex;
    margin-bottom: 10px;
}

.comment-avatar {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    margin-right: 10px;
}

.comment-content {
    flex: 1;
}

.comment-user {
    font-size: 14px;
    font-weight: bold;
}

.comment-text {
    font-size: 14px;
    margin: 3px 0;
}

.comment-image {
    max-width: 150px;
    max-height: 150px;
    border-radius: 5px;
    margin: 5px 0;
}

.comment-time {
    font-size: 12px;
    color: #999;
}

/* 评论输入 */
.comment-input-box {
    display: flex;
    margin-top: 10px;
    align-items: center;
}

.comment-input-wrapper {
    flex: 1;
    display: flex;
    align-items: center;
    background: #f5f5f5;
    border-radius: 18px;
    padding: 0 10px;
}

.comment-input {
    flex: 1;
    height: 36px;
    font-size: 14px;
    background: transparent;
}

.comment-image-btn {
    padding: 0 5px;
}

.send-btn {
    width: 60px;
    height: 36px;
    line-height: 36px;
    font-size: 14px;
    color: #fff;
    background: #d4237a;
    border-radius: 18px;
    margin-left: 10px;
    padding: 0;
}

/* 评论预览 */
.comment-preview {
    margin-top: 10px;
    position: relative;
    width: 100px;
    height: 100px;
}

.preview-image {
    width: 100%;
    height: 100%;
    border-radius: 5px;
}

.remove-image {
    position: absolute;
    top: -8px;
    right: -8px;
    width: 20px;
    height: 20px;
    background: rgba(0, 0, 0, 0.5);
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
}

/* 加载更多 */
.load-more {
    text-align: center;
    padding: 20px;
}

.load-more-text {
    font-size: 14px;
    color: #999;
}

/* 管理员回复样式 */
.admin-reply {
    margin-top: 10px;
    padding: 10px;
    background: #f8f9fa;
    border-left: 3px solid #d4237a;
    border-radius: 5px;
}

.admin-reply-header {
    display: flex;
    align-items: center;
    margin-bottom: 5px;
}

.admin-label {
    font-size: 12px;
    color: #d4237a;
    font-weight: bold;
    margin-left: 5px;
}

.admin-reply-text {
    font-size: 14px;
    color: #333;
    line-height: 1.4;
}
</style>


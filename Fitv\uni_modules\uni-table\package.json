{"id": "uni-table", "displayName": "uni-table 表格", "version": "1.2.4", "description": "表格组件，多用于展示多条结构类似的数据，如", "keywords": ["uni-ui", "uniui", "table", "表格"], "repository": "https://github.com/dcloudio/uni-ui", "engines": {"HBuilderX": ""}, "directories": {"example": "../../temps/example_temps"}, "dcloudext": {"sale": {"regular": {"price": "0.00"}, "sourcecode": {"price": "0.00"}}, "contact": {"qq": ""}, "declaration": {"ads": "无", "data": "无", "permissions": "无"}, "npmurl": "https://www.npmjs.com/package/@dcloudio/uni-ui", "type": "component-vue"}, "uni_modules": {"dependencies": ["uni-scss", "uni-datetime-picker"], "encrypt": [], "platforms": {"cloud": {"tcb": "y", "aliyun": "y"}, "client": {"App": {"app-vue": "y", "app-nvue": "n"}, "H5-mobile": {"Safari": "y", "Android Browser": "y", "微信浏览器(Android)": "y", "QQ浏览器(Android)": "y"}, "H5-pc": {"Chrome": "y", "IE": "y", "Edge": "y", "Firefox": "y", "Safari": "y"}, "小程序": {"微信": "y", "阿里": "y", "百度": "y", "字节跳动": "n", "QQ": "y"}, "快应用": {"华为": "n", "联盟": "n"}, "Vue": {"vue2": "y", "vue3": "y"}}}}}
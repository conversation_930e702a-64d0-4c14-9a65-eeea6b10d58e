{"name": "mysql", "description": "A node.js driver for mysql. It is written in JavaScript, does not require compiling, and is 100% MIT licensed.", "version": "2.18.1", "license": "MIT", "author": "<PERSON> <<EMAIL>> (http://debuggable.com/)", "contributors": ["<PERSON><PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>"], "repository": "mysqljs/mysql", "dependencies": {"bignumber.js": "9.0.0", "readable-stream": "2.3.7", "safe-buffer": "5.1.2", "sqlstring": "2.3.1"}, "devDependencies": {"after": "0.8.2", "eslint": "5.16.0", "seedrandom": "3.0.5", "timezone-mock": "0.0.7", "urun": "0.0.8", "utest": "0.0.8"}, "files": ["lib/", "Changes.md", "License", "Readme.md", "index.js"], "engines": {"node": ">= 0.6"}, "scripts": {"lint": "eslint . && node tool/lint-readme.js", "test": "node test/run.js", "test-ci": "node tool/install-nyc.js --nyc-optional --reporter=text -- npm test", "test-cov": "node tool/install-nyc.js --reporter=html --reporter=text -- npm test", "version": "node tool/version-changes.js && git add Changes.md"}}
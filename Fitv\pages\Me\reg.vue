<template>
	<view class="register-container">
		<!-- 顶部背景 -->
		<view class="register-header">
			<image class="register-bg" src="/static/e1.jpg" mode="aspectFill"></image>
			<view class="header-title">
				<text class="title-text">注册账号</text>
				<text class="subtitle-text">加入志愿填报系统，规划你的未来</text>
			</view>
		</view>
		
		<!-- 注册表单 -->
		<view class="register-form">
			<!-- 注册方式切换 -->
			<view class="register-tabs">
				<view 
					:class="['tab-item', registerType === 'phone' ? 'active' : '']" 
					@click="switchRegisterType('phone')"
				>手机号注册</view>
				<view 
					:class="['tab-item', registerType === 'account' ? 'active' : '']" 
					@click="switchRegisterType('account')"
				>账号注册</view>
			</view>
			
			<!-- 手机号注册 -->
			<view class="form-box" v-if="registerType === 'phone'">
				<view class="input-item">
					<uni-icons type="phone" size="20" color="#999"></uni-icons>
					<input type="number" v-model="phoneForm.phone" placeholder="请输入手机号" class="input" maxlength="11" />
				</view>
				<view class="input-item">
					<uni-icons type="locked" size="20" color="#999"></uni-icons>
					<input type="number" v-model="phoneForm.code" placeholder="请输入验证码" class="input" maxlength="6" />
					<view class="code-btn" @click="sendCode">{{codeText}}</view>
				</view>
				<view class="input-item">
					<uni-icons type="locked" size="20" color="#999"></uni-icons>
					<input :type="showPassword ? 'text' : 'password'" v-model="phoneForm.password" placeholder="请设置密码" class="input" />
					<uni-icons :type="showPassword ? 'eye' : 'eye-slash'" size="20" color="#999" @click="togglePasswordVisibility"></uni-icons>
				</view>
				<view class="input-item">
					<uni-icons type="locked" size="20" color="#999"></uni-icons>
					<input :type="showPassword ? 'text' : 'password'" v-model="phoneForm.confirmPassword" placeholder="请确认密码" class="input" />
				</view>
			</view>
			
			<!-- 账号注册 -->
			<view class="form-box" v-else>
				<view class="input-item">
					<uni-icons type="person" size="20" color="#999"></uni-icons>
					<input type="text" v-model="accountForm.username" placeholder="请设置用户名" class="input" />
				</view>
				<view class="input-item">
					<uni-icons type="locked" size="20" color="#999"></uni-icons>
					<input :type="showPassword ? 'text' : 'password'" v-model="accountForm.password" placeholder="请设置密码" class="input" />
					<uni-icons :type="showPassword ? 'eye' : 'eye-slash'" size="20" color="#999" @click="togglePasswordVisibility"></uni-icons>
				</view>
				<view class="input-item">
					<uni-icons type="locked" size="20" color="#999"></uni-icons>
					<input :type="showPassword ? 'text' : 'password'" v-model="accountForm.confirmPassword" placeholder="请确认密码" class="input" />
				</view>
				<view class="input-item">
					<uni-icons type="email" size="20" color="#999"></uni-icons>
					<input type="text" v-model="accountForm.email" placeholder="请输入邮箱(选填)" class="input" />
				</view>
			</view>
			
			<!-- 用户协议 -->
			<view class="agreement">
				<view class="checkbox-container" @click="toggleAgreement">
					<view :class="['checkbox', isAgree ? 'checked' : '']"></view>
				</view>
				<view class="agreement-text">
					<text>我已阅读并同意</text>
					<text class="link" @click="openAgreement('user')">《用户协议》</text>
					<text>和</text>
					<text class="link" @click="openAgreement('privacy')">《隐私政策》</text>
				</view>
			</view>
			
			<!-- 注册按钮 -->
			<button class="register-btn" :disabled="!isAgree" :class="{'disabled': !isAgree}" @click="register">注册</button>
			
			<!-- 登录入口 -->
			<view class="login-box">
				<text class="login-text">已有账号？</text>
				<text class="login-btn" @click="toLogin">立即登录</text>
			</view>
		</view>
		
		<!-- 底部提示 -->
		<view class="bottom-tips">
			<view class="tip-item">
				<uni-icons type="info" size="16" color="#d4237a"></uni-icons>
				<text class="tip-text">注册即可获得志愿填报指导</text>
			</view>
			<view class="tip-item">
				<uni-icons type="info" size="16" color="#d4237a"></uni-icons>
				<text class="tip-text">专业院校数据实时更新</text>
			</view>
		</view>
	</view>
</template>

<script>
	import request from '../../utils/request.js';
	
	export default {
		data() {
			return {
				registerType: 'phone', // phone: 手机号注册, account: 账号注册
				phoneForm: {
					phone: '',
					code: '',
					password: '',
					confirmPassword: ''
				},
				accountForm: {
					username: '',
					password: '',
					confirmPassword: '',
					email: ''
				},
				showPassword: false,
				isAgree: false,
				codeText: '获取验证码',
				timer: null,
				countdown: 60
			}
		},
		methods: {
			// 切换注册方式
			switchRegisterType(type) {
				this.registerType = type;
			},
			
			// 切换密码可见性
			togglePasswordVisibility() {
				this.showPassword = !this.showPassword;
			},
			
			// 切换协议同意
			toggleAgreement() {
				this.isAgree = !this.isAgree;
			},
			
			// 发送验证码
			sendCode() {
				// 检查是否在倒计时中
				if (this.codeText !== '获取验证码') {
					return;
				}
				
				// 检查手机号
				if (!this.phoneForm.phone || this.phoneForm.phone.length !== 11) {
					uni.showToast({
						title: '请输入正确的手机号',
						icon: 'none'
					});
					return;
				}
				
				// 调用发送验证码接口
				request.post('/auth/sendCode', {
					phone: this.phoneForm.phone
				}).then(res => {
					if (res.code === 200) {
						// 开始倒计时
						this.countdown = 60;
						this.codeText = `${this.countdown}s`;
						this.timer = setInterval(() => {
							this.countdown--;
							this.codeText = `${this.countdown}s`;
							if (this.countdown <= 0) {
								clearInterval(this.timer);
								this.codeText = '获取验证码';
							}
						}, 1000);
						
						uni.showToast({
							title: '验证码已发送',
							icon: 'success'
						});
						
						// 开发环境下显示验证码
						if (res.verificationCode) {
							console.log('验证码：', res.verificationCode);
						}
					}
				}).catch(err => {
					console.error('发送验证码请求失败', err);
				});
			},
			
			// 打开协议
			openAgreement(type) {
				const title = type === 'user' ? '用户协议' : '隐私政策';
				uni.showToast({
					title: `${title}功能开发中`,
					icon: 'none'
				});
			},
			
			// 注册
			register() {
				if (!this.isAgree) {
					uni.showToast({
						title: '请先同意用户协议和隐私政策',
						icon: 'none'
					});
					return;
				}
				
				// 手机号注册
				if (this.registerType === 'phone') {
					// 手机号注册验证
					if (!this.phoneForm.phone || this.phoneForm.phone.length !== 11) {
						uni.showToast({
							title: '请输入正确的手机号',
							icon: 'none'
						});
						return;
					}
					
					if (!this.phoneForm.code || this.phoneForm.code.length !== 6) {
						uni.showToast({
							title: '请输入6位验证码',
							icon: 'none'
						});
						return;
					}
					
					if (!this.phoneForm.password) {
						uni.showToast({
							title: '请设置密码',
							icon: 'none'
						});
						return;
					}
					
					if (this.phoneForm.password !== this.phoneForm.confirmPassword) {
						uni.showToast({
							title: '两次密码输入不一致',
							icon: 'none'
						});
						return;
					}
					
					// 调用手机号注册接口
					uni.showLoading({
						title: '注册中...'
					});
					
					request.post('/auth/register', {
						username: this.phoneForm.phone, // 用手机号作为用户名
						password: this.phoneForm.password,
						phone: this.phoneForm.phone,
						code: this.phoneForm.code,
						nick_name: '用户' + this.phoneForm.phone.substring(7), // 默认昵称
						identity: '学生' // 默认身份为学生
					}).then(res => {
						uni.hideLoading();
						if (res.code === 200) {
							uni.showToast({
								title: '注册成功',
								icon: 'success'
							});
							
							// 跳转到登录页
							setTimeout(() => {
								uni.navigateTo({
									url: '/pages/Me/Login'
								});
							}, 1500);
						}
					}).catch(err => {
						uni.hideLoading();
						console.error('注册请求失败', err);
					});
				} else {
					// 账号注册验证
					if (!this.accountForm.username) {
						uni.showToast({
							title: '请设置用户名',
							icon: 'none'
						});
						return;
					}
					
					if (!this.accountForm.password) {
						uni.showToast({
							title: '请设置密码',
							icon: 'none'
						});
						return;
					}
					
					if (this.accountForm.password !== this.accountForm.confirmPassword) {
						uni.showToast({
							title: '两次密码输入不一致',
							icon: 'none'
						});
						return;
					}
					
					// 调用账号注册接口
					uni.showLoading({
						title: '注册中...'
					});
					
					request.post('/auth/register', {
						username: this.accountForm.username,
						password: this.accountForm.password,
						nick_name: this.accountForm.username, // 默认使用用户名作为昵称
						email: this.accountForm.email || '',
						identity: '学生' // 默认身份为学生
					}).then(res => {
						uni.hideLoading();
						if (res.code === 200) {
							uni.showToast({
								title: '注册成功',
								icon: 'success'
							});
							
							// 跳转到登录页
							setTimeout(() => {
								uni.navigateTo({
									url: '/pages/Me/Login'
								});
							}, 1500);
						}
					}).catch(err => {
						uni.hideLoading();
						console.error('注册请求失败', err);
					});
				}
			},
			
			// 跳转到登录页
			toLogin() {
				uni.navigateTo({
					url: '/pages/Me/Login'
				});
			}
		},
		// 组件销毁前清除定时器
		beforeDestroy() {
			if (this.timer) {
				clearInterval(this.timer);
			}
		}
	}
</script>

<style>
.register-container {
	min-height: 100vh;
	background-color: #fff;
	display: flex;
	flex-direction: column;
}

.register-header {
	position: relative;
	height: 200px;
	overflow: hidden;
}

.register-bg {
	width: 100%;
	height: 100%;
}

.header-title {
	position: absolute;
	top: 50%;
	left: 0;
	width: 100%;
	transform: translateY(-50%);
	text-align: center;
	color: #fff;
	z-index: 1;
}

.title-text {
	font-size: 28px;
	font-weight: bold;
	display: block;
	margin-bottom: 10px;
	text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
	color: #000;
}

.subtitle-text {
	font-size: 16px;
	display: block;
	text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
	color: #000;
}

.register-form {
	position: relative;
	margin-top: -20px;
	background-color: #fff;
	border-radius: 20px 20px 0 0;
	padding: 30px 20px;
	box-shadow: 0 -3px 10px rgba(0, 0, 0, 0.05);
}

.register-tabs {
	display: flex;
	margin-bottom: 30px;
}

.tab-item {
	flex: 1;
	text-align: center;
	font-size: 16px;
	color: #666;
	padding-bottom: 10px;
	position: relative;
}

.tab-item.active {
	color: #d4237a;
	font-weight: bold;
}

.tab-item.active::after {
	content: '';
	position: absolute;
	bottom: 0;
	left: 50%;
	transform: translateX(-50%);
	width: 30px;
	height: 3px;
	background-color: #d4237a;
	border-radius: 3px;
}

.input-item {
	display: flex;
	align-items: center;
	height: 50px;
	border-bottom: 1px solid #f0f0f0;
	margin-bottom: 20px;
}

.input {
	flex: 1;
	height: 100%;
	font-size: 16px;
	margin-left: 10px;
}

.code-btn {
	padding: 5px 10px;
	background-color: #f5f5f5;
	color: #d4237a;
	font-size: 14px;
	border-radius: 3px;
}

.agreement {
	display: flex;
	align-items: center;
	margin-bottom: 30px;
}

.checkbox-container {
	margin-right: 5px;
}

.checkbox {
	width: 18px;
	height: 18px;
	border: 1px solid #ddd;
	border-radius: 3px;
	position: relative;
}

.checkbox.checked {
	background-color: #d4237a;
	border-color: #d4237a;
}

.checkbox.checked::after {
	content: '';
	position: absolute;
	top: 2px;
	left: 6px;
	width: 4px;
	height: 8px;
	border-right: 2px solid #fff;
	border-bottom: 2px solid #fff;
	transform: rotate(45deg);
}

.agreement-text {
	font-size: 14px;
	color: #666;
}

.link {
	color: #d4237a;
}

.register-btn {
	width: 100%;
	height: 50px;
	line-height: 50px;
	background-color: #d4237a;
	color: #fff;
	font-size: 16px;
	border-radius: 25px;
	margin-bottom: 20px;
}

.register-btn.disabled {
	background-color: #ddd;
	color: #999;
}

.login-box {
	display: flex;
	justify-content: center;
	align-items: center;
	margin-bottom: 30px;
}

.login-text {
	font-size: 14px;
	color: #666;
}

.login-btn {
	font-size: 14px;
	color: #d4237a;
	margin-left: 5px;
}

.bottom-tips {
	padding: 0 20px 30px;
	padding-bottom: calc(30px + env(safe-area-inset-bottom));
}

.tip-item {
	display: flex;
	align-items: center;
	margin-bottom: 10px;
}

.tip-text {
	font-size: 14px;
	color: #666;
	margin-left: 5px;
}
</style>

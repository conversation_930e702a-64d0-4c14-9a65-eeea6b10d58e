<template>
  <div class="dashboard">
    <!-- 欢迎信息 -->
    <el-card class="welcome-card" shadow="hover">
      <div class="welcome-content">
        <div class="welcome-text">
          <div class="user-info">
            <img v-if="userInfo.avatar" :src="userInfo.avatar" class="user-avatar" :alt="userInfo.name">
            <h2>欢迎回来  {{ userInfo.name }}</h2>
          </div>
          <p>今天是 {{ currentDate }}，距离2024年高考志愿填报开始还有 {{ remainingDays }} 天</p>
        </div>
        <div class="quick-actions">
          <el-button type="primary" @click="handleQuickAction('review')">
            <el-icon><Document /></el-icon>审核志愿
          </el-button>
          <el-button type="success" @click="handleQuickAction('consult')">
            <el-icon><Service /></el-icon>咨询管理
          </el-button>
          <el-button type="warning" @click="handleQuickAction('school')">
            <el-icon><School /></el-icon>院校管理
          </el-button>
        </div>
      </div>
    </el-card>

    <!-- 数据概览 -->
    <el-row :gutter="20" class="data-overview">
      <el-col :span="6" v-for="(item, index) in statistics" :key="index">
        <el-card shadow="hover" class="data-card">
          <div class="data-icon" :class="item.type">
            <el-icon><component :is="item.icon" /></el-icon>
          </div>
          <div class="data-info">
            <div class="data-title">{{ item.title }}</div>
            <div class="data-number">{{ item.value }}</div>
            <div class="data-trend" v-if="item.trend">
              较昨日
              <span :class="item.trend > 0 ? 'up' : 'down'">
                {{ Math.abs(item.trend) }}%
                <el-icon><component :is="item.trend > 0 ? 'ArrowUp' : 'ArrowDown'" /></el-icon>
              </span>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 主要内容区域 -->
    <el-row :gutter="20" class="main-content">
      <!-- 左侧图表 -->
      <el-col :span="16">
        <el-card shadow="hover" class="chart-card">
          <template #header>
            <div class="chart-header">
              <div class="chart-title">
                <h3>志愿填报数据分析</h3>
                <el-tag size="small" type="info">实时数据</el-tag>
              </div>
              <el-radio-group v-model="chartTimeRange" size="small">
                <el-radio-button label="week">本周</el-radio-button>
                <el-radio-button label="month">本月</el-radio-button>
                <el-radio-button label="year">本年</el-radio-button>
              </el-radio-group>
            </div>
          </template>
          <div class="chart-container">
            <div class="chart-tabs">
              <el-tabs v-model="activeChart">
                <el-tab-pane label="志愿填报趋势" name="applications">
                  <div class="chart" ref="applicationsChart"></div>
                </el-tab-pane>
                <el-tab-pane label="考生分数分布" name="scores">
                  <div class="chart" ref="scoresChart"></div>
                </el-tab-pane>
                <el-tab-pane label="专业偏好分析" name="majors">
                  <div class="chart" ref="majorsChart"></div>
                </el-tab-pane>
              </el-tabs>
            </div>
          </div>
        </el-card>
      </el-col>

      <!-- 右侧信息 -->
      <el-col :span="8">
        <!-- 待办事项 -->
        <el-card shadow="hover" class="todo-card">
          <template #header>
            <div class="card-header">
              <h3>待办事项</h3>
              <el-button>查看全部</el-button>
            </div>
          </template>
          <el-timeline>
            <el-timeline-item
              v-for="(todo, index) in todos"
              :key="index"
              :type="todo.type"
              :timestamp="todo.time"
            >
              {{ todo.content }}
            </el-timeline-item>
          </el-timeline>
        </el-card>

        <!-- 系统公告 -->
        <el-card shadow="hover" class="notice-card">
          <template #header>
            <div class="card-header">
              <h3>系统公告</h3>
              <el-button>更多</el-button>
            </div>
          </template>
          <div class="notice-list">
            <div v-for="(notice, index) in notices" :key="index" class="notice-item">
              <div class="notice-title">
                <el-tag :type="notice.type" size="small">{{ notice.tag }}</el-tag>
                {{ notice.title }}
              </div>
              <div class="notice-time">{{ notice.time }}</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 最新动态 -->
    <el-card shadow="hover" class="activities-card">
      <template #header>
        <div class="card-header">
          <h3>最新动态</h3>
          <div class="header-actions">
            <el-radio-group v-model="activityType" size="small">
              <el-radio-button label="all">全部</el-radio-button>
              <el-radio-button label="application">志愿填报</el-radio-button>
              <el-radio-button label="consultation">咨询记录</el-radio-button>
              <el-radio-button label="review">审核记录</el-radio-button>
            </el-radio-group>
          </div>
        </div>
      </template>
      <el-table :data="activities" style="width: 100%">
        <el-table-column prop="time" label="时间" width="180" />
        <el-table-column prop="type" label="类型" width="120">
          <template #default="scope">
            <el-tag :type="scope.row.typeStyle">{{ scope.row.type }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="content" label="内容" />
        <el-table-column prop="status" label="状态" width="120">
          <template #default="scope">
            <el-tag :type="scope.row.statusType" size="small">{{ scope.row.status }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="120">
          <template #default="scope">
            <el-button size="small" @click="handleDetail(scope.row)">
              查看详情
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
  </div>
</template>

<script>
import { ref, onMounted, computed, nextTick, watch, onUnmounted } from 'vue'
import * as echarts from 'echarts'
import { useRouter } from 'vue-router'
import {
  User, School, Service, Document, ArrowUp, ArrowDown,
  Histogram, PieChart, DataLine, Calendar
} from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { baseURL } from '@/utils/request'

export default {
  name: 'Dashboard',
  components: {
    User, School, Service, Document,
    ArrowUp, ArrowDown, Histogram,
    PieChart, DataLine, Calendar
  },
  setup() {
    const router = useRouter()
    const userInfo = ref({
      name: '',
      avatar: ''
    })

    // 在 setup 函数中添加 getImageUrl 函数
    const getImageUrl = (path) => {
      if (!path) return ''
      if (path.startsWith('http')) {
        return path
      }
      // 使用导入的基础URL
      return `${baseURL}${path}`
    }

    // 检查登录状态并获取管理员信息
    onMounted(() => {
      const adminInfoStr = localStorage.getItem('adminInfo')
      if (!adminInfoStr) {
        ElMessage.error('请先登录')
        router.push('/login')
        return
      }

      const adminInfo = JSON.parse(adminInfoStr)
      if (!adminInfo.administrator_name) {
        ElMessage.error('登录信息无效，请重新登录')
        router.push('/login')
        return
      }

      userInfo.value = {
        name: adminInfo.administrator_name,
        avatar: getImageUrl(adminInfo.administrator_image)
      }
    })

    const currentDate = computed(() => {
      return new Date().toLocaleDateString('zh-CN', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      })
    })

    const remainingDays = computed(() => {
      const today = new Date()
      const target = new Date('2024-06-20')
      return Math.ceil((target - today) / (1000 * 60 * 60 * 24))
    })

    // 统计数据
    const statistics = ref([
      {
        title: '今日报名',
        value: 256,
        trend: 15.8,
        icon: 'User',
        type: 'primary'
      },
      {
        title: '志愿填报数',
        value: 1892,
        trend: 12.3,
        icon: 'Document',
        type: 'success'
      },
      {
        title: '待处理咨询',
        value: 45,
        trend: -5.2,
        icon: 'Service',
        type: 'warning'
      },
      {
        title: '合作院校',
        value: 385,
        icon: 'School',
        type: 'info'
      }
    ])

    // 待办事项
    const todos = ref([
      {
        content: '审核新增院校信息',
        time: '2小时后截止',
        type: 'warning'
      },
      {
        content: '回复考生咨询信息',
        time: '待处理',
        type: 'info'
      },
      {
        content: '更新招生计划数据',
        time: '今日截止',
        type: 'danger'
      },
      {
        content: '志愿填报系统例行检查',
        time: '明天 14:00',
        type: 'primary'
      }
    ])

    // 系统公告
    const notices = ref([
      {
        title: '2024年高考志愿填报系统正式上线',
        time: '2024-03-01',
        type: 'success',
        tag: '重要'
      },
      {
        title: '关于开展志愿填报模拟演练的通知',
        time: '2024-02-28',
        type: 'warning',
        tag: '通知'
      },
      {
        title: '系统维护升级通知',
        time: '2024-02-25',
        type: 'info',
        tag: '维护'
      }
    ])

    // 最新动态
    const activities = ref([
      {
        time: '2024-03-01 14:23',
        type: '志愿填报',
        typeStyle: 'primary',
        content: '考生张三（2024001001）提交志愿申请',
        status: '待审核',
        statusType: 'warning'
      },
      {
        time: '2024-03-01 13:45',
        type: '咨询记录',
        typeStyle: 'success',
        content: '考生李四咨询录取概率问题',
        status: '已回复',
        statusType: 'success'
      },
      {
        time: '2024-03-01 11:30',
        type: '审核记录',
        typeStyle: 'info',
        content: '审核通过北京大学2024年招生计划',
        status: '已完成',
        statusType: 'success'
      }
    ])

    // 图表相关
    const chartTimeRange = ref('week')
    const activeChart = ref('applications')
    const charts = ref({
      applications: null,
      scores: null,
      majors: null
    })

    // 初始化志愿填报趋势图表
    const initApplicationsChart = () => {
      const chartDom = document.querySelector('[name="applications"] .chart')
      if (!chartDom) return
      
      if (charts.value.applications) {
        charts.value.applications.dispose()
      }
      
      charts.value.applications = echarts.init(chartDom)
      const option = {
        tooltip: {
          trigger: 'axis'
        },
        legend: {
          data: ['文科', '理科']
        },
        xAxis: {
          type: 'category',
          data: ['周一', '周二', '周三', '周四', '周五', '周六', '周日']
        },
        yAxis: {
          type: 'value'
        },
        series: [
          {
            name: '文科',
            type: 'line',
            smooth: true,
            data: [120, 132, 101, 134, 90, 230, 210],
            itemStyle: {
              color: '#409EFF'
            }
          },
          {
            name: '理科',
            type: 'line',
            smooth: true,
            data: [150, 232, 201, 154, 190, 330, 410],
            itemStyle: {
              color: '#67C23A'
            }
          }
        ]
      }
      charts.value.applications.setOption(option)
    }

    // 初始化考生分数分布图表
    const initScoresChart = () => {
      const chartDom = document.querySelector('[name="scores"] .chart')
      if (!chartDom) return
      
      if (charts.value.scores) {
        charts.value.scores.dispose()
      }
      
      charts.value.scores = echarts.init(chartDom)
      const option = {
        tooltip: {
          trigger: 'axis'
        },
        xAxis: {
          type: 'category',
          data: ['500-550', '550-600', '600-650', '650-700', '700+']
        },
        yAxis: {
          type: 'value'
        },
        series: [
          {
            type: 'bar',
            data: [
              {value: 200, itemStyle: {color: '#91CC75'}},
              {value: 300, itemStyle: {color: '#FAC858'}},
              {value: 400, itemStyle: {color: '#EE6666'}},
              {value: 200, itemStyle: {color: '#73C0DE'}},
              {value: 100, itemStyle: {color: '#3BA272'}}
            ],
            label: {
              show: true,
              position: 'top'
            }
          }
        ]
      }
      charts.value.scores.setOption(option)
    }

    // 初始化专业偏好分析图表
    const initMajorsChart = () => {
      const chartDom = document.querySelector('[name="majors"] .chart')
      if (!chartDom) return
      
      if (charts.value.majors) {
        charts.value.majors.dispose()
      }
      
      charts.value.majors = echarts.init(chartDom)
      const option = {
        tooltip: {
          trigger: 'item'
        },
        legend: {
          orient: 'vertical',
          right: 10,
          top: 'center'
        },
        series: [
          {
            name: '专业偏好',
            type: 'pie',
            radius: ['40%', '70%'],
            avoidLabelOverlap: false,
            itemStyle: {
              borderRadius: 10,
              borderColor: '#fff',
              borderWidth: 2
            },
            label: {
              show: false
            },
            emphasis: {
              label: {
                show: true,
                fontSize: 20,
                fontWeight: 'bold'
              }
            },
            data: [
              { value: 1048, name: '计算机类' },
              { value: 735, name: '经济类' },
              { value: 580, name: '医学类' },
              { value: 484, name: '法学类' },
              { value: 300, name: '艺术类' }
            ]
          }
        ]
      }
      charts.value.majors.setOption(option)
    }

    // 处理图表切换
    const handleChartChange = () => {
      nextTick(() => {
        switch(activeChart.value) {
          case 'applications':
            initApplicationsChart()
            break
          case 'scores':
            initScoresChart()
            break
          case 'majors':
            initMajorsChart()
            break
        }
      })
    }

    // 监听图表切换
    watch(activeChart, () => {
      handleChartChange()
    })

    onMounted(() => {
      nextTick(() => {
        handleChartChange()
      })
      
      // 监听窗口大小变化
      window.addEventListener('resize', () => {
        Object.values(charts.value).forEach(chart => {
          if (chart) {
            chart.resize()
          }
        })
      })
    })

    // 组件卸载时清理图表实例
    onUnmounted(() => {
      Object.values(charts.value).forEach(chart => {
        if (chart) {
          chart.dispose()
        }
      })
      // window.removeEventListener('resize', handleResize) // This line was removed as per the new_code, as handleResize is not defined.
    })

    // 快捷操作处理
    const handleQuickAction = (type) => {
      console.log('Quick action:', type)
    }

    // 查看详情处理
    const handleDetail = (row) => {
      console.log('View detail:', row)
    }

    return {
      userInfo,
      currentDate,
      remainingDays,
      statistics,
      chartTimeRange,
      activeChart,
      todos,
      notices,
      activities,
      handleQuickAction,
      handleDetail,
      getImageUrl
    }
  }
}
</script>

<style scoped>
.dashboard {
  padding: 24px;
  background-color: #f5f7fa;
  min-height: 100vh;
}

/* 欢迎卡片样式 */
.welcome-card {
  margin-bottom: 24px;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);
  background: linear-gradient(135deg, #409EFF 0%, #2c50e8 100%);
  border: none;
}

.welcome-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24px;
}

.welcome-text {
  flex: 1;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 12px;
}

.user-avatar {
  width: 64px;
  height: 64px;
  border-radius: 50%;
  border: 3px solid rgba(255, 255, 255, 0.8);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  object-fit: cover;
}

.welcome-text h2 {
  margin: 0;
  font-size: 28px;
  color: #ffffff;
  font-weight: 600;
  letter-spacing: 0.5px;
}

.welcome-text p {
  margin: 12px 0 0;
  color: rgba(255, 255, 255, 0.9);
  font-size: 16px;
}

.quick-actions {
  display: flex;
  gap: 16px;
}

.quick-actions .el-button {
  padding: 12px 24px;
  border-radius: 8px;
  font-weight: 500;
  transition: all 0.3s ease;
  border: none;
}

.quick-actions .el-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.quick-actions .el-icon {
  margin-right: 8px;
  font-size: 18px;
}

/* 数据卡片样式 */
.data-overview {
  margin-bottom: 65px;
}

.data-card {
  display: flex;
  padding: 24px;
  border-radius: 12px;
  transition: all 0.3s ease;
  border: none;
  background: #ffffff;
  height: 100%;
}

.data-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.data-icon {
  width: 56px;
  height: 56px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20px;
  transition: all 0.3s ease;
}

.data-icon .el-icon {
  font-size: 28px;
  color: #fff;
}

.data-icon.primary {
  background: linear-gradient(135deg, #409EFF 0%, #2c50e8 100%);
}

.data-icon.success {
  background: linear-gradient(135deg, #67C23A 0%, #4a9e1c 100%);
}

.data-icon.warning {
  background: linear-gradient(135deg, #E6A23C 0%, #c67b0f 100%);
}

.data-icon.info {
  background: linear-gradient(135deg, #909399 0%, #6b6d71 100%);
}

.data-info {
  flex: 1;
}

.data-title {
  font-size: 16px;
  color: #606266;
  margin-bottom: 12px;
  font-weight: 500;
}

.data-number {
  font-size: 28px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 12px;
  line-height: 1.2;
}

.data-trend {
  font-size: 14px;
  color: #909399;
  display: flex;
  align-items: center;
  gap: 4px;
}

.data-trend .up, .data-trend .down {
  display: flex;
  align-items: center;
  gap: 4px;
  font-weight: 500;
}

.data-trend .up {
  color: #67C23A;
}

.data-trend .down {
  color: #F56C6C;
}

/* 图表卡片样式 */
.chart-card {
  height: 97%;
  margin-bottom: 24px;
  border-radius: 12px;
  border: none;
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  border-bottom: 1px solid #f0f2f5;
}

.chart-title {
  display: flex;
  align-items: center;
  gap: 12px;
}

.chart-title h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #303133;
}

.chart-container {
  padding: 24px;
}

.chart {
  height: 400px;
}

/* 标签页样式 */
.el-tabs__nav-wrap::after {
  display: none;
}

.el-tabs__item {
  font-size: 15px;
  padding: 0 24px;
  height: 44px;
  line-height: 44px;
}

.el-tabs__item.is-active {
  font-weight: 600;
}

/* 待办事项卡片样式 */
.todo-card {
  margin-bottom: 24px;
  border-radius: 12px;
  border: none;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  border-bottom: 1px solid #f0f2f5;
}

.card-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #303133;
}

.el-timeline {
  padding: 24px;
}

.el-timeline-item__node {
  width: 12px;
  height: 12px;
}

.el-timeline-item__content {
  font-size: 14px;
  color: #606266;
}

.el-timeline-item__timestamp {
  font-size: 13px;
  color: #909399;
}

/* 系统公告样式 */
.notice-card {
  margin-bottom: 24px;
  border-radius: 12px;
  border: none;
}

.notice-list {
  max-height: 300px;
  overflow-y: auto;
  padding: 0 24px;
}

.notice-list::-webkit-scrollbar {
  width: 6px;
}

.notice-list::-webkit-scrollbar-thumb {
  background: #e4e7ed;
  border-radius: 3px;
}

.notice-item {
  padding: 16px 0;
  border-bottom: 1px solid #f0f2f5;
  transition: all 0.3s ease;
}

.notice-item:hover {
  background-color: #f5f7fa;
}

.notice-item:last-child {
  border-bottom: none;
}

.notice-title {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 8px;
  font-size: 15px;
  color: #303133;
}

.notice-time {
  font-size: 13px;
  color: #909399;
}

/* 最新动态样式 */
.activities-card {
  margin-bottom: 24px;
  border-radius: 12px;
  border: none;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 16px;
}

.el-table {
  margin: 0 -24px;
  width: calc(100% + 48px);
}

.el-table::before {
  display: none;
}

.el-table th {
  background-color: #f5f7fa;
  font-weight: 600;
  color: #606266;
  padding: 16px 24px;
}

.el-table td {
  padding: 16px 24px;
}

.el-table--enable-row-hover .el-table__body tr:hover > td {
  background-color: #f5f7fa;
}

/* 按钮样式 */
.el-button--primary {
  background: linear-gradient(135deg, #409EFF 0%, #2c50e8 100%);
  border: none;
}

.el-button--success {
  background: linear-gradient(135deg, #67C23A 0%, #4a9e1c 100%);
  border: none;
}

.el-button--warning {
  background: linear-gradient(135deg, #E6A23C 0%, #c67b0f 100%);
  border: none;
}

/* 标签样式 */
.el-tag {
  border-radius: 4px;
  padding: 4px 8px;
  font-weight: 500;
}

/* 响应式调整 */
@media (max-width: 1400px) {
  .el-col {
    margin-bottom: 24px;
  }
  
  .data-number {
    font-size: 24px;
  }
  
  .chart {
    height: 350px;
  }
}

@media (max-width: 1200px) {
  .welcome-content {
    flex-direction: column;
    gap: 24px;
    text-align: center;
  }
  
  .quick-actions {
    width: 100%;
    justify-content: center;
  }
  
  .data-card {
    padding: 20px;
  }
  
  .data-icon {
    width: 48px;
    height: 48px;
  }
  
  .data-number {
    font-size: 22px;
  }
}

@media (max-width: 768px) {
  .dashboard {
    padding: 16px;
  }
  
  .welcome-text h2 {
    font-size: 24px;
  }
  
  .welcome-text p {
    font-size: 14px;
  }
  
  .quick-actions {
    flex-direction: column;
  }
  
  .chart {
    height: 300px;
  }
  
  .el-table {
    margin: 0 -16px;
    width: calc(100% + 32px);
  }
  
  .el-table th,
  .el-table td {
    padding: 12px 16px;
  }
}
</style>

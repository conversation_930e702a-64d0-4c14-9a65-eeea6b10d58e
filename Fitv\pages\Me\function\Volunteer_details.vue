<!-- 志愿详情 -->
<template>
	<view class="detail-container">
		
		<!-- 加载中 -->
		<view v-if="loading" class="loading-container">
			<uni-icons type="spinner-cycle" size="30" color="#999"></uni-icons>
			<text class="loading-text">加载中...</text>
		</view>
		
		<!-- 错误提示 -->
		<view v-else-if="error" class="error-container">
			<uni-icons type="info" size="30" color="#ff3b30"></uni-icons>
			<text class="error-text">{{errorMessage}}</text>
			<button class="retry-btn" @click="fetchDetail">重试</button>
		</view>
		
		<!-- 详情内容 -->
		<view v-else class="detail-content">
			<!-- 学校信息卡片 -->
			<view class="detail-card">
				<view class="card-header">
					<image :src="getSchoolImage(detail)" class="school-logo"></image>
					<view class="school-info">
						<text class="school-name">{{detail.school_name || '未知学校'}}</text>
						<text class="school-major">{{detail.specialized_content || '未知专业'}}</text>
						<view class="status-tag" :class="getStatusClass(detail.admission_state)">
							{{getStatusText(detail.admission_state)}}
						</view>
					</view>
				</view>
			</view>
			
			<!-- 用户信息 -->
			<view class="detail-card">
				<view class="card-title">
					<uni-icons type="person" size="18" color="#007aff"></uni-icons>
					<text>用户信息</text>
				</view>
				<view class="info-list">
					<view class="info-item">
						<text class="info-label">用户名</text>
						<text class="info-value">{{detail.username || '未知'}}</text>
					</view>
					<view class="info-item">
						<text class="info-label">姓名</text>
						<text class="info-value">{{detail.nick_name || '未知'}}</text>
					</view>
					<view class="info-item">
						<text class="info-label">性别</text>
						<text class="info-value">{{detail.sex || '未知'}}</text>
					</view>
					<view class="info-item">
						<text class="info-label">电话</text>
						<text class="info-value">{{detail.phone || '未知'}}</text>
					</view>
					<view class="info-item">
						<text class="info-label">身份</text>
						<text class="info-value">{{detail.identity || '未知'}}</text>
					</view>
					<view class="info-item">
						<text class="info-label">毕业学校</text>
						<text class="info-value">{{detail.user_school || '未知'}}</text>
					</view>
					<view class="info-item">
						<text class="info-label">考试分数</text>
						<text class="info-value">{{detail.user_fraction || '未知'}}</text>
					</view>
					<view class="info-item">
						<text class="info-label">考试类型</text>
						<text class="info-value">{{detail.user_form || '未知'}}</text>
					</view>
				</view>
			</view>
			
			<!-- 学校详情 -->
			<view class="detail-card">
				<view class="card-title">
					<uni-icons type="home" size="18" color="#007aff"></uni-icons>
					<text>学校详情</text>
				</view>
				<view class="info-list">
					<view class="info-item">
						<text class="info-label">学校名称</text>
						<text class="info-value">{{detail.school_name || '未知'}}</text>
					</view>
					<view class="info-item">
						<text class="info-label">联系电话</text>
						<text class="info-value">{{detail.school_phone || '未知'}}</text>
					</view>
					<view class="info-item">
						<text class="info-label">学校地址</text>
						<text class="info-value">{{detail.school_address || '未知'}}</text>
					</view>
					<view class="info-item">
						<text class="info-label">学校特色</text>
						<text class="info-value">{{detail.school_characteristic || '未知'}}</text>
					</view>
					<view class="info-item">
						<text class="info-label">办学理念</text>
						<text class="info-value">{{detail.school_idea || '未知'}}</text>
					</view>
					<view class="info-item">
						<text class="info-label">学校邮箱</text>
						<text class="info-value">{{detail.school_email || '未知'}}</text>
					</view>
					<view class="info-item">
						<text class="info-label">分数线</text>
						<text class="info-value">{{detail.Score_line || '未知'}}</text>
					</view>
					<view class="info-item">
						<text class="info-label">招生人数</text>
						<text class="info-value">{{detail.school_number || '未知'}}</text>
					</view>
				</view>
			</view>
			
			<!-- 专业信息 -->
			<view class="detail-card">
				<view class="card-title">
					<uni-icons type="paperplane" size="18" color="#007aff"></uni-icons>
					<text>专业信息</text>
				</view>
				<view class="info-list">
					<view class="info-item">
						<text class="info-label">专业名称</text>
						<text class="info-value">{{detail.specialized_content || '未知'}}</text>
					</view>
					<view class="info-item" v-if="detail.specialized_part">
						<text class="info-label">专业方向</text>
						<text class="info-value">{{detail.specialized_part}}</text>
					</view>
					<view class="info-item" v-if="detail.specialized_type">
						<text class="info-label">专业类型</text>
						<text class="info-value">{{detail.specialized_type}}</text>
					</view>
					<view class="info-item" v-if="detail.specialized_degree">
						<text class="info-label">学位类型</text>
						<text class="info-value">{{detail.specialized_degree}}</text>
					</view>
				</view>
			</view>
			
			<!-- 专业详细介绍 -->
			<view class="detail-card" v-if="detail.specialized_introduce || detail.specialized_course || detail.specialized_employment">
				<view class="card-title">
					<uni-icons type="info" size="18" color="#007aff"></uni-icons>
					<text>专业详情</text>
				</view>
				<view class="info-list">
					<view class="info-section" v-if="detail.specialized_introduce">
						<text class="section-title">专业介绍</text>
						<text class="section-content">{{detail.specialized_introduce}}</text>
					</view>
					<view class="info-section" v-if="detail.specialized_course">
						<text class="section-title">主要课程</text>
						<text class="section-content">{{detail.specialized_course}}</text>
					</view>
					<view class="info-section" v-if="detail.specialized_employment">
						<text class="section-title">就业方向</text>
						<text class="section-content">{{detail.specialized_employment}}</text>
					</view>
				</view>
			</view>
			
			<!-- 录取分数信息 -->
			<view class="detail-card">
				<view class="card-title">
					<uni-icons type="medal" size="18" color="#007aff"></uni-icons>
					<text>录取分数信息</text>
				</view>
				<view class="info-list">
					<view class="info-item">
						<text class="info-label">最低分数</text>
						<text class="info-value">{{detail.min_scores || '未知'}}</text>
					</view>
					<view class="info-item">
						<text class="info-label">最高分数</text>
						<text class="info-value">{{detail.max_scores || '未知'}}</text>
					</view>
					<view class="info-item">
						<text class="info-label">年份</text>
						<text class="info-value">{{formatDate(detail.year) || '未知'}}</text>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
import request from '../../../utils/request.js';

export default {
	data() {
		return {
			id: null,
			loading: true,
			error: false,
			errorMessage: '加载失败',
			detail: {},
			defaultImage: '/static/c1.png'
		}
	},
	onLoad(options) {
		console.log('志愿详情页面参数:', options);
		if (options.id) {
			this.id = options.id;
			this.fetchDetail();
		} else {
			this.error = true;
			this.errorMessage = '缺少必要参数';
			this.loading = false;
		}
	},
	methods: {
		// 获取志愿详情
		fetchDetail() {
			this.loading = true;
			this.error = false;
			
			// 获取单个志愿详情
			request.post('/specialized/getAdmissionDetail', {
				admission_id: this.id
			})
			.then(res => {
				console.log('获取志愿详情成功:', res);
				if (res.code === 200 && res.result) {
					this.detail = res.result;
				} else {
					this.error = true;
					this.errorMessage = res.message || '获取志愿详情失败';
				}
			})
			.catch(err => {
				console.error('获取志愿详情异常:', err);
				this.error = true;
				this.errorMessage = '网络请求失败';
			})
			.finally(() => {
				this.loading = false;
			});
		},
		
		// 返回上一页
		goBack() {
			uni.navigateBack();
		},
		
		// 获取学校图片
		getSchoolImage(item) {
			// 如果有学校图片且不为空，则使用学校图片
			if (item.school_image && item.school_image.trim() !== '') {
				// 检查是否是完整URL
				if (item.school_image.startsWith('http')) {
					return item.school_image;
				} else {
					// 如果不是完整URL，则拼接基础URL
					return request.baseUrl + item.school_image;
				}
			}
			// 否则使用默认图片
			return this.defaultImage;
		},
		
		// 获取状态对应的CSS类
		getStatusClass(status) {
			if (typeof status === 'string') {
				if (status.includes('已录取')) {
					return 'status-success';
				} else if (status.includes('待录取')) {
					return 'status-pending';
				} else if (status.includes('未录取')) {
					return 'status-fail';
				}
			}
			
			// 如果是数字代码
			switch(status) {
				case '1':
					return 'status-success';
				case '0':
					return 'status-pending';
				case '2':
					return 'status-fail';
				default:
					return 'status-pending';
			}
		},
		
		// 获取状态对应的文本
		getStatusText(status) {
			// 如果已经是中文状态，直接返回
			if (typeof status === 'string' && (
				status.includes('已录取') || 
				status.includes('待录取') || 
				status.includes('未录取')
			)) {
				return status;
			}
			
			// 如果是数字代码，转换为中文
			switch(status) {
				case '1':
					return '已录取';
				case '0':
					return '待录取';
				case '2':
					return '未录取';
				default:
					return '待录取';
			}
		},
		
		// 格式化日期
		formatDate(dateStr) {
			if (!dateStr) return '';
			
			try {
				const date = new Date(dateStr);
				return date.getFullYear() + '年';
			} catch (e) {
				console.error('日期格式化错误:', e);
				return dateStr;
			}
		}
	}
}
</script>

<style>
.detail-container {
	background-color: #f5f5f5;
	min-height: 100vh;
	padding-bottom: 20px;
}

.loading-container, .error-container {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	height: 300px;
}

.loading-text, .error-text {
	margin-top: 15px;
	color: #999;
}

.retry-btn {
	margin-top: 15px;
	padding: 6px 15px;
	background-color: #007aff;
	color: #fff;
	border-radius: 4px;
	font-size: 14px;
}

.detail-content {
	padding: 15px;
}

.detail-card {
	background-color: #fff;
	border-radius: 8px;
	margin-bottom: 15px;
	padding: 15px;
	box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.card-header {
	display: flex;
	align-items: center;
	margin-bottom: 15px;
}

.school-logo {
	width: 60px;
	height: 60px;
	border-radius: 8px;
	margin-right: 15px;
	object-fit: cover;
}

.school-info {
	flex: 1;
}

.school-name {
	font-size: 18px;
	font-weight: bold;
	margin-bottom: 5px;
	display: block;
}

.school-major {
	font-size: 14px;
	color: #666;
	margin-bottom: 8px;
	display: block;
}

.status-tag {
	display: inline-block;
	padding: 3px 8px;
	border-radius: 3px;
	font-size: 12px;
}

.status-success {
	color: #34c759;
	background-color: rgba(52, 199, 89, 0.1);
}

.status-pending {
	color: #ff9500;
	background-color: rgba(255, 149, 0, 0.1);
}

.status-fail {
	color: #ff3b30;
	background-color: rgba(255, 59, 48, 0.1);
}

.card-title {
	display: flex;
	align-items: center;
	margin-bottom: 15px;
	font-size: 16px;
	font-weight: bold;
}

.card-title uni-icons {
	margin-right: 8px;
}

.info-list {
	
}

.info-item {
	display: flex;
	margin-bottom: 10px;
}

.info-label {
	width: 90px;
	color: #666;
	font-size: 14px;
}

.info-value {
	flex: 1;
	font-size: 14px;
}

.info-section {
	margin-bottom: 15px;
}

.section-title {
	font-size: 15px;
	font-weight: bold;
	color: #333;
	margin-bottom: 8px;
	display: block;
}

.section-content {
	font-size: 14px;
	color: #666;
	line-height: 1.5;
	display: block;
	white-space: pre-wrap;
}
</style>



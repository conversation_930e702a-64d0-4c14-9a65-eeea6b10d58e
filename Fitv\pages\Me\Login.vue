<template>
	<view class="login-container">
		<!-- 顶部背景 -->
		<view class="login-header">
			<image class="login-bg" src="/static/e1.jpg" mode="aspectFill"></image>
			<view class="login-title">
				<text class="title-text">志愿填报系统</text>
				<text class="subtitle-text">助力每一位考生选择理想院校</text>
			</view>
		</view>
		
		<!-- 登录表单 -->
		<view class="login-form">
			<!-- 登录方式切换 -->
			<view class="login-tabs">
				<view 
					:class="['tab-item', loginType === 'account' ? 'active' : '']" 
					@click="switchLoginType('account')"
				>账号登录</view>
				<view 
					:class="['tab-item', loginType === 'code' ? 'active' : '']" 
					@click="switchLoginType('code')"
				>验证码登录</view>
			</view>
			
			<!-- 账号密码登录 -->
			<view class="form-box" v-if="loginType === 'account'">
				<view class="input-item">
					<uni-icons type="person" size="20" color="#999"></uni-icons>
					<input type="text" v-model="accountForm.username" placeholder="请输入账号/手机号" class="input" />
				</view>
				<view class="input-item">
					<uni-icons type="locked" size="20" color="#999"></uni-icons>
					<input type="password" v-model="accountForm.password" placeholder="请输入密码" class="input" />
				</view>
			</view>
			
			<!-- 验证码登录 -->
			<view class="form-box" v-else>
				<view class="input-item">
					<uni-icons type="phone" size="20" color="#999"></uni-icons>
					<input type="number" v-model="codeForm.phone" placeholder="请输入手机号" class="input" maxlength="11" />
				</view>
				<view class="input-item">
					<uni-icons type="locked" size="20" color="#999"></uni-icons>
					<input type="number" v-model="codeForm.code" placeholder="请输入验证码" class="input" maxlength="6" />
					<view class="code-btn" @click="sendCode">{{codeText}}</view>
				</view>
			</view>
			
			<!-- 记住密码和忘记密码 -->
			<view class="form-options">
				<view class="remember-box" @click="toggleRemember">
					<view :class="['checkbox', isRemember ? 'checked' : '']"></view>
					<text class="remember-text">记住密码</text>
				</view>
				<text class="forget-text" @click="forgetPassword">忘记密码?</text>
			</view>
			
			<!-- 登录按钮 -->
			<button class="login-btn" @click="login" :loading="loading">登录</button>
			
			<!-- 其他登录方式 -->
			<view class="other-login">
				<view class="divider">
					<view class="line"></view>
					<text class="or-text">其他登录方式</text>
					<view class="line"></view>
				</view>
				<view class="login-icons">
					<view class="icon-item">
						<view class="icon-box wechat">
							<uni-icons type="weixin" size="30" color="#fff"></uni-icons>
						</view>
						<text class="icon-text">微信</text>
					</view>
					<view class="icon-item">
						<view class="icon-box qq">
							<uni-icons type="qq" size="30" color="#fff"></uni-icons>
						</view>
						<text class="icon-text">QQ</text>
					</view>
					<view class="icon-item">
						<view class="icon-box weibo">
							<uni-icons type="weibo" size="30" color="#fff"></uni-icons>
						</view>
						<text class="icon-text">微博</text>
					</view>
				</view>
			</view>
			
			<!-- 注册入口 -->
			<view class="register-box">
				<text class="register-text">还没有账号？</text>
				<text class="register-btn" @click="toRegister">立即注册</text>
			</view>
		</view>
	</view>
</template>

<script>
	import request from '@/utils/request';
	
	export default {
		data() {
			return {
				loginType: 'account', // account: 账号登录, code: 验证码登录
				accountForm: {
					username: '',
					password: ''
				},
				codeForm: {
					phone: '',
					code: ''
				},
				isRemember: false,
				codeText: '获取验证码',
				timer: null,
				countdown: 60,
				loading: false
			}
		},
		onLoad() {
			// 检查是否有记住的账号密码
			const savedAccount = uni.getStorageSync('savedAccount');
			if (savedAccount) {
				try {
					const account = JSON.parse(savedAccount);
					this.accountForm.username = account.username;
					this.accountForm.password = account.password;
					this.isRemember = true;
				} catch (e) {
					console.error('解析保存的账号密码失败', e);
				}
			}
		},
		methods: {
			// 切换登录方式
			switchLoginType(type) {
				this.loginType = type;
			},
			
			// 切换记住密码
			toggleRemember() {
				this.isRemember = !this.isRemember;
			},
			
			// 忘记密码
			forgetPassword() {
				uni.navigateTo({
					url: '/pages/Me/forget/forget'
				});
			},
			
			// 发送验证码
			sendCode() {
				// 检查是否在倒计时中
				if (this.codeText !== '获取验证码') {
					return;
				}
				
				// 检查手机号
				if (!this.codeForm.phone || this.codeForm.phone.length !== 11) {
					uni.showToast({
						title: '请输入正确的手机号',
						icon: 'none'
					});
					return;
				}
				
				// 开始倒计时
				this.countdown = 60;
				this.codeText = `${this.countdown}s`;
				this.timer = setInterval(() => {
					this.countdown--;
					this.codeText = `${this.countdown}s`;
					if (this.countdown <= 0) {
						clearInterval(this.timer);
						this.codeText = '获取验证码';
					}
				}, 1000);
				
				// 发送验证码请求
				request.post('/auth/sendCode', {
					phone: this.codeForm.phone
				}).then(res => {
					uni.showToast({
						title: '验证码已发送',
						icon: 'success'
					});
				}).catch(err => {
					// 发送失败，停止倒计时
					clearInterval(this.timer);
					this.codeText = '获取验证码';
				});
			},
			
			// 登录
			login() {
				if (this.loading) return;
				
				if (this.loginType === 'account') {
					// 账号密码登录
					if (!this.accountForm.username || !this.accountForm.password) {
						uni.showToast({
							title: '请输入账号和密码',
							icon: 'none'
						});
						return;
					}
					
					this.loading = true;
					
					// 调用登录接口
					request.post('/auth/login', {
						username: this.accountForm.username,
						password: this.accountForm.password
					}).then(res => {
						console.log('登录响应数据:', res);
						
						// 检查登录结果
						if (res.code !== 200) {
							uni.showToast({
								title: res.message || '登录失败，用户名或密码错误',
								icon: 'none'
							});
							this.loading = false;
							return;
						}
						
						// 登录成功
						this.loading = false;
						
						// 生成token（如果后端没有返回token）
						const token = `user-${res.result.user_id}-${Date.now()}`;
						uni.setStorageSync('token', token);
						
						// 保存用户信息 - 使用res.result
						const userInfo = {
							id: res.result.user_id,
							username: res.result.username,
							name: res.result.nick_name || res.result.username,
							nick_name: res.result.nick_name,
							avatar: this.processAvatarUrl(res.result.user_image),
							sex: res.result.sex,
							birth: res.result.birth,
							phone: res.result.phone,
							identity: res.result.identity,
							user_school: res.result.user_school,
							user_fraction: res.result.user_fraction,
							user_form: res.result.user_form,
							// 新增学科成绩字段
							Chinese: res.result.Chinese,
							math: res.result.math,
							English: res.result.English,
							Arts: res.result.Arts,
							// 默认统计数据
							volunteerCount: 0,
							followCount: 0,
							fansCount: 0
						};
						
						console.log('准备存储的用户信息:', userInfo);
						uni.setStorageSync('userInfo', JSON.stringify(userInfo));
						
						// 记住密码
						if (this.isRemember) {
							uni.setStorageSync('savedAccount', JSON.stringify({
								username: this.accountForm.username,
								password: this.accountForm.password
							}));
						} else {
							uni.removeStorageSync('savedAccount');
						}
						
						uni.showToast({
							title: '登录成功',
							icon: 'success'
						});
						
						// 跳转到首页
						setTimeout(() => {
							uni.switchTab({
								url: '/pages/Home/Home'
							});
						}, 1500);
					}).catch(err => {
						this.loading = false;
						uni.showToast({
							title: err.message || '登录失败，请检查账号密码',
							icon: 'none'
						});
					});
				} else {
					// 验证码登录
					if (!this.codeForm.phone || this.codeForm.phone.length !== 11) {
						uni.showToast({
							title: '请输入正确的手机号',
							icon: 'none'
						});
						return;
					}
					
					if (!this.codeForm.code || this.codeForm.code.length !== 6) {
						uni.showToast({
							title: '请输入6位验证码',
							icon: 'none'
						});
						return;
					}
					
					this.loading = true;
					
					// 调用验证码登录接口
					request.post('/auth/loginByCode', {
						phone: this.codeForm.phone,
						code: this.codeForm.code
					}).then(res => {
						console.log('验证码登录响应数据:', res);
						
						// 检查登录结果
						if (res.code !== 200) {
							uni.showToast({
								title: res.message || '验证码错误或已过期',
								icon: 'none'
							});
							this.loading = false;
							return;
						}
						
						// 登录成功
						this.loading = false;
						
						// 生成token
						const token = `user-${res.result.user_id}-${Date.now()}`;
						uni.setStorageSync('token', token);
						
						// 保存用户信息 - 使用res.result
						const userInfo = {
							id: res.result.user_id,
							username: res.result.username,
							name: res.result.nick_name || res.result.username,
							nick_name: res.result.nick_name,
							avatar: this.processAvatarUrl(res.result.user_image),
							sex: res.result.sex,
							birth: res.result.birth,
							phone: res.result.phone,
							identity: res.result.identity,
							user_school: res.result.user_school,
							user_fraction: res.result.user_fraction,
							user_form: res.result.user_form,
							// 新增学科成绩字段
							Chinese: res.result.Chinese,
							math: res.result.math,
							English: res.result.English,
							Arts: res.result.Arts,
							// 默认统计数据
							volunteerCount: 0,
							followCount: 0,
							fansCount: 0
						};
						
						console.log('准备存储的用户信息:', userInfo);
						uni.setStorageSync('userInfo', JSON.stringify(userInfo));
						
						uni.showToast({
							title: '登录成功',
							icon: 'success'
						});
						
						// 跳转到首页
						setTimeout(() => {
							uni.switchTab({
								url: '/pages/Home/Home'
							});
						}, 1500);
					}).catch(err => {
						this.loading = false;
						uni.showToast({
							title: err.message || '验证码错误或已过期',
							icon: 'none'
						});
					});
				}
			},
			
			// 跳转到注册页
			toRegister() {
				uni.navigateTo({
					url: '/pages/Me/reg'
				});
			},

			// 处理头像URL
			processAvatarUrl(url) {
				if (!url) return '/static/c1.png';
				
				// 如果已经是完整URL，直接返回
				if (url.startsWith('http')) return url;
				
				// 如果是静态资源路径，直接返回
				if (url.startsWith('/static')) return url;
				
				// 确保路径格式正确
				if (!url.startsWith('/')) {
					url = '/' + url;
				}
				
				// 返回完整URL
				return request.baseUrl + url;
			}
		},
		// 组件销毁前清除定时器
		beforeDestroy() {
			if (this.timer) {
				clearInterval(this.timer);
			}
		}
	}
</script>

<style>
.login-container {
	min-height: 100vh;
	background-color: #fff;
	display: flex;
	flex-direction: column;
}

.login-header {
	position: relative;
	height: 220px;
	overflow: hidden;
}

.login-bg {
	width: 100%;
	height: 100%;
}

.login-title {
	position: absolute;
	top: 50%;
	left: 0;
	width: 100%;
	transform: translateY(-50%);
	text-align: center;
	color: #fff;
	z-index: 1;
}

.title-text {
	font-size: 28px;
	font-weight: bold;
	display: block;
	margin-bottom: 10px;
	text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
	color: #000;
}

.subtitle-text {
	font-size: 16px;
	display: block;
	text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
	color: #000;
}

.login-form {
	position: relative;
	margin-top: -20px;
	background-color: #fff;
	border-radius: 20px 20px 0 0;
	padding: 30px 20px;
	box-shadow: 0 -3px 10px rgba(0, 0, 0, 0.05);
}

.login-tabs {
	display: flex;
	margin-bottom: 30px;
}

.tab-item {
	flex: 1;
	text-align: center;
	font-size: 16px;
	color: #666;
	padding-bottom: 10px;
	position: relative;
}

.tab-item.active {
	color: #d4237a;
	font-weight: bold;
}

.tab-item.active::after {
	content: '';
	position: absolute;
	bottom: 0;
	left: 50%;
	transform: translateX(-50%);
	width: 30px;
	height: 3px;
	background-color: #d4237a;
	border-radius: 3px;
}

.input-item {
	display: flex;
	align-items: center;
	height: 50px;
	border-bottom: 1px solid #f0f0f0;
	margin-bottom: 20px;
}

.input {
	flex: 1;
	height: 100%;
	font-size: 16px;
	margin-left: 10px;
}

.code-btn {
	padding: 5px 10px;
	background-color: #f5f5f5;
	color: #d4237a;
	font-size: 14px;
	border-radius: 3px;
}

.form-options {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 30px;
}

.remember-box {
	display: flex;
	align-items: center;
}

.checkbox {
	width: 18px;
	height: 18px;
	border: 1px solid #ddd;
	border-radius: 3px;
	margin-right: 5px;
	position: relative;
}

.checkbox.checked {
	background-color: #d4237a;
	border-color: #d4237a;
}

.checkbox.checked::after {
	content: '';
	position: absolute;
	top: 2px;
	left: 6px;
	width: 4px;
	height: 8px;
	border-right: 2px solid #fff;
	border-bottom: 2px solid #fff;
	transform: rotate(45deg);
}

.remember-text {
	font-size: 14px;
	color: #666;
}

.forget-text {
	font-size: 14px;
	color: #d4237a;
}

.login-btn {
	width: 100%;
	height: 50px;
	line-height: 50px;
	background-color: #d4237a;
	color: #fff;
	font-size: 16px;
	border-radius: 25px;
	margin-bottom: 30px;
}

.other-login {
	margin-bottom: 30px;
}

.divider {
	display: flex;
	align-items: center;
	margin-bottom: 20px;
}

.line {
	flex: 1;
	height: 1px;
	background-color: #f0f0f0;
}

.or-text {
	padding: 0 15px;
	font-size: 14px;
	color: #999;
}

.login-icons {
	display: flex;
	justify-content: center;
}

.icon-item {
	display: flex;
	flex-direction: column;
	align-items: center;
	margin: 0 20px;
}

.icon-box {
	width: 50px;
	height: 50px;
	border-radius: 50%;
	display: flex;
	justify-content: center;
	align-items: center;
	margin-bottom: 5px;
}

.wechat {
	background-color: #07c160;
}

.qq {
	background-color: #12b7f5;
}

.weibo {
	background-color: #e6162d;
}

.icon-text {
	font-size: 12px;
	color: #666;
}

.register-box {
	display: flex;
	justify-content: center;
	align-items: center;
	margin-bottom: 30px;
	padding-bottom: env(safe-area-inset-bottom);
}

.register-text {
	font-size: 14px;
	color: #666;
}

.register-btn {
	font-size: 14px;
	color: #d4237a;
	margin-left: 5px;
}
</style>







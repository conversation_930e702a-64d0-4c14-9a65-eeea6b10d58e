<!-- 轮播图 -->

<template>
  <div class="banner-container">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <span>轮播图管理</span>
          <el-button type="primary" @click="handleAdd">添加轮播图</el-button>
        </div>
      </template>
      
      <el-table :data="tableData" style="width: 100%" v-loading="loading">
        <el-table-column prop="banner_id" label="ID" width="80" />
        <el-table-column prop="banner_name" label="标题" />
        <el-table-column label="图片" width="180">
          <template #default="scope">
            <el-image 
              :src="scope.row.banner_image" 
              :preview-src-list="[scope.row.banner_image]"
              :preview-teleported="true"
              :initial-index="0"
              preview-class="image-preview-custom"
              fit="cover"
              style="width: 100px; height: 60px;"
            />
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="scope">
            <el-button-group>
              <el-button type="primary" link @click="handleEdit(scope.row)">编辑</el-button>
              <el-button type="danger" link @click="handleDelete(scope.row)">删除</el-button>
            </el-button-group>
          </template>
        </el-table-column>
      </el-table>

      <div class="pagination-container">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 30, 50]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 添加/编辑对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogType === 'add' ? '添加轮播图' : '编辑轮播图'"
      width="500px"
    >
      <el-form :model="form" :rules="rules" ref="formRef" label-width="80px">
        <el-form-item label="标题" prop="banner_name">
          <el-input v-model="form.banner_name" placeholder="请输入标题" />
        </el-form-item>
        <el-form-item label="图片" prop="banner_image">
          <el-upload
            class="avatar-uploader"
            :auto-upload="false"
            :show-file-list="false"
            :on-change="handleFileChange"
            :before-upload="beforeUpload"
          >
            <img v-if="form.banner_image" :src="form.banner_image" class="avatar" />
            <el-icon v-else class="avatar-uploader-icon"><Plus /></el-icon>
          </el-upload>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSubmit">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus } from '@element-plus/icons-vue'
import { get, post, baseURL } from '@/utils/request'

const loading = ref(false)
const tableData = ref([])
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)
const dialogVisible = ref(false)
const dialogType = ref('add')
const formRef = ref(null)

const form = ref({
  banner_name: '',
  banner_image: '',
  imageFile: null
})

const rules = {
  banner_name: [
    { required: true, message: '请输入标题', trigger: 'blur' },
    { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  banner_image: [
    { required: true, message: '请上传图片', trigger: 'change' }
  ]
}

// 获取完整的图片URL
const getImageUrl = (path) => {
  if (!path) return ''
  if (path.startsWith('http')) {
    return path
  }
  return `${baseURL}${path}`
}

// 获取列表数据时处理图片URL
const getList = async () => {
  loading.value = true
  try {
    const res = await post('/banner/', {
      page: currentPage.value,
      pageSize: pageSize.value
    })
    if (res.code === 200) {
      // 处理图片URL
      tableData.value = res.result.map(item => ({
        ...item,
        banner_image: getImageUrl(item.banner_image)
      }))
      total.value = res.result.length
    }
  } catch (error) {
    console.error(error)
    ElMessage.error('获取数据失败')
  } finally {
    loading.value = false
  }
}

// 处理文件选择
const handleFileChange = (file) => {
  if (file && file.raw) {
    // 预览图片
    form.value.banner_image = URL.createObjectURL(file.raw)
    // 存储文件用于提交
    form.value.imageFile = file.raw
  }
}

const beforeUpload = (file) => {
  const isImage = file.type.startsWith('image/')
  const isLt2M = file.size / 1024 / 1024 < 2

  if (!isImage) {
    ElMessage.error('只能上传图片文件!')
    return false
  }
  if (!isLt2M) {
    ElMessage.error('图片大小不能超过 2MB!')
    return false
  }
  return true
}

// 添加
const handleAdd = () => {
  dialogType.value = 'add'
  form.value = {
    banner_name: '',
    banner_image: '',
    imageFile: null
  }
  dialogVisible.value = true
}

// 编辑时也需要处理图片URL
const handleEdit = (row) => {
  dialogType.value = 'edit'
  form.value = {
    banner_id: row.banner_id,
    banner_name: row.banner_name,
    banner_image: getImageUrl(row.banner_image),
    imageFile: null
  }
  dialogVisible.value = true
}

// 删除
const handleDelete = (row) => {
  ElMessageBox.confirm('确认删除该轮播图吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      const res = await post('/banner/delete', { banner_id: row.banner_id })
      if (res.code === 200) {
        ElMessage.success('删除成功')
        getList()
      }
    } catch (error) {
      console.error(error)
      ElMessage.error('删除失败')
    }
  })
}

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return
  await formRef.value.validate(async (valid) => {
    if (valid) {
      try {
        const url = dialogType.value === 'add' ? '/banner/upload' : '/banner/update'
        
        // 创建FormData用于文件上传
        const formData = new FormData()
        formData.append('banner_name', form.value.banner_name)
        
        if (form.value.imageFile) {
          formData.append('banner_image', form.value.imageFile)
        }
        
        if (dialogType.value === 'edit') {
          formData.append('banner_id', form.value.banner_id)
        }

        const res = await post(url, formData, {
          headers: {
            'Content-Type': 'multipart/form-data'
          }
        })
        
        if (res.code === 200) {
          ElMessage.success(dialogType.value === 'add' ? '添加成功' : '修改成功')
          dialogVisible.value = false
          getList()
        }
      } catch (error) {
        console.error(error)
        ElMessage.error(dialogType.value === 'add' ? '添加失败' : '修改失败')
      }
    }
  })
}

// 分页
const handleSizeChange = (val) => {
  pageSize.value = val
  getList()
}

const handleCurrentChange = (val) => {
  currentPage.value = val
  getList()
}

onMounted(() => {
  getList()
})
</script>

<style scoped>
.banner-container {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

.avatar-uploader {
  border: 1px dashed var(--el-border-color);
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: var(--el-transition-duration-fast);
}

.avatar-uploader:hover {
  border-color: var(--el-color-primary);
}

.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 178px;
  height: 178px;
  text-align: center;
  line-height: 178px;
}

.avatar {
  width: 178px;
  height: 178px;
  display: block;
}

:deep(.image-preview-custom) {
  z-index: 3000;
}
</style> 

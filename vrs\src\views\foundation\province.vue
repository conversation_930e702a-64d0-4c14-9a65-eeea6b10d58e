<template>
  <div class="province-container">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <span>省份管理</span>
          <el-button type="primary" @click="handleAdd">添加省份</el-button>
        </div>
      </template>
      
      <el-table :data="tableData" style="width: 100%" v-loading="loading">
        <el-table-column prop="id" label="ID" width="80" />
        <el-table-column prop="name" label="省份名称" />
        <el-table-column prop="code" label="行政代码" width="120" />
        <el-table-column prop="capital" label="省会城市" width="120" />
        <el-table-column prop="region" label="所属地区" width="120">
          <template #default="scope">
            <el-tag :type="getRegionType(scope.row.region)">
              {{ scope.row.region }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="scope">
            <el-button-group>
              <el-button type="primary" link @click="handleEdit(scope.row)">编辑</el-button>
              <el-button type="danger" link @click="handleDelete(scope.row)">删除</el-button>
            </el-button-group>
          </template>
        </el-table-column>
      </el-table>

      <div class="pagination-container">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 30, 50]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 添加/编辑对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogType === 'add' ? '添加省份' : '编辑省份'"
      width="500px"
    >
      <el-form :model="form" :rules="rules" ref="formRef" label-width="100px">
        <el-form-item label="省份名称" prop="name">
          <el-input v-model="form.name" placeholder="请输入省份名称" />
        </el-form-item>
        <el-form-item label="行政代码" prop="code">
          <el-input v-model="form.code" placeholder="请输入行政代码" />
        </el-form-item>
        <el-form-item label="省会城市" prop="capital">
          <el-input v-model="form.capital" placeholder="请输入省会城市" />
        </el-form-item>
        <el-form-item label="所属地区" prop="region">
          <el-select v-model="form.region" placeholder="请选择所属地区" style="width: 100%">
            <el-option label="华东" value="华东" />
            <el-option label="华南" value="华南" />
            <el-option label="华中" value="华中" />
            <el-option label="华北" value="华北" />
            <el-option label="西南" value="西南" />
            <el-option label="西北" value="西北" />
            <el-option label="东北" value="东北" />
            <el-option label="港澳台" value="港澳台" />
          </el-select>
        </el-form-item>
        <el-form-item label="排序" prop="sort">
          <el-input-number v-model="form.sort" :min="0" :max="999" />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-switch v-model="form.status" />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSubmit">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'

const loading = ref(false)
const tableData = ref([])
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)
const dialogVisible = ref(false)
const dialogType = ref('add')
const formRef = ref(null)

const form = ref({
  name: '',
  code: '',
  capital: '',
  region: ''
})

const rules = {
  name: [
    { required: true, message: '请输入省份名称', trigger: 'blur' },
    { min: 2, max: 20, message: '长度在 2 到 20 个字符', trigger: 'blur' }
  ],
  code: [
    { required: true, message: '请输入行政代码', trigger: 'blur' },
    { pattern: /^\d{6}$/, message: '行政代码必须为6位数字', trigger: 'blur' }
  ],
  capital: [
    { required: true, message: '请输入省会城市', trigger: 'blur' }
  ],
  region: [
    { required: true, message: '请选择所属地区', trigger: 'change' }
  ]
}

// 获取地区类型
const getRegionType = (region) => {
  const types = {
    '华东': '',
    '华南': 'success',
    '华中': 'warning',
    '华北': 'danger',
    '西南': 'info',
    '西北': 'warning',
    '东北': 'danger',
    '港澳台': ''
  }
  return types[region] || ''
}

// 获取列表数据
const getList = async () => {
  loading.value = true
  try {
    // 使用静态数据
    tableData.value = [
      { id: 1, name: '北京市', code: '110000', capital: '北京', region: '华北' },
      { id: 2, name: '上海市', code: '310000', capital: '上海', region: '华东' },
      { id: 3, name: '天津市', code: '120000', capital: '天津', region: '华北' },
      { id: 4, name: '重庆市', code: '500000', capital: '重庆', region: '西南' },
      { id: 5, name: '河北省', code: '130000', capital: '石家庄', region: '华北' },
      { id: 6, name: '山西省', code: '140000', capital: '太原', region: '华北' },
      { id: 7, name: '辽宁省', code: '210000', capital: '沈阳', region: '东北' },
      { id: 8, name: '吉林省', code: '220000', capital: '长春', region: '东北' },
      { id: 9, name: '黑龙江省', code: '230000', capital: '哈尔滨', region: '东北' },
      { id: 10, name: '江苏省', code: '320000', capital: '南京', region: '华东' },
      { id: 11, name: '浙江省', code: '330000', capital: '杭州', region: '华东' },
      { id: 12, name: '安徽省', code: '340000', capital: '合肥', region: '华东' },
      { id: 13, name: '福建省', code: '350000', capital: '福州', region: '华东' },
      { id: 14, name: '江西省', code: '360000', capital: '南昌', region: '华东' },
      { id: 15, name: '山东省', code: '370000', capital: '济南', region: '华东' },
      { id: 16, name: '河南省', code: '410000', capital: '郑州', region: '华中' },
      { id: 17, name: '湖北省', code: '420000', capital: '武汉', region: '华中' },
      { id: 18, name: '湖南省', code: '430000', capital: '长沙', region: '华中' },
      { id: 19, name: '广东省', code: '440000', capital: '广州', region: '华南' },
      { id: 20, name: '广西壮族自治区', code: '450000', capital: '南宁', region: '华南' },
      { id: 21, name: '海南省', code: '460000', capital: '海口', region: '华南' },
      { id: 22, name: '四川省', code: '510000', capital: '成都', region: '西南' },
      { id: 23, name: '贵州省', code: '520000', capital: '贵阳', region: '西南' },
      { id: 24, name: '云南省', code: '530000', capital: '昆明', region: '西南' },
      { id: 25, name: '西藏自治区', code: '540000', capital: '拉萨', region: '西南' },
      { id: 26, name: '陕西省', code: '610000', capital: '西安', region: '西北' },
      { id: 27, name: '甘肃省', code: '620000', capital: '兰州', region: '西北' },
      { id: 28, name: '青海省', code: '630000', capital: '西宁', region: '西北' },
      { id: 29, name: '宁夏回族自治区', code: '640000', capital: '银川', region: '西北' },
      { id: 30, name: '新疆维吾尔自治区', code: '650000', capital: '乌鲁木齐', region: '西北' },
      { id: 31, name: '香港特别行政区', code: '810000', capital: '香港', region: '港澳台' },
      { id: 32, name: '澳门特别行政区', code: '820000', capital: '澳门', region: '港澳台' },
      { id: 33, name: '台湾省', code: '710000', capital: '台北', region: '港澳台' }
    ]
    total.value = tableData.value.length
  } catch (error) {
    console.error(error)
    ElMessage.error('获取数据失败')
  } finally {
    loading.value = false
  }
}

// 添加
const handleAdd = () => {
  dialogType.value = 'add'
  form.value = {
    name: '',
    code: '',
    capital: '',
    region: ''
  }
  dialogVisible.value = true
}

// 编辑
const handleEdit = (row) => {
  dialogType.value = 'edit'
  form.value = { ...row }
  dialogVisible.value = true
}

// 删除
const handleDelete = (row) => {
  ElMessageBox.confirm('确认删除该省份吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      // TODO: 调用删除API
      ElMessage.success('删除成功')
      getList()
    } catch (error) {
      console.error(error)
      ElMessage.error('删除失败')
    }
  })
}

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return
  await formRef.value.validate(async (valid) => {
    if (valid) {
      try {
        // TODO: 调用添加/编辑API
        ElMessage.success(dialogType.value === 'add' ? '添加成功' : '编辑成功')
        dialogVisible.value = false
        getList()
      } catch (error) {
        console.error(error)
        ElMessage.error(dialogType.value === 'add' ? '添加失败' : '编辑失败')
      }
    }
  })
}

// 分页
const handleSizeChange = (val) => {
  pageSize.value = val
  getList()
}

const handleCurrentChange = (val) => {
  currentPage.value = val
  getList()
}

onMounted(() => {
  getList()
})
</script>

<style scoped>
.province-container {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}
</style> 

//超级管理员接口

const express = require('express');
const router = express.Router();
const db = require('../config/db');
const multer = require('multer');
const path = require('path');

// 配置 multer 存储
const storage = multer.diskStorage({
    destination: function (req, file, cb) {
        cb(null, path.join(__dirname, '../../public/uploads/'));
    },
    filename: function (req, file, cb) {
        // 生成唯一的文件名
        const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
        cb(null, uniqueSuffix + path.extname(file.originalname));
    }
});

// 文件过滤器
const fileFilter = (req, file, cb) => {
    if (file.mimetype.startsWith('image/')) {
        cb(null, true);
    } else {
        cb(new Error('只允许上传图片文件！'), false);
    }
};

const upload = multer({ 
    storage: storage,
    fileFilter: fileFilter,
    limits: {
        fileSize: 5 * 1024 * 1024 // 限制文件大小为5MB
    }
});


router.post('/',(req,res)=>{
    const sql='select * from administrator'
    db.query(sql,(err,result)=>{
        if(err){
            res.send({
                code:201,
                success:"失败"
            })
        }else{
            res.send({
                code:200,
                success:"成功",
                result:result
            })
        }
    })
})

router.post('/login', (req, res) => {
    // 登录逻辑
    const { administrator_name, administrator_password } = req.body;
    const sql = `select * from administrator where administrator_name='${administrator_name}'
     and administrator_password='${administrator_password}'`;
    db.query(sql, (err, result) => {
        if (err) {
            res.send({
                code: 201,
                success: "失败"
            });
        } else if (result.length === 0) {
            res.send({
                code: 201,
                success: "失败"
            });
        } else {
            res.send({
                code: 200,
                success: "成功",
                result: result[0]
            });
        }
    });
});

router.post('/register', upload.single('administrator_image'), (req, res) => {
    const {
        administrator_name,
        administrator_password,
        administrator_phone,
        administrator_realname,
        administrator_email
    } = req.body;
    
    // 检查用户名是否已存在
    const checkUserSql = 'SELECT * FROM administrator WHERE administrator_name = ?';
    db.query(checkUserSql, [administrator_name], (err, result) => {
        if (err) {
            return res.status(500).json({
                code: 201,
                success: "失败",
                message: "服务器错误"
            });
        }
        
        if (result.length > 0) {
            return res.status(400).json({
                code: 201,
                success: "失败",
                message: "用户名已存在"
            });
        }

        // 获取上传的图片路径
        const administrator_image = req.file ? `/uploads/${req.file.filename}` : null;

        // 插入新用户
        const insertSql = `
            INSERT INTO administrator (
                administrator_name,
                administrator_password,
                administrator_phone,
                administrator_realname,
                administrator_email,
                administrator_image
            ) 
            VALUES (?, ?, ?, ?, ?, ?)
        `;
        
        const values = [
            administrator_name,
            administrator_password,
            administrator_phone,
            administrator_realname,
            administrator_email,
            administrator_image
        ];
        
        db.query(insertSql, values, (err, result) => {
            if (err) {
                return res.status(500).json({
                    code: 201,
                    success: "失败",
                    message: "注册失败",
                    error: err.message
                });
            }
            
            res.status(200).json({
                code: 200,
                success: "成功",
                message: "注册成功",
                data: result
            });
        });
    });
});


//注销超级管理员身份
router.post('/delete',(req,res)=>{
    const sql='delete from administrator where administrator_id=?'
    db.query(sql,[req.body.administrator_id],(err,result)=>{
        if(err){
            res.send({
                code:201,
                message:'注销失败'
            })
        }else{
            res.send({
                code:200,
                message:'注销成功'
            })
        }
    })
})


//修改超级管理员信息
router.post('/update', upload.single('administrator_image'), (req, res) => {
    const {
        administrator_id,
        administrator_name,
        administrator_password,
        administrator_phone,
        administrator_realname,
        administrator_email
    } = req.body;

    // 检查用户名是否已被其他用户使用
    const checkUserSql = 'SELECT * FROM administrator WHERE administrator_name = ? AND administrator_id != ?';
    db.query(checkUserSql, [administrator_name, administrator_id], (err, result) => {
        if (err) {
            return res.status(500).json({
                code: 201,
                message: '服务器错误'
            });
        }

        if (result.length > 0) {
            return res.status(400).json({
                code: 201,
                message: '用户名已被使用'
            });
        }

        // 构建更新SQL语句
        let updateSql = `
            UPDATE administrator 
            SET administrator_name = ?,
                administrator_password = ?,
                administrator_phone = ?,
                administrator_realname = ?,
                administrator_email = ?
        `;
        
        // 构建参数数组
        let params = [
            administrator_name,
            administrator_password,
            administrator_phone,
            administrator_realname,
            administrator_email
        ];

        // 如果上传了新图片，添加图片路径更新
        if (req.file) {
            updateSql += ', administrator_image = ?';
            params.push(`/uploads/${req.file.filename}`);
        }

        // 添加WHERE条件
        updateSql += ' WHERE administrator_id = ?';
        params.push(administrator_id);

        // 执行更新操作
        db.query(updateSql, params, (err, result) => {
            if (err) {
                return res.status(500).json({
                    code: 201,
                    message: '修改失败',
                    error: err.message
                });
            }

            if (result.affectedRows === 0) {
                return res.status(400).json({
                    code: 201,
                    message: '未找到要修改的管理员'
                });
            }

            // 查询更新后的管理员信息
            const selectSql = 'SELECT * FROM administrator WHERE administrator_id = ?';
            db.query(selectSql, [administrator_id], (err, rows) => {
                if (err) {
                    return res.status(500).json({
                        code: 201,
                        message: '获取更新后的信息失败'
                    });
                }

                res.status(200).json({
                    code: 200,
                    message: '修改成功',
                    result: rows[0]
                });
            });
        });
    });
});

module.exports = router;

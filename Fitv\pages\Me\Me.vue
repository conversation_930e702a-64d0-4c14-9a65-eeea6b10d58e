<!-- 我的页面 -->

<template>
	<view class="me-container">
		<!-- 用户信息 -->
		<view class="user-card">
			<view class="user-info-section">
				<image class="avatar" :src="userInfo.avatar" @error="handleAvatarError"></image>
				<view class="user-details">
					<view class="username">{{userInfo.name}}</view>
					<view class="user-id">ID: {{userInfo.id}}</view>
				</view>
				<view class="edit-btn" @click="editProfile">
					<uni-icons type="compose" size="16" color="#666"></uni-icons>
					<text class="edit-text">编辑</text>
				</view>
			</view>
			
			<!-- 用户数据 -->
			<view class="user-data">
				<view class="data-item" @click="Volunteer()">
					<text class="data-num">{{userInfo.volunteerCount}}</text>
					<text class="data-label">志愿</text>
				</view>
				<view class="data-item" @click="goToConcern('following')">
					<text class="data-num">{{userInfo.followCount}}</text>
					<text class="data-label">关注</text>
				</view>
				<view class="data-item" @click="goToConcern('fans')">
					<text class="data-num">{{userInfo.fansCount}}</text>
					<text class="data-label">粉丝</text>
				</view>
			</view>
		</view>
		
		<!-- 功能菜单 -->
		<view class="menu-section">
			<view class="menu-title">我的服务</view>
			<view class="menu-grid">
				<view class="menu-item" v-for="(item, index) in menuItems" :key="index" @click="navigateTo(item.path)">
					<view class="menu-icon" :style="{backgroundColor: item.bgColor}">
						<uni-icons :type="item.icon" size="24" color="#fff"></uni-icons>
					</view>
					<text class="menu-name">{{item.name}}</text>
				</view>
			</view>
		</view>
		
		<!-- 我的成绩 -->
		<view class="score-section">
			<view class="section-header">
				<text class="section-title">我的成绩</text>
				<text class="more" @click="navigateTo('/pages/score/score')">查看详情 ></text>
			</view>
			<view class="score-card">
				<view class="score-item">
					<text class="score-label">高考总分</text>
					<text class="score-value">{{calculateTotal()}}</text>
				</view>
				<view class="score-divider"></view>
				<view class="score-subjects">
					<view class="subject-item">
						<text class="subject-name">语文</text>
						<text class="subject-score">{{userSubjects.Chinese || '-'}}</text>
					</view>
					<view class="subject-item">
						<text class="subject-name">数学</text>
						<text class="subject-score">{{userSubjects.math || '-'}}</text>
					</view>
					<view class="subject-item">
						<text class="subject-name">英语</text>
						<text class="subject-score">{{userSubjects.English || '-'}}</text>
					</view>
					<view class="subject-item">
						<text class="subject-name">文科综合</text>
						<text class="subject-score">{{userSubjects.Arts || '-'}}</text>
					</view>
				</view>
			</view>
		</view>
		
		<!-- 志愿记录 - 使用Volunteer组件 -->
		<volunteer-records 
			:userId="userInfo.id" 
			:limit="3" 
			@view-all="navigateTo('/pages/volunteer-history/volunteer-history')"
		></volunteer-records>
	</view>
</template>

<script>
	import VolunteerRecords from './moudle/Volunteer.vue';
	import request from '../../utils/request.js';
	
	export default {
		components: {
			VolunteerRecords
		},
		data() {
			return {
				userInfo: {
					name: '张同学',
					id: '10086',
					avatar: '/static/c1.png',
					volunteerCount: 0,
					followCount: 0,
					fansCount: 0
				},
				userSubjects: {
					Chinese: null,
					math: null,
					English: null,
					Arts: null
				},
				menuItems: [
					{ name: '成绩分析', icon: 'bars', bgColor: '#ff6b35', path: '/pages/Volunteer/function/grades' },
					{ name: '收藏院校', icon: 'star', bgColor: '#ff9500', path: '/pages/Me/function/collection' },
					{ name: '志愿填报', icon: 'compose', bgColor: '#34c759', path: '/pages/Volunteer/function/insert' },
					{ name: '专业测评', icon: 'help', bgColor: '#007aff', path: '/pages/Me/function/Evaluation' },
					{ name: '录取概率', icon: 'refreshempty', bgColor: '#5856d6', path: '/pages/Me/function/probability' },
					{ name: '志愿咨询', icon: 'chat', bgColor: '#ff2d55', path: '/pages/Volunteer/intelligent/intelligent' },
					{ name: '用户评论', icon: 'pyq', bgColor: '#ef57d9', path: '/pages/Me/function/user_comment' },
					{ name: '设置', icon: 'gear', bgColor: '#8e8e93', path: '/pages/Me/function/set' }
				]
			}
		},
		onShow() {
			this.getUserInfo();
			if (this.userInfo.id) {
				this.getVolunteerCount();
				this.getUserStats();
			}
		},
		methods: {
			// 跳转到关注页面
			goToConcern(tab = 'following') {
				uni.navigateTo({
					url: `/pages/Me/function/concerns?tab=${tab}`
				});
			},
			Volunteer(){
				uni.navigateTo({
					url:'/pages/Me/function/more_volunteer'
				})
			},
			
			// 获取用户信息
			getUserInfo() {
				try {
					const storedUserInfo = uni.getStorageSync('userInfo');
					
					if (storedUserInfo) {
						const userInfo = JSON.parse(storedUserInfo);
						console.log('获取用户信息成功，用户ID:', userInfo.id || userInfo.user_id);
						
						// 处理头像URL
						let avatarUrl = userInfo.avatar || userInfo.user_image || '/static/c1.png';
						
						if (avatarUrl && !avatarUrl.startsWith('http') && !avatarUrl.startsWith('/static')) {
							if (!avatarUrl.startsWith('/')) {
								avatarUrl = '/' + avatarUrl;
							}
							avatarUrl = request.baseUrl + avatarUrl;
						}
						
						// 更新用户信息
						this.userInfo = {
							name: userInfo.nick_name || userInfo.name || userInfo.username || this.userInfo.name,
							id: userInfo.id || userInfo.user_id || userInfo.userId || this.userInfo.id,
							avatar: avatarUrl,
							volunteerCount: 0,
							followCount: 0,
							fansCount: 0
						};
						
						// 更新学科成绩
						this.userSubjects = {
							Chinese: userInfo.Chinese || null,
							math: userInfo.math || null,
							English: userInfo.English || null,
							Arts: userInfo.Arts || null
						};
						
						// 获取真实数据
						this.getVolunteerCount();
						this.getUserStats();
					} else {
						console.warn('本地存储中未找到用户信息');
					}
				} catch (e) {
					console.error('获取用户信息失败:', e);
				}
			},
			
			// 获取用户志愿数量
			async getVolunteerCount() {
				if (!this.userInfo.id) {
					console.warn('用户ID为空，无法获取志愿数量');
					return;
				}
				
				try {
					const res = await request.post('/admission/getUserAdmissions', {
						user_id: this.userInfo.id
					});
					
					if (res.code === 200 && res.result) {
						this.userInfo.volunteerCount = res.result.length;
						console.log('获取志愿数量成功:', this.userInfo.volunteerCount);
					} else {
						this.userInfo.volunteerCount = 0;
						console.warn('获取志愿数量失败:', res.message);
					}
				} catch (err) {
					console.error('获取志愿数量异常:', err);
					this.userInfo.volunteerCount = 0;
				}
			},
			
			// 获取用户统计数据（关注和粉丝数量）
			async getUserStats() {
				if (!this.userInfo.id) {
					console.warn('用户ID为空，无法获取统计数据');
					return;
				}
				
				try {
					const res = await request.post('/concern/getUserStats', {
						user_id: this.userInfo.id
					});
					
					if (res.code === 200 && res.data) {
						this.userInfo.followCount = res.data.following_count || 0;
						this.userInfo.fansCount = res.data.fans_count || 0;
						console.log('获取统计数据成功 - 关注:', this.userInfo.followCount, '粉丝:', this.userInfo.fansCount);
					} else {
						this.userInfo.followCount = 0;
						this.userInfo.fansCount = 0;
						console.warn('获取统计数据失败:', res.message);
					}
				} catch (err) {
					console.error('获取统计数据异常:', err);
					this.userInfo.followCount = 0;
					this.userInfo.fansCount = 0;
				}
			},
			
			// 处理头像加载错误
			handleAvatarError() {
				this.userInfo.avatar = '/static/c1.png';
			},
			
			// 计算总分
			calculateTotal() {
				let total = 0;
				if (this.userSubjects.Chinese) total += parseInt(this.userSubjects.Chinese);
				if (this.userSubjects.math) total += parseInt(this.userSubjects.math);
				if (this.userSubjects.English) total += parseInt(this.userSubjects.English);
				if (this.userSubjects.Arts) total += parseInt(this.userSubjects.Arts);
				
				return total > 0 ? total : '-';
			},
			
			editProfile() {
				uni.navigateTo({
					url: '/pages/Me/function/edit'
				});
				console.log("去23")
			},
			navigateTo(path) {
				uni.navigateTo({
					url: path
				});
			},
			logout() {
				console.log('用户退出登录');
				uni.removeStorageSync('token');
				uni.removeStorageSync('userInfo');
				uni.removeStorageSync('savedAccount');
				
				uni.navigateTo({
					url: '/pages/Me/Login'
				});
			}
		}
	}
</script>

<style>
.me-container {
	padding-bottom: 20px;
	background-color: #f5f5f5;
}

.user-card {
	background-color: #fff;
	padding: 20px 15px;
}

.user-info-section {
	display: flex;
	align-items: center;
}

.avatar {
	width: 70px;
	height: 70px;
	border-radius: 50%;
	margin-right: 15px;
}

.user-details {
	flex: 1;
}

.username {
	font-size: 18px;
	font-weight: bold;
}

.user-id {
	font-size: 14px;
	color: #999;
	margin-top: 5px;
}

.edit-btn {
	display: flex;
	align-items: center;
	padding: 5px 10px;
	border: 1px solid #ddd;
	border-radius: 15px;
}

.edit-text {
	font-size: 12px;
	color: #666;
	margin-left: 5px;
}

.user-data {
	display: flex;
	margin-top: 20px;
}

.data-item {
	flex: 1;
	display: flex;
	flex-direction: column;
	align-items: center;
}

.data-num {
	font-size: 18px;
	font-weight: bold;
}

.data-label {
	font-size: 12px;
	color: #999;
	margin-top: 5px;
}

.menu-section {
	background-color: #fff;
	margin-top: 10px;
	padding: 15px;
}

.menu-title {
	font-size: 16px;
	font-weight: bold;
	margin-bottom: 15px;
}

.menu-grid {
	display: flex;
	flex-wrap: wrap;
}

.menu-item {
	width: 25%;
	display: flex;
	flex-direction: column;
	align-items: center;
	margin-bottom: 15px;
}

.menu-icon {
	width: 50px;
	height: 50px;
	border-radius: 50%;
	display: flex;
	justify-content: center;
	align-items: center;
	margin-bottom: 8px;
}

.menu-name {
	font-size: 12px;
}

.section-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 15px;
}

.section-title {
	font-size: 16px;
	font-weight: bold;
}

.more {
	font-size: 12px;
	color: #999;
}

.score-section, .volunteer-section {
	background-color: #fff;
	margin-top: 10px;
}

.score-card {
	padding: 0 15px 15px;
}

.score-item {
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.score-label {
	font-size: 16px;
	color: #333;
}

.score-value {
	font-size: 24px;
	font-weight: bold;
	color: #d4237a;
}

.score-divider {
	height: 1px;
	background-color: #f5f5f5;
	margin: 15px 0;
}

.score-subjects {
	display: flex;
	flex-wrap: wrap;
}

.subject-item {
	width: 100%;
	display: flex;
	justify-content: space-between;
	margin-bottom: 10px;
}

.subject-name {
	font-size: 14px;
	color: #666;
}

.subject-score {
	font-size: 14px;
	font-weight: bold;
}

.volunteer-list {
	padding: 0 15px 15px;
}

.volunteer-item {
	margin-bottom: 15px;
}

.volunteer-time {
	font-size: 12px;
	color: #999;
	margin-bottom: 5px;
}

.volunteer-card {
	background-color: #f9f9f9;
	border-radius: 8px;
	padding: 15px;
}

.school-info {
	display: flex;
	align-items: center;
}

.school-logo {
	width: 40px;
	height: 40px;
	border-radius: 5px;
	margin-right: 10px;
}

.school-detail {
	flex: 1;
}

.school-name {
	font-size: 16px;
	font-weight: bold;
}

.major-name {
	font-size: 14px;
	color: #666;
	margin-top: 5px;
}

.volunteer-status {
	padding: 3px 8px;
	border-radius: 3px;
	font-size: 12px;
}

.volunteer-status.success {
	color: #34c759;
	background-color: rgba(52, 199, 89, 0.1);
}

.volunteer-status.pending {
	color: #ff9500;
	background-color: rgba(255, 149, 0, 0.1);
}

.volunteer-status.fail {
	color: #ff3b30;
	background-color: rgba(255, 59, 48, 0.1);
}

.settings-section {
	margin-top: 10px;
	background-color: #fff;
}

.settings-list {
	padding: 0 15px;
}

.settings-item {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 15px 0;
	border-bottom: 1px solid #f5f5f5;
}

.settings-item:last-child {
	border-bottom: none;
}

.settings-left {
	display: flex;
	align-items: center;
}

.settings-name {
	font-size: 16px;
	margin-left: 10px;
}
</style>






















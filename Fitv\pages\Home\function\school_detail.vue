<!-- 学校详情 -->

<template>
	<view class="school-detail">
		<!-- 加载状态 -->
		<view v-if="loading" class="loading-container">
			<text>加载中...</text>
		</view>
		
		<!-- 错误状态 -->
		<view v-else-if="error" class="error-container">
			<text>{{errorMessage}}</text>
			<view class="retry-btn" @click="getSchoolDetail">重试</view>
		</view>
		
		<!-- 学校详情内容 -->
		<view v-else>
			<!-- 头部信息 -->
			<view class="header-section">
				<image 
					:src="getSchoolImage()" 
					mode="aspectFill" 
					class="school-banner"
				></image>
				<view class="school-info">
					<view class="school-basic">
						<image 
							:src="getSchoolImage()" 
							mode="aspectFill" 
							class="school-logo"
						></image>
						<view class="basic-info">
							<text class="school-name">{{getSchoolName()}}</text>
						</view>
					</view>
					<view class="action-buttons">
						<view class="action-btn favorite" @click="toggleFavorite">
							<uni-icons 
								:type="isCollected ? 'star-filled' : 'star'" 
								size="18" 
								:color="isCollected ? '#ff9500' : '#999'"
							></uni-icons>
							<text :style="{color: isCollected ? '#ff9500' : '#999'}">
								{{isCollected ? '已收藏' : '收藏'}}
							</text>
						</view>
						<view class="action-btn share">
							<uni-icons type="redo" size="18" color="#007aff"></uni-icons>
							<text>分享</text>
						</view>
					</view>
				</view>
			</view>

			<!-- 统计信息 -->
			<view class="stats-section">
				<view class="stat-item">
					<text class="stat-number">{{getScoreLine()}}</text>
					<text class="stat-label">录取分数线</text>
				</view>
				<view class="stat-item">
					<text class="stat-number">{{getSchoolNumber()}}</text>
					<text class="stat-label">招生人数</text>
				</view>
				<view class="stat-item">
					<text class="stat-number">{{getEmploymentRate()}}%</text>
					<text class="stat-label">就业率</text>
				</view>
			</view>

			<!-- 学校简介 -->
			<view class="section">
				<view class="section-title">
					<uni-icons type="info" size="18" color="#d4237a"></uni-icons>
					<text>学校简介</text>
				</view>
				<view class="section-content">
					<text class="content-text">{{getSchoolIdea()}}</text>
				</view>
			</view>

			<!-- 学校信息 -->
			<view class="section">
				<view class="section-title">
					<uni-icons type="contact" size="18" color="#d4237a"></uni-icons>
					<text>联系信息</text>
				</view>
				<view class="contact-info">
					<view class="contact-item">
						<uni-icons type="location" size="16" color="#666"></uni-icons>
						<text class="contact-label">地址：</text>
						<text class="contact-value">{{getSchoolAddress()}}</text>
					</view>
					<view class="contact-item">
						<uni-icons type="phone" size="16" color="#666"></uni-icons>
						<text class="contact-label">电话：</text>
						<text class="contact-value">{{getSchoolPhone()}}</text>
					</view>
					<view class="contact-item">
						<uni-icons type="email" size="16" color="#666"></uni-icons>
						<text class="contact-label">邮箱：</text>
						<text class="contact-value">{{getSchoolEmail()}}</text>
					</view>
					<view class="contact-item" v-if="getSchoolCharacteristic() !== '暂无'">
						<uni-icons type="star" size="16" color="#666"></uni-icons>
						<text class="contact-label">特色：</text>
						<text class="contact-value">{{getSchoolCharacteristic()}}</text>
					</view>
				</view>
			</view>

			<!-- 学校视频 -->
			<view v-if="getSchoolVideoUrl()" class="section">
				<view class="section-title">
					<uni-icons type="videocam" size="18" color="#d4237a"></uni-icons>
					<text>学校视频</text>
				</view>
				<view class="video-container">
					<video 
						:src="getSchoolVideoUrl()" 
						controls 
						class="school-video"
						:poster="getSchoolImage()"
						preload="auto"
						@error="handleVideoError"
						:show-center-play-btn="true"
						:show-play-btn="true"
						:enable-play-gesture="true"
						object-fit="fill"
					></video>
				</view>
			</view>

			<!-- 开设专业 -->
			<view class="section">
				<view class="section-title">
					<uni-icons type="star" size="18" color="#d4237a"></uni-icons>
					<text>开设专业</text>
				</view>
				<view v-if="realMajors.length > 0" class="major-list">
					<view 
						class="major-item" 
						v-for="(major, index) in realMajors" 
						:key="major.specialized_id"
						@click="viewMajorDetail(major)"
					>
						<text class="major-name">{{major.name}}</text>
						<text class="major-score">{{major.score}}</text>
					</view>
				</view>
				<view v-else class="no-majors">
					<text class="no-majors-text">暂无专业信息</text>
				</view>
			</view>

			<!-- 底部操作按钮 -->
			<view class="bottom-actions">
				<view class="action-button primary" @click="applyVolunteer">
					<uni-icons type="heart" size="20" color="#fff"></uni-icons>
					<text>填报志愿</text>
				</view>
				<view class="action-button secondary" @click="consultOnline">
					<uni-icons type="chat" size="20" color="#d4237a"></uni-icons>
					<text>在线咨询</text>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
import request from '../../../utils/request.js';
import { getUserId } from '../../../utils/user.js';

export default {
    name: 'SchoolDetail',
    data() {
        return {
            schoolId: null,
            schoolName: '',
            loading: true,
            error: false,
            errorMessage: '',
            schoolDetail: null,
            employmentRate: null,
            realMajors: [],
            isCollected: false
        }
    },
    
    onLoad(options) {
        this.schoolId = options.id || null;
        this.schoolName = options.name || '';
        this.employmentRate = Math.floor(Math.random() * (98 - 85 + 1)) + 85;
        
        if (this.schoolId) {
            this.getSchoolDetail();
        } else {
            this.showError('学校ID不能为空');
        }
    },
    
    methods: {
        // 显示错误状态
        showError(message) {
            this.error = true;
            this.errorMessage = message;
            this.loading = false;
        },
        
        // 获取学校详情
        async getSchoolDetail() {
            this.loading = true;
            this.error = false;
            
            try {
                const res = await request.post('/school/detail', { school_id: this.schoolId });
                if (res.code === 200 && res.result) {
                    this.schoolDetail = res.result;
                    this.processSchoolData();
                    this.checkCollectionStatus();
                } else {
                    this.showError(res.message || '获取学校详情失败');
                }
            } catch (err) {
                console.error('获取学校详情失败:', err);
                this.showError('网络请求失败');
            } finally {
                this.loading = false;
            }
        },
        
        // 处理学校数据
        processSchoolData() {
            if (this.schoolDetail?.specializedList) {
                const shuffled = this.schoolDetail.specializedList
                    .sort(() => 0.5 - Math.random())
                    .slice(0, 4);
                    
                this.realMajors = shuffled.map(item => ({
                    specialized_id: item.specialized_id,
                    name: item.specialized_content,
                    score: item.min_scores ? `${item.min_scores}分` : '暂无'
                }));
            }
        },
        
        // 安全获取学校信息
        getSchoolInfo(field, defaultValue = '暂无') {
            return this.schoolDetail?.[field] || defaultValue;
        },
        
        // 获取学校图片
        getSchoolImage() {
            const image = this.getSchoolInfo('school_image');
            if (!image || image === '暂无') return '/static/default-school.jpg';
            return image.startsWith('http') ? image : request.baseUrl + image;
        },
        
        // 获取基本信息
        getSchoolName() { return this.getSchoolInfo('school_name'); },
        getSchoolIdea() { return this.getSchoolInfo('school_idea'); },
        getSchoolNumber() { return this.getSchoolInfo('school_number'); },
        getSchoolAddress() { return this.getSchoolInfo('school_address'); },
        getSchoolPhone() { return this.getSchoolInfo('school_phone'); },
        getSchoolEmail() { return this.getSchoolInfo('school_email'); },
        getSchoolCharacteristic() { return this.getSchoolInfo('school_characteristic'); },
        getScoreLine() { return this.realMajors.length > 0 ? this.realMajors[0].score : '暂无'; },
        getEmploymentRate() { return this.employmentRate || 90; },
        
        // 获取学校视频URL
        getSchoolVideoUrl() {
            const video = this.getSchoolInfo('school_video');
            if (!video || video === '暂无') return '';
            return video.startsWith('http') ? video : request.baseUrl + video;
        },
        
        // 视频错误处理
        handleVideoError() {
            uni.showModal({
                title: '视频播放失败',
                content: '视频无法播放，请检查网络连接',
                showCancel: false
            });
        },
        
        // 检查收藏状态
        async checkCollectionStatus() {
            try {
                const userId = getUserId({ showLoginPrompt: false });
                if (!userId) return;
                
                const res = await request.post('/collection/checkCollection', {
                    user_id: userId,
                    school_id: this.schoolId
                });
                
                if (res.code === 200) {
                    this.isCollected = res.is_collected || false;
                }
            } catch (err) {
                console.error('检查收藏状态失败:', err);
            }
        },
        
        // 切换收藏状态
        async toggleFavorite() {
            try {
                const userId = getUserId({ showLoginPrompt: true });
                if (!userId) return;
                
                if (!this.schoolId) {
                    uni.showToast({ title: '学校信息不完整', icon: 'none' });
                    return;
                }
                
                uni.showLoading({ title: this.isCollected ? '取消收藏中...' : '收藏中...' });
                
                const endpoint = this.isCollected ? '/collection/removeCollection' : '/collection/addCollection';
                const res = await request.post(endpoint, {
                    user_id: userId,
                    school_id: this.schoolId
                });
                
                uni.hideLoading();
                
                if (res.code === 200) {
                    this.isCollected = !this.isCollected;
                    uni.showToast({
                        title: this.isCollected ? '收藏成功' : '取消收藏成功',
                        icon: 'success'
                    });
                } else {
                    uni.showToast({ title: res.message || '操作失败', icon: 'none' });
                }
            } catch (err) {
                uni.hideLoading();
                console.error('收藏操作失败:', err);
                uni.showToast({ title: '网络请求失败', icon: 'none' });
            }
        },
        
        // 页面操作
        viewMajorDetail(major) {
            uni.navigateTo({
                url: `/pages/Home/function/specialized_detail?id=${major.specialized_id}&name=${major.name}`
            });
        },
        
        applyVolunteer() {
            uni.navigateTo({
            	url:'/pages/Volunteer/function/insert'
            })
        },
        
        consultOnline() {
            const phone = this.getSchoolPhone();
            if (!phone || phone === '暂无') {
                uni.showModal({
                    title: '提示',
                    content: '该学校暂未提供联系电话',
                    showCancel: false
                });
                return;
            }
            
            uni.showModal({
                title: '学校联系电话',
                content: `电话：${phone}`,
                showCancel: false
            });
        }
    }
}
</script>

<style scoped>
.school-detail {
    background: #f8f9fa;
    min-height: 100vh;
    padding-bottom: 80px;
}

.loading-container, .error-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 50vh;
    color: #666;
}

.retry-btn {
    margin-top: 20px;
    padding: 10px 20px;
    background: #d4237a;
    color: #fff;
    border-radius: 5px;
}

.header-section {
    position: relative;
    background: #fff;
    margin-bottom: 12px;
}

.school-banner {
    width: 100%;
    height: 200px;
}

.school-info {
    padding: 16px;
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
}

.school-basic {
    display: flex;
    align-items: center;
    flex: 1;
}

.school-logo {
    width: 60px;
    height: 60px;
    border-radius: 8px;
    margin-right: 12px;
}

.basic-info {
    flex: 1;
}

.school-name {
    font-size: 20px;
    font-weight: bold;
    color: #333;
    margin-bottom: 4px;
}

.action-buttons {
    display: flex;
    gap: 12px;
}

.action-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 8px;
    border-radius: 8px;
    min-width: 30px;
}

.action-btn text {
    font-size: 12px;
    margin-top: 4px;
}

.favorite {
    background: #fff5e6;
}

.share {
    background: #e6f3ff;
    color: #007aff;
}

.stats-section {
    display: flex;
    background: #fff;
    padding: 16px;
    margin-bottom: 12px;
}

.stat-item {
    flex: 1;
    text-align: center;
    display: flex;
    flex-direction: column;
    align-items: center;
}

.stat-number {
    font-size: 18px;
    font-weight: bold;
    color: #d4237a;
    margin-bottom: 4px;
}

.stat-label {
    font-size: 12px;
    color: #666;
}

.section {
    background: #fff;
    margin-bottom: 12px;
    padding: 16px;
}

.section-title {
    display: flex;
    align-items: center;
    margin-bottom: 12px;
}

.section-title text {
    font-size: 16px;
    font-weight: bold;
    color: #333;
    margin-left: 8px;
}

.section-content {
    padding: 12px;
    background: #f8f9fa;
    border-radius: 8px;
}

.content-text {
    font-size: 14px;
    line-height: 1.6;
    color: #666;
}

.major-list {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.major-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px;
    background: #f8f9fa;
    border-radius: 8px;
    transition: background-color 0.2s;
}

.major-item:active {
    background-color: #f0f0f0;
}

.major-name {
    font-size: 14px;
    color: #333;
}

.major-score {
    font-size: 14px;
    color: #d4237a;
    font-weight: bold;
}

.no-majors {
    padding: 20px;
    text-align: center;
}

.no-majors-text {
    font-size: 14px;
    color: #999;
}

.bottom-actions {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    display: flex;
    padding: 12px 16px;
    background: #fff;
    border-top: 1px solid #f0f0f0;
    gap: 12px;
}

.action-button {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 12px;
    border-radius: 8px;
    gap: 8px;
}

.primary {
    background: #d4237a;
    color: #fff;
}

.secondary {
    background: #fff;
    color: #d4237a;
    border: 1px solid #d4237a;
}

.action-button text {
    font-size: 16px;
    font-weight: bold;
}

.video-container {
    border-radius: 8px;
    overflow: hidden;
}

.school-video {
    width: 100%;
    height: 250px;
    background: #000;
}

.contact-info {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.contact-item {
    display: flex;
    align-items: flex-start;
    padding: 12px;
    background: #f8f9fa;
    border-radius: 8px;
    gap: 8px;
}

.contact-label {
    font-size: 14px;
    color: #666;
    font-weight: 500;
    min-width: 40px;
}

.contact-value {
    font-size: 14px;
    color: #333;
    flex: 1;
    line-height: 1.4;
    word-break: break-all;
}
</style>









import request from './request.js';

/**
 * 获取用户信息
 * @param {boolean} showError 是否显示错误提示，默认true
 * @returns {Object|null} 用户信息对象或null
 */
export function getUserInfo(showError = true) {
    try {
        const userInfo = uni.getStorageSync('userInfo');
        if (userInfo) {
            return JSON.parse(userInfo);
        }
        
        if (showError) {
            uni.showToast({
                title: '请先登录',
                icon: 'none'
            });
            setTimeout(() => {
                uni.navigateTo({
                    url: '/pages/Me/Login'
                });
            }, 1500);
        }
        return null;
    } catch (e) {
        console.error('获取用户信息失败:', e);
        return null;
    }
}

/**
 * 获取用户ID
 * @returns {string|number|null} 用户ID或null
 */
export function getUserId() {
    const userInfo = getUserInfo(false);
    return userInfo ? (userInfo.user_id || userInfo.id) : null;
}

/**
 * 获取用户名
 * @returns {string} 用户名
 */
export function getUserName() {
    const userInfo = getUserInfo(false);
    return userInfo ? (userInfo.nick_name || userInfo.name || '用户') : '用户';
}

/**
 * 获取用户头像
 * @returns {string} 头像URL
 */
export function getUserAvatar() {
    const userInfo = getUserInfo(false);
    if (userInfo) {
        let avatar = userInfo.avatar || userInfo.user_image || '/static/c1.png';
        
        if (avatar && !avatar.startsWith('http') && !avatar.startsWith('/static')) {
            if (!avatar.startsWith('/')) {
                avatar = '/' + avatar;
            }
            avatar = request.baseUrl + avatar;
        }
        return avatar;
    }
    return '/static/c1.png';
}

/**
 * 检查用户是否已登录
 * @returns {boolean} 是否已登录
 */
export function isUserLoggedIn() {
    return getUserInfo(false) !== null;
}

/**
 * 保存用户信息
 * @param {Object} userInfo 用户信息对象
 */
export function saveUserInfo(userInfo) {
    try {
        uni.setStorageSync('userInfo', JSON.stringify(userInfo));
    } catch (e) {
        console.error('保存用户信息失败:', e);
    }
}

/**
 * 清除用户信息（退出登录）
 */
export function clearUserInfo() {
    try {
        uni.removeStorageSync('userInfo');
        uni.removeStorageSync('token');
    } catch (e) {
        console.error('清除用户信息失败:', e);
    }
}

// 默认导出
export default {
    getUserInfo,
    getUserId,
    getUserName,
    getUserAvatar,
    isUserLoggedIn,
    saveUserInfo,
    clearUserInfo
}

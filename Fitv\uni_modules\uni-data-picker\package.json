{"id": "uni-data-picker", "displayName": "uni-data-picker 数据驱动的picker选择器", "version": "2.0.0", "description": "单列、多列级联选择器，常用于省市区城市选择、公司部门选择、多级分类等场景", "keywords": ["uni-ui", "uniui", "picker", "级联", "省市区", ""], "repository": "https://github.com/dcloudio/uni-ui", "engines": {"HBuilderX": ""}, "directories": {"example": "../../temps/example_temps"}, "dcloudext": {"sale": {"regular": {"price": "0.00"}, "sourcecode": {"price": "0.00"}}, "contact": {"qq": ""}, "declaration": {"ads": "无", "data": "无", "permissions": "无"}, "npmurl": "https://www.npmjs.com/package/@dcloudio/uni-ui", "type": "component-vue"}, "uni_modules": {"dependencies": ["uni-load-more", "uni-icons", "uni-scss"], "encrypt": [], "platforms": {"cloud": {"tcb": "y", "aliyun": "y"}, "client": {"App": {"app-vue": "y", "app-nvue": "y", "app-uvue": "y"}, "H5-mobile": {"Safari": "y", "Android Browser": "y", "微信浏览器(Android)": "y", "QQ浏览器(Android)": "y"}, "H5-pc": {"Chrome": "y", "IE": "y", "Edge": "y", "Firefox": "y", "Safari": "y"}, "小程序": {"微信": "y", "阿里": "y", "百度": "y", "字节跳动": "y", "QQ": "y", "京东": "u"}, "快应用": {"华为": "u", "联盟": "u"}, "Vue": {"vue2": "y", "vue3": "y"}}}}}
const mysql = require('mysql');

// 数据库连接配置
const dbConfig = {
    host: 'localhost',        // 数据库服务器地址
    port: '3306',            // MySQL默认端口
    user: 'root',            // 数据库用户名
    password: '123456',      // 数据库密码
    database: 'Volunteer',   // 数据库名称
    connectionLimit: 10,     // 连接池最大连接数
    acquireTimeout: 60000,   // 获取连接的超时时间(60秒)
    timeout: 60000,          // 查询超时时间
    reconnect: true,         // 自动重连
    keepAliveInitialDelay: 0,// 保持连接活跃的初始延迟
    enableKeepAlive: true,   // 启用保持连接活跃
    idleTimeout: 300000,     // 空闲连接超时时间(5分钟)
    handleDisconnects: true  // 处理断开连接
};

// 创建数据库连接池
let pool = mysql.createPool(dbConfig);  // 创建连接池实例
let isPoolClosed = false;               // 标记连接池是否已关闭
let healthCheckInterval = null;         // 健康检查定时器

// 重新创建连接池的函数
function recreatePool() {
    console.log('正在重新创建数据库连接池...');
    
    // 清除健康检查定时器，避免在重建过程中继续检查
    if (healthCheckInterval) {
        clearInterval(healthCheckInterval);
        healthCheckInterval = null;
    }
    
    // 如果旧连接池存在且未关闭，先关闭它
    if (pool && !isPoolClosed) {
        isPoolClosed = true;
        pool.end(() => {
            console.log('旧连接池已关闭');
            createNewPool();  // 关闭完成后创建新连接池
        });
    } else {
        createNewPool();  // 直接创建新连接池
    }
}

function createNewPool() {
    pool = mysql.createPool(dbConfig);
    isPoolClosed = false;
    setupPoolEvents();
    startHealthCheck();
    console.log('新连接池已创建');
}

// 设置连接池事件监听
function setupPoolEvents() {
    // 监听新连接建立事件
    pool.on('connection', function (connection) {
        console.log('数据库连接已建立: ' + connection.threadId);
        
        // 为每个连接设置错误处理
        connection.on('error', function(err) {
            console.error('连接错误:', err);
            if (err.code === 'PROTOCOL_CONNECTION_LOST') {
                console.log('连接丢失，将在下次查询时重新连接');
            }
        });
    });

    // 监听连接池错误事件
    pool.on('error', function(err) {
        console.error('数据库连接池错误:', err);
        // 检查是否是可恢复的错误类型
        if (err.code === 'PROTOCOL_CONNECTION_LOST' || 
            err.code === 'ECONNRESET' || 
            err.code === 'ENOTFOUND' ||
            err.code === 'ETIMEDOUT') {
            // 3秒后自动重建连接池
            setTimeout(() => {
                recreatePool();
            }, 3000);
        }
    });
}

// 初始化连接池事件
setupPoolEvents();

// 改进的数据库查询方法
const query = (sql, params, callback, retryCount = 0) => {
    // 参数处理：如果第二个参数是函数，说明没有传params
    if (typeof params === 'function') {
        callback = params;
        params = [];
    }
    // 确保callback始终是函数，避免"callback is not a function"错误
    if (typeof callback !== 'function') {
        callback = function(err, results) {
            if (err) console.error('查询错误:', err);
        };
    }

    const maxRetries = 3;  // 最大重试次数
    
    // 检查连接池状态
    if (isPoolClosed) {
        console.log('连接池已关闭，等待重新创建...');
        setTimeout(() => {
            query(sql, params, callback, retryCount);
        }, 1000);
        return;
    }
    
    // 从连接池获取连接
    pool.getConnection((err, connection) => {
        if (err) {
            // 如果是连接相关错误且还有重试机会
            if ((err.code === 'PROTOCOL_CONNECTION_LOST' || 
                 err.code === 'ECONNRESET' || 
                 err.code === 'ETIMEDOUT' ||
                 err.message === 'Pool is closed.') && 
                retryCount < maxRetries) {
                
                // 递增延迟重试
                setTimeout(() => {
                    query(sql, params, callback, retryCount + 1);
                }, 2000 * (retryCount + 1));
                return;
            }
            
            callback(err, null);
            return;
        }
        
        // 执行查询
        connection.query(sql, params, (error, results) => {
            connection.release();  // 释放连接回连接池
            
            if (error) {
                // 查询错误时的重试逻辑
                if ((error.code === 'PROTOCOL_CONNECTION_LOST' || 
                     error.code === 'ECONNRESET') && 
                    retryCount < maxRetries) {
                    
                    setTimeout(() => {
                        query(sql, params, callback, retryCount + 1);
                    }, 2000 * (retryCount + 1));
                    return;
                }
            }
            
            callback(error, results);
        });
    });
};

// 健康检查函数
const healthCheck = () => {
    // 如果连接池已关闭，跳过检查
    if (isPoolClosed) {
        console.log('连接池已关闭，跳过健康检查');
        return;
    }
    
    // 执行简单查询检查连接状态
    query('SELECT 1 as health', [], (err, results) => {
        if (err) {
            console.error('数据库健康检查失败:', err);
        } else {
            console.log('数据库连接正常');
        }
    });
};

// 每30秒执行一次健康检查
function startHealthCheck() {
    if (healthCheckInterval) {
        clearInterval(healthCheckInterval);
    }
    healthCheckInterval = setInterval(healthCheck, 60000);
}

// 启动健康检查
startHealthCheck();

// 处理Ctrl+C信号
process.on('SIGINT', () => {
    console.log('正在关闭数据库连接池...');
    isPoolClosed = true;
    if (healthCheckInterval) {
        clearInterval(healthCheckInterval);  // 清除定时器
    }
    pool.end(() => {
        console.log('数据库连接池已关闭');
        process.exit(0);
    });
});

process.on('SIGTERM', () => {
    console.log('正在关闭数据库连接池...');
    isPoolClosed = true;
    if (healthCheckInterval) {
        clearInterval(healthCheckInterval);
    }
    pool.end(() => {
        console.log('数据库连接池已关闭');
        process.exit(0);
    });
});

// 导出查询方法和连接池
module.exports = {
    query,
    pool: () => pool,
    healthCheck,
    recreatePool
};

// 自动重连：连接丢失时自动重建连接池
// 重试机制：查询失败时自动重试，最多3次
// 健康检查：定期检查数据库连接状态
// 错误处理：全面的错误类型处理和恢复
// 资源管理：正确释放连接和清理定时器
// 优雅关闭：程序退出时正确关闭连接池
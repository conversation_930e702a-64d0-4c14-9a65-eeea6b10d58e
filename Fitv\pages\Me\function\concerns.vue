<!-- 用户关注页面 -->

<template>
	<view class="concern-container">
		<!-- 顶部统计 -->
		<view class="stats-header">
			<view class="stats-item">
				<text class="stats-number">{{followingCount}}</text>
				<text class="stats-label">关注</text>
			</view>
			<view class="stats-divider"></view>
			<view class="stats-item">
				<text class="stats-number">{{fansCount}}</text>
				<text class="stats-label">粉丝</text>
			</view>
		</view>
		
		<!-- 切换标签 -->
		<view class="tab-container">
			<view 
				:class="['tab-item', currentTab === 'following' ? 'active' : '']"
				@click="switchTab('following')"
			>
				我的关注
			</view>
			<view 
				:class="['tab-item', currentTab === 'fans' ? 'active' : '']"
				@click="switchTab('fans')"
			>
				我的粉丝
			</view>
		</view>
		
		<!-- 关注列表 -->
		<view class="user-list" v-if="currentTab === 'following'">
			<view class="empty-state" v-if="followingList.length === 0 && !loading">
				<image src="/static/empty.png" class="empty-image"></image>
				<text class="empty-text">暂无关注</text>
				<text class="empty-desc">去发现更多有趣的人吧</text>
			</view>
			
			<view class="user-item" v-for="(user, index) in followingList" :key="index" @click="viewUserProfile(user)">
				<image :src="user.avatar" class="user-avatar" @error="handleAvatarError"></image>
				<view class="user-info">
					<text class="user-name">{{user.name}}</text>
					<text class="user-desc">{{user.school}} · {{user.form}}</text>
					<text class="user-score">分数: {{user.fraction}}</text>
				</view>
				<view class="action-btn unfollow-btn" @click.stop="unfollowUser(index)">
					<text class="btn-text">已关注</text>
				</view>
			</view>
		</view>
		
		<!-- 粉丝列表 -->
		<view class="user-list" v-if="currentTab === 'fans'">
			<view class="empty-state" v-if="fansList.length === 0 && !loading">
				<image src="/static/empty.png" class="empty-image"></image>
				<text class="empty-text">暂无粉丝</text>
				<text class="empty-desc">分享更多内容来吸引粉丝吧</text>
			</view>
			
			<view class="user-item" v-for="(user, index) in fansList" :key="index" @click="viewUserProfile(user)">
				<image :src="user.avatar" class="user-avatar" @error="handleAvatarError"></image>
				<view class="user-info">
					<text class="user-name">{{user.name}}</text>
					<text class="user-desc">{{user.school}} · {{user.form}}</text>
					<text class="user-score">分数: {{user.fraction}}</text>
				</view>
				<view class="action-btn follow-btn" @click.stop="followUser(index)" v-if="!user.isFollowed">
					<text class="btn-text">关注</text>
				</view>
				<view class="action-btn followed-btn" v-else>
					<text class="btn-text">已关注</text>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
import request from '../../../utils/request.js';

export default {
    data() {
        return {
            currentTab: 'following',
            followingList: [],
            fansList: [],
            loading: false,
            userInfo: null,
            followingCount: 0,
            fansCount: 0
        }
    },
    onLoad(options) {
        // 获取传入的tab参数，默认为following
        const tab = options.tab || 'following';
        this.currentTab = tab;
        
        this.getUserInfo();
        this.loadConcernData();
        this.updateStats();
        
        // 根据传入的tab加载对应数据
        if (tab === 'fans') {
            this.loadFansData();
        }
    },
    methods: {
        // 获取用户信息
        getUserInfo() {
            try {
                const storedUserInfo = uni.getStorageSync('userInfo');
                if (storedUserInfo) {
                    this.userInfo = JSON.parse(storedUserInfo);
                }
            } catch (e) {
                console.error('获取用户信息失败', e);
            }
        },
        
        // 加载关注数据
        async loadConcernData() {
            if (!this.userInfo || !this.userInfo.id) {
                uni.showToast({
                    title: '请先登录',
                    icon: 'none'
                });
                return;
            }
            
            this.loading = true;
            
            try {
                const res = await request.post('/concern/getUserConcerns', {
                    user_id: this.userInfo.id
                });
                
                console.log('获取关注数据成功:', res);
                
                if (res.code === 200 && res.result) {
                    // 处理关注的用户数据，去重（同一个用户可能有多条消息被关注）
                    const uniqueUsers = new Map();
                    const uniqueNames = new Set(); // 用于按名字去重
                    
                    res.result.forEach(item => {
                        const userId = item.target_user_id;
                        const userName = item.nick_name || '未知用户';
                        
                        // 按user_id去重，如果user_id相同则跳过
                        if (uniqueUsers.has(userId)) {
                            return;
                        }
                        
                        // 按用户名去重，如果名字相同也跳过
                        if (uniqueNames.has(userName)) {
                            return;
                        }
                        
                        // 添加到去重集合
                        uniqueUsers.set(userId, {
                            id: userId,
                            name: userName,
                            avatar: this.processAvatarUrl(item.user_image),
                            school: item.user_school || '未知学校',
                            form: item.user_form || '未知类型',
                            fraction: item.user_fraction || '未知分数',
                            sex: item.sex || '未知',
                            birth: item.birth || '未知',
                            phone: item.phone || '未知',
                            concernId: item.concern_id,
                            concernData: item.concern_data,
                            messageId: item.message_id,
                            isFollowed: true
                        });
                        uniqueNames.add(userName);
                    });
                    
                    this.followingList = Array.from(uniqueUsers.values());
                } else {
                    this.followingList = [];
                    console.warn('获取关注数据失败:', res.message);
                }
            } catch (err) {
                console.error('获取关注数据异常:', err);
                uni.showToast({
                    title: '获取关注数据失败',
                    icon: 'none'
                });
            } finally {
                this.loading = false;
            }
        },
        
        // 处理头像URL
        processAvatarUrl(url) {
            if (!url) return '/static/c1.png';
            if (url.startsWith('http')) return url;
            if (url.startsWith('/static')) return url;
            if (!url.startsWith('/')) url = '/' + url;
            return request.baseUrl + url;
        },
        
        // 切换标签
        switchTab(tab) {
            this.currentTab = tab;
            if (tab === 'following') {
                this.loadConcernData();
            } else if (tab === 'fans') {
                this.loadFansData();
            }
        },
        
        // 查看用户资料
        viewUserProfile(user) {
            console.log('查看用户资料:', user.name);
            
            // 传递完整的用户信息
            const userInfo = encodeURIComponent(JSON.stringify({
                id: user.id,
                name: user.name,
                avatar: user.avatar,
                sex: user.sex,
                birth: user.birth,
                phone: user.phone,
                school: user.school,
                form: user.form,
                fraction: user.fraction,
                isFollowed: user.isFollowed
            }));
            
            uni.navigateTo({
                url: `/pages/Me/function/concerns_user?userId=${user.id}&userName=${encodeURIComponent(user.name)}&userInfo=${userInfo}`
            });
        },
        
        // 取消关注用户
        async unfollowUser(index) {
            const user = this.followingList[index];
            
            try {
                uni.showLoading({ title: '取消关注中...' });
                
                const res = await request.post('/concern/removeConcern', {
                    user_id: this.userInfo.id,
                    message_id: user.messageId
                });
                
                uni.hideLoading();
                
                if (res.code === 200) {
                    // 从列表中移除
                    this.followingList.splice(index, 1);
                    this.followingCount = Math.max(0, this.followingCount - 1);
                    
                    uni.showToast({
                        title: '取消关注成功',
                        icon: 'success'
                    });
                } else {
                    uni.showToast({
                        title: res.message || '取消关注失败',
                        icon: 'none'
                    });
                }
            } catch (err) {
                uni.hideLoading();
                console.error('取消关注失败:', err);
                uni.showToast({
                    title: '取消关注失败，请重试',
                    icon: 'none'
                });
            }
        },
        
        // 切换关注状态
        toggleFollow(index) {
            const user = this.fansList[index];
            user.isFollowed = !user.isFollowed;
            
            uni.showToast({
                title: user.isFollowed ? '关注成功' : '已取消关注',
                icon: 'success'
            });
        },
        
        // 处理头像加载错误
        handleAvatarError(index, type) {
            if (type === 'following') {
                this.followingList[index].avatar = '/static/c1.png';
            } else {
                this.fansList[index].avatar = '/static/c1.png';
            }
        },
        formatTime(dateString) {
            if (!dateString) return '';
            const date = new Date(dateString);
            return date.toLocaleDateString();
        },
        // 获取消息图片
        getMessageImages(item) {
            const images = [];
            if (item.message_image) images.push(this.processAvatarUrl(item.message_image));
            if (item.message_imageone) images.push(this.processAvatarUrl(item.message_imageone));
            if (item.message_imagetwo) images.push(this.processAvatarUrl(item.message_imagetwo));
            return images;
        },
        // 截断文本
        truncateText(text, maxLength) {
            if (!text) return '';
            return text.length > maxLength ? text.substring(0, maxLength) + '...' : text;
        },
        // 查看消息详情
        viewMessageDetail(user) {
            uni.navigateTo({
                url: `/pages/chat/chat_detail?messageId=${user.messageId}`
            });
        },
        // 预览图片
        previewImage(images, current) {
            uni.previewImage({
                urls: images,
                current: images[current]
            });
        },
        // 加载粉丝数据
        async loadFansData() {
            if (!this.userInfo || !this.userInfo.id) {
                uni.showToast({
                    title: '请先登录',
                    icon: 'none'
                });
                return;
            }
            
            this.loading = true;
            
            try {
                const res = await request.post('/concern/getUserFans', {
                    user_id: this.userInfo.id
                });
                
                console.log('获取粉丝数据成功:', res);
                
                if (res.code === 200 && res.result) {
                    // 处理粉丝数据，去重
                    const uniqueUsers = new Map();
                    const uniqueNames = new Set();
                    
                    res.result.forEach(item => {
                        const userId = item.fan_user_id;
                        const userName = item.fan_nick_name || '未知用户';
                        
                        // 按user_id去重
                        if (uniqueUsers.has(userId)) {
                            return;
                        }
                        
                        // 按用户名去重
                        if (uniqueNames.has(userName)) {
                            return;
                        }
                        
                        uniqueUsers.set(userId, {
                            id: userId,
                            name: userName,
                            avatar: this.processAvatarUrl(item.fan_user_image),
                            school: item.fan_user_school || '未知学校',
                            form: item.fan_user_form || '未知类型',
                            fraction: item.fan_user_fraction || '未知分数',
                            sex: item.fan_sex || '未知',
                            birth: item.fan_birth || '未知',
                            phone: item.fan_phone || '未知',
                            concernData: item.concern_data,
                            isFollowed: false
                        });
                        uniqueNames.add(userName);
                    });
                    
                    this.fansList = Array.from(uniqueUsers.values());
                } else {
                    this.fansList = [];
                    console.warn('获取粉丝数据失败:', res.message);
                }
            } catch (err) {
                console.error('获取粉丝数据异常:', err);
                uni.showToast({
                    title: '获取粉丝数据失败',
                    icon: 'none'
                });
            } finally {
                this.loading = false;
            }
        },
        // 更新统计数据
        async updateStats() {
            if (!this.userInfo || !this.userInfo.id) return;
            
            try {
                const res = await request.post('/concern/getUserStats', {
                    user_id: this.userInfo.id
                });
                
                if (res.code === 200 && res.data) {
                    // 更新本地显示的统计数据
                    this.followingCount = res.data.following_count;
                    this.fansCount = res.data.fans_count;
                }
            } catch (err) {
                console.error('获取统计数据失败:', err);
            }
        }
    }
}
</script>

<style>
.concern-container {
	background-color: #f8f9fa;
	min-height: 100vh;
}

.stats-header {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	padding: 60rpx 0 40rpx;
	display: flex;
	justify-content: center;
	align-items: center;
	position: relative;
}

.stats-header::after {
	content: '';
	position: absolute;
	bottom: -20rpx;
	left: 50%;
	transform: translateX(-50%);
	width: 60rpx;
	height: 60rpx;
	background: inherit;
	border-radius: 50%;
	opacity: 0.1;
}

.stats-item {
	display: flex;
	flex-direction: column;
	align-items: center;
	color: white;
}

.stats-number {
	font-size: 56rpx;
	font-weight: 600;
	margin-bottom: 12rpx;
	text-shadow: 0 2rpx 8rpx rgba(0,0,0,0.1);
}

.stats-label {
	font-size: 28rpx;
	opacity: 0.9;
	font-weight: 300;
}

.stats-divider {
	width: 2rpx;
	height: 80rpx;
	background: linear-gradient(to bottom, transparent, rgba(255,255,255,0.3), transparent);
	margin: 0 100rpx;
}

.tab-container {
	background-color: #fff;
	display: flex;
	margin: 0 30rpx 20rpx;
	border-radius: 16rpx;
	padding: 8rpx;
	box-shadow: 0 2rpx 20rpx rgba(0,0,0,0.04);
}

.tab-item {
	flex: 1;
	text-align: center;
	padding: 24rpx 0;
	font-size: 30rpx;
	color: #666;
	border-radius: 12rpx;
	transition: all 0.3s ease;
	font-weight: 500;
}

.tab-item.active {
	color: #667eea;
	background: linear-gradient(135deg, rgba(102,126,234,0.1) 0%, rgba(118,75,162,0.1) 100%);
	font-weight: 600;
}

.user-list {
	padding: 0 30rpx 40rpx;
}

.user-item {
	background-color: #fff;
	margin-bottom: 24rpx;
	padding: 32rpx;
	border-radius: 20rpx;
	display: flex;
	align-items: center;
	box-shadow: 0 2rpx 16rpx rgba(0,0,0,0.04);
	transition: all 0.3s ease;
	border: 2rpx solid transparent;
}

.user-item:active {
	transform: translateY(-2rpx);
	box-shadow: 0 8rpx 32rpx rgba(0,0,0,0.08);
	border-color: rgba(102,126,234,0.1);
}

.user-avatar {
	width: 96rpx;
	height: 96rpx;
	border-radius: 50%;
	margin-right: 32rpx;
	border: 4rpx solid #f8f9fa;
	box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.1);
}

.user-info {
	flex: 1;
	min-width: 0;
}

.user-name {
	font-size: 32rpx;
	font-weight: 600;
	color: #2c3e50;
	display: block;
	margin-bottom: 12rpx;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
}

.user-desc {
	font-size: 26rpx;
	color: #7f8c8d;
	display: block;
	margin-bottom: 8rpx;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
}

.user-score {
	font-size: 24rpx;
	color: #95a5a6;
	font-weight: 500;
}

.action-btn {
	padding: 20rpx 32rpx;
	border-radius: 50rpx;
	font-size: 26rpx;
	transition: all 0.3s ease;
	border: none;
	min-width: 120rpx;
	text-align: center;
}

.follow-btn {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	color: white;
	box-shadow: 0 4rpx 16rpx rgba(102,126,234,0.3);
}

.follow-btn:active {
	transform: scale(0.95);
	box-shadow: 0 2rpx 8rpx rgba(102,126,234,0.4);
}

.followed-btn {
	background-color: #ecf0f1;
	color: #7f8c8d;
	border: 2rpx solid #bdc3c7;
}

.unfollow-btn {
	background-color: #f8f9fa;
	color: #6c757d;
	border: 2rpx solid #e9ecef;
}

.unfollow-btn:active {
	background-color: #e9ecef;
	transform: scale(0.95);
}

.btn-text {
	font-weight: 500;
}

.empty-state {
	display: flex;
	flex-direction: column;
	align-items: center;
	padding: 120rpx 60rpx;
	text-align: center;
}

.empty-image {
	width: 240rpx;
	height: 240rpx;
	margin-bottom: 40rpx;
	opacity: 0.6;
}

.empty-text {
	font-size: 36rpx;
	color: #2c3e50;
	margin-bottom: 16rpx;
	font-weight: 600;
}

.empty-desc {
	font-size: 28rpx;
	color: #95a5a6;
	line-height: 1.5;
}

.loading-state {
	display: flex;
	flex-direction: column;
	align-items: center;
	padding: 120rpx 60rpx;
	text-align: center;
}

.loading-text {
	font-size: 28rpx;
	color: #7f8c8d;
	margin-top: 20rpx;
}

/* 加载动画 */
@keyframes fadeIn {
	from {
		opacity: 0;
		transform: translateY(20rpx);
	}
	to {
		opacity: 1;
		transform: translateY(0);
	}
}

.user-item {
	animation: fadeIn 0.3s ease-out;
}

/* 响应式调整 */
@media (max-width: 750rpx) {
	.stats-divider {
		margin: 0 60rpx;
	}
	
	.user-avatar {
		width: 80rpx;
		height: 80rpx;
		margin-right: 24rpx;
	}
	
	.user-name {
		font-size: 30rpx;
	}
	
	.action-btn {
		padding: 16rpx 24rpx;
		font-size: 24rpx;
		min-width: 100rpx;
	}
}
</style>
























<template>
    <view class="analysis-container">
        <!-- 加载状态 -->
        <view v-if="loading" class="loading-container">
            <uni-icons type="spinner-cycle" size="40" color="#007aff"></uni-icons>
            <text class="loading-text">正在分析成绩...</text>
        </view>

        <!-- 错误状态 -->
        <view v-else-if="error" class="error-container">
            <uni-icons type="closeempty" size="60" color="#ff3b30"></uni-icons>
            <text class="error-title">加载失败</text>
            <text class="error-message">{{errorMessage}}</text>
            <button class="retry-btn" @click="retryLoad">重新加载</button>
        </view>

        <!-- 正常内容 -->
        <view v-else>
            <!-- 头部信息 -->
            <view class="header-section">
                <view class="score-overview">
                    <text class="total-score">{{totalScore}}</text>
                    <text class="score-label">总分</text>
                    <view class="rank-info">
                        <text class="rank-text">省内排名: {{provinceRank}}</text>
                        <text class="beat-text">超越了{{beatPercentage}}%的考生</text>
                    </view>
                </view>
            </view>

            <!-- 各科成绩分析 -->
            <view class="subjects-section" v-if="subjects.length > 0">
                <view class="section-title">
                    <uni-icons type="bars" size="18" color="#007aff"></uni-icons>
                    <text class="title-text">各科成绩分析</text>
                </view>
                <view class="subjects-grid">
                    <view class="subject-card" v-for="(subject, index) in subjects" :key="index">
                        <view class="subject-header">
                            <text class="subject-name">{{subject.name}}</text>
                            <text class="subject-score" :class="getScoreLevel(subject.score, subject.fullScore)">
                                {{subject.score}}/{{subject.fullScore}}
                            </text>
                        </view>
                        <view class="progress-bar">
                            <view class="progress-fill" :style="{width: getProgressWidth(subject.score, subject.fullScore), backgroundColor: getProgressColor(subject.score, subject.fullScore)}"></view>
                        </view>
                        <view class="subject-analysis">
                            <text class="analysis-text">{{subject.analysis}}</text>
                            <view class="level-tag" :class="getScoreLevel(subject.score, subject.fullScore)">
                                {{getScoreLevelText(subject.score, subject.fullScore)}}
                            </view>
                        </view>
                    </view>
                </view>
            </view>

            <!-- 优势与劣势分析 -->
            <view class="strength-section">
                <view class="section-title">
                    <uni-icons type="medal" size="18" color="#34c759"></uni-icons>
                    <text class="title-text">优势与劣势分析</text>
                </view>
                <view class="analysis-cards">
                    <view class="analysis-card strength">
                        <view class="card-header">
                            <uni-icons type="checkmarkempty" size="20" color="#34c759"></uni-icons>
                            <text class="card-title">优势科目</text>
                        </view>
                        <view class="subjects-list">
                            <view class="subject-item" v-for="subject in strengthSubjects" :key="subject" v-if="strengthSubjects.length > 0">
                                <text class="subject-text">{{subject}}</text>
                            </view>
                            <text v-if="strengthSubjects.length === 0" class="empty-text">暂无优势科目</text>
                        </view>
                        <text class="suggestion">建议：继续保持优势，可考虑相关专业</text>
                    </view>
                    
                    <view class="analysis-card weakness">
                        <view class="card-header">
                            <uni-icons type="closeempty" size="20" color="#ff3b30"></uni-icons>
                            <text class="card-title">薄弱科目</text>
                        </view>
                        <view class="subjects-list">
                            <view class="subject-item" v-for="subject in weaknessSubjects" :key="subject" v-if="weaknessSubjects.length > 0">
                                <text class="subject-text">{{subject}}</text>
                            </view>
                            <text v-if="weaknessSubjects.length === 0" class="empty-text">无薄弱科目</text>
                        </view>
                        <text class="suggestion">建议：加强练习，避免选择相关专业</text>
                    </view>
                </view>
            </view>

            <!-- 专业推荐 -->
            <view class="recommendation-section" v-if="recommendations.length > 0">
                <view class="section-title">
                    <uni-icons type="heart" size="18" color="#d4237a"></uni-icons>
                    <text class="title-text">专业推荐</text>
                </view>
                <view class="recommendation-list">
                    <view class="recommendation-item" v-for="(item, index) in recommendations" :key="index" @click="viewMajorDetail(item)">
                        <view class="recommendation-header">
                            <text class="major-name">{{item.major}}</text>
                            <view class="match-rate" :class="getMatchClass(item.matchRate)">
                                <text class="rate-text">{{item.matchRate}}%匹配</text>
                            </view>
                        </view>
                        <text class="recommendation-reason">{{item.reason}}</text>
                        <view class="recommendation-tags">
                            <text class="tag" v-for="tag in item.tags" :key="tag">{{tag}}</text>
                        </view>
                    </view>
                </view>
            </view>

            <!-- 提升建议 -->
            <view class="improvement-section" v-if="improvements.length > 0">
                <view class="section-title">
                    <uni-icons type="gear" size="18" color="#5856d6"></uni-icons>
                    <text class="title-text">提升建议</text>
                </view>
                <view class="improvement-list">
                    <view class="improvement-item" v-for="(item, index) in improvements" :key="index">
                        <view class="improvement-icon">
                            <uni-icons :type="item.icon" size="16" :color="item.color"></uni-icons>
                        </view>
                        <view class="improvement-content">
                            <text class="improvement-title">{{item.title}}</text>
                            <text class="improvement-desc">{{item.description}}</text>
                        </view>
                    </view>
                </view>
            </view>
        </view>
    </view>
</template>

<script>
import request from '../../../utils/request.js';

export default {
    data() {
        return {
            loading: true,
            error: false,
            errorMessage: '',
            userId: null,
            totalScore: 0,
            provinceRank: 0,
            beatPercentage: 0,
            subjects: [],
            strengthSubjects: [],
            weaknessSubjects: [],
            yearData: [],
            recommendations: [],
            improvements: []
        }
    },
    onLoad() {
        this.getUserInfo();
        this.fetchScoreAnalysis();
    },
    methods: {
        // 获取用户信息
        getUserInfo() {
            try {
                const storedUserInfo = uni.getStorageSync('userInfo');
                if (storedUserInfo) {
                    const userInfo = JSON.parse(storedUserInfo);
                    this.userId = userInfo.id || userInfo.user_id || userInfo.userId;
                    console.log('获取用户ID:', this.userId);
                } else {
                    console.warn('未找到用户信息');
                    this.showLoginPrompt();
                }
            } catch (e) {
                console.error('获取用户信息失败:', e);
                this.showLoginPrompt();
            }
        },

        // 显示登录提示
        showLoginPrompt() {
            uni.showToast({
                title: '请先登录',
                icon: 'none',
                duration: 2000
            });
            setTimeout(() => {
                uni.navigateTo({
                    url: '/pages/Me/Login'
                });
            }, 2000);
        },

        // 获取成绩分析数据
        async fetchScoreAnalysis() {
            if (!this.userId) {
                console.warn('用户ID为空，无法获取成绩分析');
                this.loading = false;
                this.error = true;
                this.errorMessage = '用户信息缺失';
                return;
            }

            this.loading = true;
            this.error = false;

            try {
                uni.showLoading({
                    title: '分析中...',
                    mask: true
                });

                const response = await request.post('/auth/analyzeScore', {
                    user_id: this.userId
                });

                uni.hideLoading();

                console.log('成绩分析响应:', response);

                if (response.code === 200) {
                    const result = response.result;
                    const scoreAnalysis = result.scoreAnalysis;

                    // 更新数据
                    this.totalScore = scoreAnalysis.totalScore || 0;
                    this.provinceRank = scoreAnalysis.provinceRank || 0;
                    this.beatPercentage = scoreAnalysis.beatPercentage || 0;
                    this.subjects = scoreAnalysis.subjects || [];
                    this.strengthSubjects = scoreAnalysis.strengthSubjects || [];
                    this.weaknessSubjects = scoreAnalysis.weaknessSubjects || [];
                    this.recommendations = scoreAnalysis.recommendations || [];
                    this.improvements = scoreAnalysis.improvements || [];

                    console.log('成绩分析数据更新成功');
                } else {
                    throw new Error(response.message || '获取成绩分析失败');
                }
            } catch (err) {
                uni.hideLoading();
                console.error('获取成绩分析失败:', err);
                
                this.error = true;
                this.errorMessage = err.message || '网络请求失败';
                
                uni.showToast({
                    title: this.errorMessage,
                    icon: 'none',
                    duration: 3000
                });
            } finally {
                this.loading = false;
            }
        },

        // 重新加载数据
        retryLoad() {
            this.fetchScoreAnalysis();
        },

        // 获取成绩等级
        getScoreLevel(score, fullScore) {
            const percentage = (score / fullScore) * 100;
            if (percentage >= 90) return 'excellent';
            if (percentage >= 80) return 'good';
            if (percentage >= 70) return 'average';
            return 'poor';
        },

        // 获取成绩等级文本
        getScoreLevelText(score, fullScore) {
            const percentage = (score / fullScore) * 100;
            if (percentage >= 90) return '优秀';
            if (percentage >= 80) return '良好';
            if (percentage >= 70) return '一般';
            return '待提升';
        },

        // 获取进度条宽度
        getProgressWidth(score, fullScore) {
            return `${(score / fullScore) * 100}%`;
        },

        // 获取进度条颜色
        getProgressColor(score, fullScore) {
            const percentage = (score / fullScore) * 100;
            if (percentage >= 90) return '#34c759';
            if (percentage >= 80) return '#007aff';
            if (percentage >= 70) return '#ff9500';
            return '#ff3b30';
        },

        // 获取用户分数位置
        getUserScorePosition(lineScore) {
            const maxScore = 750;
            const position = (this.totalScore / maxScore) * 100;
            return `${Math.min(position, 5)}%`;
        },

        // 获取分数条宽度
        getBarWidth(lineScore) {
            const maxScore = 750;
            return `${(lineScore / maxScore) * 100}%`;
        },

        // 获取分差样式类
        getGapClass(lineScore) {
            const gap = this.totalScore - lineScore;
            return gap > 0 ? 'positive' : 'negative';
        },

        // 获取分差文本
        getGapText(lineScore) {
            const gap = this.totalScore - lineScore;
            return gap > 0 ? `超出${gap}分` : `差${Math.abs(gap)}分`;
        },

        // 获取匹配度样式类
        getMatchClass(rate) {
            if (rate >= 90) return 'high';
            if (rate >= 70) return 'medium';
            return 'low';
        },

        // 查看专业详情
        viewMajorDetail(major) {
            // 根据专业名称查找专业ID
            this.findSpecializedId(major.major, (specializedId) => {
                if (specializedId) {
                    uni.navigateTo({
                        url: `/pages/Home/function/specialized_detail?id=${specializedId}&name=${encodeURIComponent(major.major)}`
                    });
                } else {
                    uni.showToast({
                        title: '专业信息不存在',
                        icon: 'none'
                    });
                }
            });
        },

        // 根据专业名称查找专业ID
        findSpecializedId(majorName, callback) {
            request.post('/specialized/getAllWithDetail', {})
                .then(res => {
                    if (res.code === 200) {
                        const specializedList = res.result;
                        const found = specializedList.find(item => item.specialized_content === majorName);
                        callback(found ? found.specialized_id : null);
                    } else {
                        callback(null);
                    }
                })
                .catch(err => {
                    console.error('查询专业ID失败:', err);
                    callback(null);
                });
        }
    }
}
</script>

<style scoped>
.analysis-container {
    background: #f5f5f5;
    min-height: 100vh;
    padding: 20rpx;
}

.header-section {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 12rpx;
    padding: 40rpx;
    margin-bottom: 20rpx;
    color: #fff;
    text-align: center;
}

.total-score {
    font-size: 72rpx;
    font-weight: bold;
    display: block;
    margin-bottom: 10rpx;
}

.score-label {
    font-size: 28rpx;
    opacity: 0.9;
    display: block;
    margin-bottom: 20rpx;
}

.rank-info {
    background: rgba(255,255,255,0.2);
    border-radius: 20rpx;
    padding: 15rpx 25rpx;
    display: inline-block;
}

.rank-text {
    font-size: 24rpx;
    display: block;
    margin-bottom: 5rpx;
}

.beat-text {
    font-size: 22rpx;
    opacity: 0.8;
}

.subjects-section, .strength-section, .comparison-section, .recommendation-section, .improvement-section {
    background: #fff;
    border-radius: 12rpx;
    padding: 30rpx;
    margin-bottom: 20rpx;
}

.section-title {
    display: flex;
    align-items: center;
    margin-bottom: 25rpx;
}

.title-text {
    font-size: 32rpx;
    font-weight: bold;
    color: #333;
    margin-left: 10rpx;
}

.subjects-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20rpx;
}

.subject-card {
    background: #f8f9fa;
    border-radius: 8rpx;
    padding: 20rpx;
}

.subject-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15rpx;
}

.subject-name {
    font-size: 28rpx;
    font-weight: bold;
    color: #333;
}

.subject-score {
    font-size: 24rpx;
    font-weight: bold;
}

.subject-score.excellent { color: #34c759; }
.subject-score.good { color: #007aff; }
.subject-score.average { color: #ff9500; }
.subject-score.poor { color: #ff3b30; }

.progress-bar {
    height: 8rpx;
    background: #e9ecef;
    border-radius: 4rpx;
    margin-bottom: 15rpx;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    border-radius: 4rpx;
    transition: width 0.3s ease;
}

.subject-analysis {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.analysis-text {
    font-size: 22rpx;
    color: #666;
    flex: 1;
}

.level-tag {
    font-size: 20rpx;
    padding: 4rpx 8rpx;
    border-radius: 8rpx;
    margin-left: 10rpx;
}

.level-tag.excellent { background: #e8f5e8; color: #34c759; }
.level-tag.good { background: #e3f2fd; color: #007aff; }
.level-tag.average { background: #fff3e0; color: #ff9500; }
.level-tag.poor { background: #ffebee; color: #ff3b30; }

.analysis-cards {
    display: flex;
    gap: 20rpx;
}

.analysis-card {
    flex: 1;
    border-radius: 8rpx;
    padding: 20rpx;
}

.analysis-card.strength {
    background: #e8f5e8;
    border: 1rpx solid #c8e6c9;
}

.analysis-card.weakness {
    background: #ffebee;
    border: 1rpx solid #ffcdd2;
}

.card-header {
    display: flex;
    align-items: center;
    margin-bottom: 15rpx;
}

.card-title {
    font-size: 26rpx;
    font-weight: bold;
    margin-left: 8rpx;
}

.subjects-list {
    margin-bottom: 15rpx;
}

.subject-item {
    display: inline-block;
    background: rgba(255,255,255,0.8);
    padding: 6rpx 12rpx;
    border-radius: 12rpx;
    margin: 5rpx 5rpx 5rpx 0;
}

.subject-text {
    font-size: 22rpx;
    color: #333;
}

.suggestion {
    font-size: 22rpx;
    color: #666;
    font-style: italic;
}

.comparison-chart {
    background: #f8f9fa;
    border-radius: 8rpx;
    padding: 20rpx;
}

.chart-header {
    text-align: center;
    margin-bottom: 20rpx;
}

.chart-title {
    font-size: 26rpx;
    font-weight: bold;
    color: #333;
}

.year-item {
    margin-bottom: 25rpx;
}

.year-info {
    display: flex;
    justify-content: space-between;
    margin-bottom: 10rpx;
}

.year-text {
    font-size: 24rpx;
    color: #333;
}

.line-score {
    font-size: 24rpx;
    font-weight: bold;
    color: #007aff;
}

.comparison-bar {
    position: relative;
    height: 20rpx;
    background: #e9ecef;
    border-radius: 10rpx;
    margin-bottom: 8rpx;
}

.score-bar {
    height: 100%;
    background: #007aff;
    border-radius: 10rpx;
}

.user-score-line {
    position: absolute;
    top: -5rpx;
    height: 30rpx;
    display: flex;
    flex-direction: column;
    align-items: center;
}

.score-dot {
    width: 12rpx;
    height: 12rpx;
    background: #ff3b30;
    border-radius: 50%;
    margin-bottom: 2rpx;
}

.score-text {
    font-size: 18rpx;
    color: #ff3b30;
    white-space: nowrap;
}

.gap-text {
    font-size: 22rpx;
    text-align: right;
}

.gap-text.positive { color: #34c759; }
.gap-text.negative { color: #ff3b30; }

.recommendation-list {
    display: flex;
    flex-direction: column;
    gap: 20rpx;
}

.recommendation-item {
    background: #f8f9fa;
    border-radius: 8rpx;
    padding: 20rpx;
}

.recommendation-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10rpx;
}

.major-name {
    font-size: 28rpx;
    font-weight: bold;
    color: #333;
}

.match-rate {
    padding: 4rpx 12rpx;
    border-radius: 12rpx;
    font-size: 20rpx;
}

.match-rate.high { background: #e8f5e8; color: #34c759; }
.match-rate.medium { background: #fff3e0; color: #ff9500; }
.match-rate.low { background: #ffebee; color: #ff3b30; }

.rate-text {
    font-weight: bold;
}

.recommendation-reason {
    font-size: 24rpx;
    color: #666;
    margin-bottom: 15rpx;
    line-height: 1.5;
}

.recommendation-tags {
    display: flex;
    gap: 10rpx;
}

.tag {
    font-size: 20rpx;
    padding: 4rpx 12rpx;
    border-radius: 12rpx;
    background: #e3f2fd;
    color: #1976d2;
}

.improvement-list {
    display: flex;
    flex-direction: column;
    gap: 20rpx;
}

.improvement-item {
    display: flex;
    align-items: flex-start;
    background: #f8f9fa;
    border-radius: 8rpx;
    padding: 20rpx;
}

.improvement-icon {
    width: 40rpx;
    height: 40rpx;
    background: rgba(255,255,255,0.8);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 15rpx;
    flex-shrink: 0;
}

.improvement-content {
    flex: 1;
}

.improvement-title {
    font-size: 26rpx;
    font-weight: bold;
    color: #333;
    display: block;
    margin-bottom: 8rpx;
}

.improvement-desc {
    font-size: 24rpx;
    color: #666;
    line-height: 1.5;
}

.loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 400rpx;
    padding: 40rpx;
}

.loading-text {
    font-size: 28rpx;
    color: #666;
    margin-top: 20rpx;
}

.error-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 400rpx;
    padding: 40rpx;
}

.error-title {
    font-size: 32rpx;
    font-weight: bold;
    color: #333;
    margin: 20rpx 0 10rpx;
}

.error-message {
    font-size: 26rpx;
    color: #666;
    text-align: center;
    margin-bottom: 30rpx;
}

.retry-btn {
    background: #007aff;
    color: #fff;
    border: none;
    border-radius: 8rpx;
    padding: 15rpx 30rpx;
    font-size: 28rpx;
}

.empty-text {
    font-size: 22rpx;
    color: #999;
    font-style: italic;
}
</style>









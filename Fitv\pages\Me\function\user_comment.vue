<!-- 用户评论 -->

<template>
    <view class="comment-container">
        
        <!-- 页面内容 -->
        <view class="page-content">
            <!-- 加载状态 -->
            <view v-if="loading" class="loading-container">
                <view class="loading-spinner">
                    <uni-icons type="spinner-cycle" size="32" color="#667eea"></uni-icons>
                </view>
                <text class="loading-text">加载中...</text>
            </view>
            
            <!-- 空状态 -->
            <view v-else-if="comments.length === 0" class="empty-container">
                <image src="/static/empty.png" class="empty-image"></image>
                <text class="empty-title">暂无评论</text>
                <text class="empty-desc">您还没有发表过评论</text>
            </view>
            
            <!-- 评论列表 -->
            <view v-else class="comment-list">
                <view v-for="(comment, index) in comments" :key="comment.comment_id" class="comment-card">
                    <!-- 用户信息头部 -->
                    <view class="comment-header">
                        <view class="user-info">
                            <image 
                                :src="getUserAvatar()" 
                                class="user-avatar" 
                                @error="handleAvatarError"
                            ></image>
                            <view class="user-details">
                                <text class="user-name">{{ getUserName() }}</text>
                                <text class="comment-time">{{ formatTime(comment.create_time) }}</text>
                            </view>
                        </view>
                        <view class="comment-status">
                            <view class="status-dot"></view>
                            <text class="status-text">已发布</text>
                        </view>
                    </view>
                    
                    <!-- 评论内容 -->
                    <view class="comment-body">
                        <view class="comment-content">
                            <text class="content-text">{{ comment.comment_content }}</text>
                            <image 
                                v-if="comment.comment_image" 
                                :src="getImageUrl(comment.comment_image)" 
                                class="comment-image" 
                                mode="aspectFill" 
                                @click="previewImage(comment.comment_image)"
                            ></image>
                        </view>
                        
                        <!-- 原消息引用 -->
                        <view class="original-message">
                            <view class="quote-line"></view>
                            <view class="quote-content">
                                <uni-icons type="chat" size="14" color="#999"></uni-icons>
                                <text class="quote-text">回复：{{ comment.message_content || '原消息已删除' }}</text>
                            </view>
                        </view>
                    </view>
                    
                    <!-- 操作按钮 -->
                    <view class="comment-actions">
                        <view class="action-btn edit-btn" @click="editComment(index)">
                            <uni-icons type="compose" size="16" color="#667eea"></uni-icons>
                            <text class="action-text">编辑</text>
                        </view>
                        <view class="action-btn delete-btn" @click="deleteComment(index)">
                            <uni-icons type="trash" size="16" color="#ff4757"></uni-icons>
                            <text class="action-text">删除</text>
                        </view>
                    </view>
                </view>
            </view>
        </view>
        
        <!-- 修改评论弹窗 -->
        <uni-popup ref="editPopup" type="center" :mask-click="false">
            <view class="edit-popup">
                <view class="popup-header">
                    <text class="popup-title">编辑评论</text>
                    <view class="close-btn" @click="cancelEdit">
                        <uni-icons type="close" size="18" color="#999"></uni-icons>
                    </view>
                </view>
                
                <view class="popup-content">
                    <!-- 用户信息 -->
                    <view class="edit-user-info">
                        <image :src="getUserAvatar()" class="edit-avatar"></image>
                        <text class="edit-username">{{ getUserName() }}</text>
                    </view>
                    
                    <!-- 编辑区域 -->
                    <view class="edit-area">
                        <textarea 
                            class="edit-textarea" 
                            v-model="editForm.content" 
                            placeholder="请输入评论内容..."
                            maxlength="500"
                            auto-height
                        ></textarea>
                        <view class="char-count">{{ editForm.content.length }}/500</view>
                    </view>
                    
                    <!-- 图片上传区域 -->
                    <view class="image-upload-section">
                        <view class="upload-label">
                            <uni-icons type="image" size="16" color="#667eea"></uni-icons>
                            <text>添加图片（可选）</text>
                        </view>
                        <view class="image-container">
                            <view v-if="editForm.image" class="image-preview">
                                <image :src="editForm.image" class="preview-image" mode="aspectFill"></image>
                                <view class="remove-image" @click="removeEditImage">
                                    <uni-icons type="close" size="14" color="#fff"></uni-icons>
                                </view>
                            </view>
                            <view v-else class="upload-placeholder" @click="chooseEditImage">
                                <uni-icons type="camera" size="28" color="#ccc"></uni-icons>
                                <text class="upload-text">点击添加图片</text>
                            </view>
                        </view>
                    </view>
                </view>
                
                <view class="popup-actions">
                    <button class="cancel-btn" @click="cancelEdit">取消</button>
                    <button class="confirm-btn" @click="confirmEdit" :disabled="editLoading">
                        <uni-icons v-if="editLoading" type="spinner-cycle" size="16" color="#fff"></uni-icons>
                        <text>{{ editLoading ? '保存中...' : '保存' }}</text>
                    </button>
                </view>
            </view>
        </uni-popup>
    </view>
</template>
<script>
import request from '../../../utils/request.js';

export default {
    data() {
        return {
            loading: true,
            error: false,
            errorMessage: '',
            userId: null,
            comments: [],
            editForm: {
                commentId: null,
                content: '',
                index: -1,
                image: null
            },
            editLoading: false
        }
    },
    
    onLoad() {
        this.init();
    },
    
    onShow() {
        // 页面显示时刷新数据
        if (this.userId) {
            this.loadUserComments();
        }
    },
    
    methods: {
        // 初始化
        init() {
            this.getUserInfo();
            this.loadUserComments();
        },
        
        // 获取用户信息
        getUserInfo() {
            try {
                const storedUserInfo = uni.getStorageSync('userInfo');
                if (storedUserInfo) {
                    const userInfo = JSON.parse(storedUserInfo);
                    this.userId = userInfo.id || userInfo.user_id || userInfo.userId;
                    console.log('获取用户ID:', this.userId);
                } else {
                    this.handleUserNotFound();
                }
            } catch (e) {
                console.error('获取用户信息失败:', e);
                this.handleUserNotFound();
            }
        },
        
        // 处理用户未找到
        handleUserNotFound() {
            this.setErrorState('用户信息缺失，请重新登录');
            setTimeout(() => {
                uni.navigateTo({
                    url: '/pages/Me/Login'
                });
            }, 2000);
        },
        
        // 加载用户评论
        async loadUserComments() {
            if (!this.userId) {
                this.setErrorState('用户信息缺失');
                return;
            }
            
            this.setLoadingState(true);
            
            try {
                // 先获取所有消息列表
                const messagesRes = await request.post('/message/', {});
                
                if (messagesRes.code === 200 && messagesRes.result) {
                    // 获取当前用户发布的消息ID列表
                    const userMessages = messagesRes.result.filter(msg => msg.user_id == this.userId);
                    
                    // 为每个消息获取评论
                    const allComments = [];
                    for (const message of userMessages) {
                        try {
                            const commentsRes = await request.post('/message/comments', {
                                message_id: message.message_id
                            });
                            
                            if (commentsRes.code === 200 && commentsRes.result) {
                                // 筛选出当前用户的评论
                                const userComments = commentsRes.result
                                    .filter(comment => comment.user_id == this.userId)
                                    .map(comment => ({
                                        comment_id: comment.comment_id,
                                        comment_content: comment.comment_content,
                                        comment_image: comment.comment_image,
                                        create_time: comment.create_time,
                                        message_id: comment.message_id,
                                        message_content: message.message_content, // 原消息内容
                                        user_id: comment.user_id
                                    }));
                                
                                allComments.push(...userComments);
                            }
                        } catch (err) {
                            console.error(`获取消息${message.message_id}的评论失败:`, err);
                        }
                    }
                    
                    // 同时获取用户对其他人消息的评论
                    const allMessagesRes = await request.post('/message/', {});
                    if (allMessagesRes.code === 200 && allMessagesRes.result) {
                        for (const message of allMessagesRes.result) {
                            try {
                                const commentsRes = await request.post('/message/comments', {
                                    message_id: message.message_id
                                });
                                
                                if (commentsRes.code === 200 && commentsRes.result) {
                                    // 筛选出当前用户的评论（排除已经添加的）
                                    const userComments = commentsRes.result
                                        .filter(comment => 
                                            comment.user_id == this.userId && 
                                            !allComments.some(existing => existing.comment_id === comment.comment_id)
                                        )
                                        .map(comment => ({
                                            comment_id: comment.comment_id,
                                            comment_content: comment.comment_content,
                                            comment_image: comment.comment_image,
                                            create_time: comment.create_time,
                                            message_id: comment.message_id,
                                            message_content: message.message_content, // 原消息内容
                                            user_id: comment.user_id
                                        }));
                                    
                                    allComments.push(...userComments);
                                }
                            } catch (err) {
                                console.error(`获取消息${message.message_id}的评论失败:`, err);
                            }
                        }
                    }
                    
                    // 按时间倒序排列
                    this.comments = allComments.sort((a, b) => new Date(b.create_time) - new Date(a.create_time));
                    this.error = false;
                    
                    console.log('获取用户评论成功:', this.comments);
                } else {
                    throw new Error('获取消息列表失败');
                }
            } catch (err) {
                console.error('获取用户评论失败:', err);
                this.setErrorState(err.message || '网络请求失败');
            } finally {
                this.setLoadingState(false);
            }
        },
        
        // 设置加载状态
        setLoadingState(loading) {
            this.loading = loading;
            if (loading) {
                this.error = false;
            }
        },
        
        // 设置错误状态
        setErrorState(message) {
            this.loading = false;
            this.error = true;
            this.errorMessage = message;
        },
        
        // 删除评论
        deleteComment(index) {
            const comment = this.comments[index];
            
            uni.showModal({
                title: '确认删除',
                content: '确定要删除这条评论吗？删除后无法恢复。',
                confirmText: '删除',
                cancelText: '取消',
                confirmColor: '#ff3b30',
                success: (res) => {
                    if (res.confirm) {
                        this.performDeleteComment(comment.comment_id, index);
                    }
                }
            });
        },
        
        // 执行删除操作
        async performDeleteComment(commentId, index) {
            uni.showLoading({ title: '删除中...' });
            
            try {
                const res = await request.post('/message/delete', {
                    message_id: commentId
                });
                
                if (res.code === 200) {
                    // 从列表中移除该评论
                    this.comments.splice(index, 1);
                    
                    uni.showToast({
                        title: '删除成功',
                        icon: 'success'
                    });
                } else {
                    throw new Error(res.message || '删除失败');
                }
            } catch (err) {
                console.error('删除评论失败:', err);
                uni.showToast({
                    title: err.message || '删除失败，请重试',
                    icon: 'none'
                });
            } finally {
                uni.hideLoading();
            }
        },
        
        // 修改评论
        editComment(index) {
            const comment = this.comments[index];
            this.editForm = {
                commentId: comment.comment_id,
                content: comment.comment_content,
                image: comment.comment_image ? this.getImageUrl(comment.comment_image) : '',
                imageFile: null,
                index: index
            };
            this.$refs.editPopup.open();
        },
        
        // 取消修改
        cancelEdit() {
            this.editForm = {
                commentId: '',
                content: '',
                image: '',
                imageFile: null,
                index: -1
            };
            this.$refs.editPopup.close();
        },
        
        // 确认修改
        async confirmEdit() {
            if (!this.editForm.content.trim()) {
                uni.showToast({
                    title: '评论内容不能为空',
                    icon: 'none'
                });
                return;
            }
            
            this.editLoading = true;
            
            try {
                // 如果有新图片，使用uni.uploadFile上传
                if (this.editForm.imageFile) {
                    uni.uploadFile({
                        url: request.baseUrl + '/message/update',
                        filePath: this.editForm.image,
                        name: 'comment_image',
                        formData: {
                            message_id: this.editForm.commentId,
                            message_content: this.editForm.content.trim()
                        },
                        success: (uploadRes) => {
                            try {
                                const res = JSON.parse(uploadRes.data);
                                if (res.code === 200) {
                                    // 更新本地数据
                                    this.comments[this.editForm.index].comment_content = this.editForm.content.trim();
                                    this.comments[this.editForm.index].comment_image = res.result?.imageUrl || this.editForm.image;
                                    
                                    uni.showToast({
                                        title: '修改成功',
                                        icon: 'success'
                                    });
                                    
                                    this.cancelEdit();
                                } else {
                                    throw new Error(res.message || '修改失败');
                                }
                            } catch (parseErr) {
                                console.error('解析响应失败:', parseErr);
                                uni.showToast({
                                    title: '修改失败，请重试',
                                    icon: 'none'
                                });
                            } finally {
                                this.editLoading = false;
                            }
                        },
                        fail: (err) => {
                            console.error('上传失败:', err);
                            uni.showToast({
                                title: '上传失败，请重试',
                                icon: 'none'
                            });
                            this.editLoading = false;
                        }
                    });
                } else {
                    // 没有新图片，只更新文本内容
                    const res = await request.post('/message/update', {
                        message_id: this.editForm.commentId,
                        message_content: this.editForm.content.trim()
                    });
                    
                    if (res.code === 200) {
                        // 更新本地数据
                        this.comments[this.editForm.index].comment_content = this.editForm.content.trim();
                        
                        uni.showToast({
                            title: '修改成功',
                            icon: 'success'
                        });
                        
                        this.cancelEdit();
                    } else {
                        throw new Error(res.message || '修改失败');
                    }
                    this.editLoading = false;
                }
            } catch (err) {
                console.error('修改评论失败:', err);
                uni.showToast({
                    title: err.message || '修改失败，请重试',
                    icon: 'none'
                });
                this.editLoading = false;
            }
        },
        
        // 预览图片
        previewImage(imageUrl) {
            uni.previewImage({
                urls: [this.getImageUrl(imageUrl)],
                current: this.getImageUrl(imageUrl)
            });
        },
        
        // 获取图片URL
        getImageUrl(imagePath) {
            if (!imagePath) return '';
            if (imagePath.startsWith('http')) return imagePath;
            return request.baseUrl + imagePath;
        },
        
        // 格式化时间
        formatTime(timeString) {
            if (!timeString) return '';
            const date = new Date(timeString);
            const now = new Date();
            const diff = now - date;
            
            if (diff < 60000) return '刚刚';
            if (diff < 3600000) return Math.floor(diff / 60000) + '分钟前';
            if (diff < 86400000) return Math.floor(diff / 3600000) + '小时前';
            if (diff < 604800000) return Math.floor(diff / 86400000) + '天前';
            
            return date.toLocaleDateString();
        },
        
        // 返回上一页
        goBack() {
            uni.navigateBack();
        },
        
        // 选择图片
        chooseEditImage() {
            uni.chooseImage({
                count: 1,
                sizeType: ['compressed'],
                sourceType: ['album', 'camera'],
                success: (res) => {
                    this.editForm.image = res.tempFilePaths[0];
                    this.editForm.imageFile = res.tempFiles[0];
                }
            });
        },
        
        // 移除图片
        removeEditImage() {
            this.editForm.image = '';
            this.editForm.imageFile = null;
        },
        
        // 获取用户头像
        getUserAvatar() {
            try {
                const userInfo = uni.getStorageSync('userInfo');
                if (userInfo) {
                    const user = JSON.parse(userInfo);
                    let avatar = user.avatar || user.user_image || '/static/c1.png';
                    
                    if (avatar && !avatar.startsWith('http') && !avatar.startsWith('/static')) {
                        if (!avatar.startsWith('/')) {
                            avatar = '/' + avatar;
                        }
                        avatar = request.baseUrl + avatar;
                    }
                    return avatar;
                }
            } catch (err) {
                console.error('获取用户头像失败:', err);
            }
            return '/static/c1.png';
        },
        
        // 获取用户名
        getUserName() {
            try {
                const userInfo = uni.getStorageSync('userInfo');
                if (userInfo) {
                    const user = JSON.parse(userInfo);
                    return user.nick_name || user.name || user.username || '用户';
                }
            } catch (err) {
                console.error('获取用户名失败:', err);
            }
            return '用户';
        },
        
        // 处理头像加载错误
        handleAvatarError(e) {
            e.target.src = '/static/c1.png';
        }
    }
}
</script>
<style>
/* 页面容器 */
.comment-container {
    min-height: 100vh;
    background: linear-gradient(180deg, #f8f9fa 0%, #ffffff 100%);
}


/* 页面内容 */
.page-content {
    padding: 32rpx 24rpx;
    padding-bottom: 120rpx;
}

/* 加载状态 */
.loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 120rpx 0;
}

.loading-spinner {
    width: 80rpx;
    height: 80rpx;
    border-radius: 50%;
    background: rgba(102, 126, 234, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 24rpx;
}

.loading-text {
    color: #667eea;
    font-size: 28rpx;
}

/* 空状态 */
.empty-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 120rpx 0;
}

.empty-image {
    width: 200rpx;
    height: 200rpx;
    opacity: 0.6;
    margin-bottom: 32rpx;
}

.empty-title {
    font-size: 32rpx;
    color: #2c3e50;
    font-weight: 600;
    margin-bottom: 16rpx;
}

.empty-desc {
    font-size: 26rpx;
    color: #7f8c8d;
}

/* 评论列表 */
.comment-list {
    display: flex;
    flex-direction: column;
    gap: 24rpx;
}

/* 评论卡片 */
.comment-card {
    background: #fff;
    border-radius: 24rpx;
    padding: 32rpx;
    box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);
    border: 2rpx solid rgba(102, 126, 234, 0.05);
    transition: all 0.3s ease;
}

.comment-card:hover {
    transform: translateY(-4rpx);
    box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.12);
    border-color: rgba(102, 126, 234, 0.15);
}

/* 评论头部 */
.comment-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24rpx;
}

.user-info {
    display: flex;
    align-items: center;
    flex: 1;
}

.user-avatar {
    width: 72rpx;
    height: 72rpx;
    border-radius: 50%;
    margin-right: 24rpx;
    border: 3rpx solid rgba(102, 126, 234, 0.1);
    box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.user-details {
    flex: 1;
}

.user-name {
    display: block;
    font-size: 30rpx;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 8rpx;
}

.comment-time {
    font-size: 24rpx;
    color: #95a5a6;
}

.comment-status {
    display: flex;
    align-items: center;
    padding: 8rpx 16rpx;
    background: rgba(46, 204, 113, 0.1);
    border-radius: 20rpx;
}

.status-dot {
    width: 12rpx;
    height: 12rpx;
    border-radius: 50%;
    background: #2ecc71;
    margin-right: 8rpx;
}

.status-text {
    font-size: 22rpx;
    color: #2ecc71;
    font-weight: 500;
}

/* 评论主体 */
.comment-body {
    margin-bottom: 24rpx;
}

.comment-content {
    margin-bottom: 20rpx;
}

.content-text {
    font-size: 28rpx;
    color: #2c3e50;
    line-height: 1.6;
    display: block;
    margin-bottom: 16rpx;
}

.comment-image {
    width: 100%;
    max-width: 400rpx;
    height: 300rpx;
    border-radius: 16rpx;
    border: 2rpx solid rgba(102, 126, 234, 0.1);
}

/* 原消息引用 */
.original-message {
    background: rgba(102, 126, 234, 0.05);
    border-radius: 16rpx;
    padding: 20rpx;
    border-left: 6rpx solid #667eea;
    position: relative;
}

.quote-line {
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 6rpx;
    background: linear-gradient(180deg, #667eea 0%, #764ba2 100%);
    border-radius: 3rpx;
}

.quote-content {
    display: flex;
    align-items: flex-start;
    gap: 12rpx;
}

.quote-text {
    font-size: 26rpx;
    color: #7f8c8d;
    line-height: 1.5;
    flex: 1;
}

/* 操作按钮 */
.comment-actions {
    display: flex;
    gap: 16rpx;
    justify-content: flex-end;
}

.action-btn {
    display: flex;
    align-items: center;
    gap: 8rpx;
    padding: 16rpx 24rpx;
    border-radius: 20rpx;
    font-size: 26rpx;
    transition: all 0.3s ease;
    border: 2rpx solid transparent;
}

.edit-btn {
    background: rgba(102, 126, 234, 0.1);
    color: #667eea;
    border-color: rgba(102, 126, 234, 0.2);
}

.edit-btn:active {
    background: rgba(102, 126, 234, 0.2);
    transform: scale(0.95);
}

.delete-btn {
    background: rgba(255, 71, 87, 0.1);
    color: #ff4757;
    border-color: rgba(255, 71, 87, 0.2);
}

.delete-btn:active {
    background: rgba(255, 71, 87, 0.2);
    transform: scale(0.95);
}

.action-text {
    font-size: 26rpx;
    font-weight: 500;
}

/* 编辑弹窗 */
.edit-popup {
    width: 680rpx;
    background: #fff;
    border-radius: 24rpx;
    overflow: hidden;
    box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.15);
}

.popup-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    padding: 32rpx;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.popup-title {
    color: #fff;
    font-size: 32rpx;
    font-weight: 600;
}

.close-btn {
    width: 56rpx;
    height: 56rpx;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.2);
    display: flex;
    align-items: center;
    justify-content: center;
}

.popup-content {
    padding: 32rpx;
}

/* 编辑用户信息 */
.edit-user-info {
    display: flex;
    align-items: center;
    margin-bottom: 24rpx;
    padding: 20rpx;
    background: rgba(102, 126, 234, 0.05);
    border-radius: 16rpx;
}

.edit-avatar {
    width: 56rpx;
    height: 56rpx;
    border-radius: 50%;
    margin-right: 16rpx;
    border: 2rpx solid rgba(102, 126, 234, 0.2);
}

.edit-username {
    font-size: 28rpx;
    color: #2c3e50;
    font-weight: 600;
}

/* 编辑区域 */
.edit-area {
    margin-bottom: 24rpx;
}

.edit-textarea {
    width: 100%;
    min-height: 200rpx;
    padding: 20rpx;
    border: 2rpx solid rgba(102, 126, 234, 0.1);
    border-radius: 16rpx;
    font-size: 28rpx;
    color: #2c3e50;
    line-height: 1.6;
    background: rgba(102, 126, 234, 0.02);
    transition: all 0.3s ease;
}

.edit-textarea:focus {
    border-color: #667eea;
    background: #fff;
    box-shadow: 0 0 0 6rpx rgba(102, 126, 234, 0.1);
}

.char-count {
    text-align: right;
    font-size: 24rpx;
    color: #95a5a6;
    margin-top: 12rpx;
}

/* 图片上传 */
.image-upload-section {
    margin-bottom: 32rpx;
}

.upload-label {
    display: flex;
    align-items: center;
    gap: 8rpx;
    font-size: 26rpx;
    color: #667eea;
    font-weight: 500;
    margin-bottom: 16rpx;
}

.image-container {
    position: relative;
}

.image-preview {
    position: relative;
    display: inline-block;
}

.preview-image {
    width: 200rpx;
    height: 200rpx;
    border-radius: 16rpx;
    border: 2rpx solid rgba(102, 126, 234, 0.1);
}

.remove-image {
    position: absolute;
    top: -12rpx;
    right: -12rpx;
    width: 48rpx;
    height: 48rpx;
    border-radius: 50%;
    background: #ff4757;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 4rpx 12rpx rgba(255, 71, 87, 0.3);
}

.upload-placeholder {
    width: 200rpx;
    height: 200rpx;
    border: 3rpx dashed rgba(102, 126, 234, 0.3);
    border-radius: 16rpx;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background: rgba(102, 126, 234, 0.02);
    transition: all 0.3s ease;
}

.upload-placeholder:active {
    border-color: #667eea;
    background: rgba(102, 126, 234, 0.1);
}

.upload-text {
    font-size: 24rpx;
    color: #95a5a6;
    margin-top: 12rpx;
}

/* 弹窗操作按钮 */
.popup-actions {
    display: flex;
    gap: 16rpx;
    padding: 24rpx 32rpx;
    background: rgba(102, 126, 234, 0.05);
}

.cancel-btn {
    flex: 1;
    height: 80rpx;
    border: 2rpx solid rgba(102, 126, 234, 0.3);
    border-radius: 20rpx;
    background: #fff;
    color: #667eea;
    font-size: 28rpx;
    font-weight: 500;
}

.confirm-btn {
    flex: 1;
    height: 80rpx;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    border-radius: 20rpx;
    color: #fff;
    font-size: 28rpx;
    font-weight: 600;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8rpx;
}

.confirm-btn:disabled {
    opacity: 0.6;
}

/* 响应式适配 */
@media (max-width: 750rpx) {
    .comment-card {
        margin: 0 16rpx;
        padding: 24rpx;
    }
    
    .edit-popup {
        width: 90vw;
        margin: 0 5vw;
    }
}

/* 动画效果 */
@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(30rpx);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.comment-card {
    animation: slideInUp 0.3s ease-out;
}

.comment-card:nth-child(n) {
    animation-delay: calc(var(--index, 0) * 0.1s);
}
</style>















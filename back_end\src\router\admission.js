//学校录取学生接口

const express = require('express');
const router = express.Router();
const db = require('../config/db');
const multer = require('multer');
const path = require('path');

// 获取所有录取信息
router.post('/', (req, res) => {
    const sql = `
        SELECT 
            a.*,
            u.username,
            u.password,
            u.nick_name,
            u.sex,
            u.birth,
            u.phone,
            u.identity,
            u.user_image,
            u.user_form,
            u.user_fraction,
            s.school_account,
            s.school_password,
            s.school_name,
            s.school_image,
            s.school_phone,
            s.school_address,
            s.school_characteristic,
            s.school_idea,
            s.school_email,
            s.identity as school_identity,
            sp.specialized_id,
            sp.specialized_content,
            sc.min_scores
        FROM admission a
        LEFT JOIN user u ON a.user_id = u.user_id
        LEFT JOIN school s ON a.school_id = s.school_id
        LEFT JOIN specialized sp ON a.specialized_id = sp.specialized_id
        LEFT JOIN school_scores sc ON (a.school_id = sc.school_id AND a.specialized_id = sc.specialized_id)
    `;
    
    db.query(sql, (err, result) => {
        if (err) {
            res.status(500).send({
                code: 201,
                success: "失败",
                message: "查询失败",
                error: err.message
            });
        } else {
            res.send({
                code: 200,
                success: "成功",
                message: "查询成功",
                result: result
            });
        }
    });
});

// 添加录取信息
router.post('/add', (req, res) => {
    const { user_id, school_id, specialized_id, admission_state } = req.body;

    if (!user_id || !school_id) {
        return res.status(400).send({
            code: 201,
            success: "失败",
            message: "用户ID和学校ID不能为空"
        });
    }

    // 设置默认录取状态为"未录取"，如果提供了状态则使用提供的状态
    const state = admission_state || "未录取";
    
    // 验证admission_state的值是否有效
    if (state !== "未录取" && state !== "已录取") {
        return res.status(400).send({
            code: 201,
            success: "失败",
            message: "录取状态只能是'已录取'或'未录取'"
        });
    }

    const sql = 'INSERT INTO admission (user_id, school_id, specialized_id, admission_state) VALUES (?, ?, ?, ?)';
    db.query(sql, [user_id, school_id, specialized_id || null, state], (err, result) => {
        if (err) {
            res.status(500).send({
                code: 201,
                success: "失败",
                message: "添加失败",
                error: err.message
            });
        } else {
            res.send({
                code: 200,
                success: "成功",
                message: "添加成功",
                data: {
                    admission_id: result.insertId,
                    user_id,
                    school_id,
                    specialized_id,
                    admission_state: state
                }
            });
        }
    });
});

// 修改录取信息
router.post('/update', (req, res) => {
    const { admission_id, user_id, school_id, specialized_id, admission_state } = req.body;

    if (!admission_id) {
        return res.status(400).send({
            code: 201,
            success: "失败",
            message: "录取ID不能为空"
        });
    }

    const updateFields = [];
    const values = [];

    if (user_id !== undefined) {
        updateFields.push('user_id = ?');
        values.push(user_id);
    }

    if (school_id !== undefined) {
        updateFields.push('school_id = ?');
        values.push(school_id);
    }

    if (specialized_id !== undefined) {
        updateFields.push('specialized_id = ?');
        values.push(specialized_id);
    }
    
    if (admission_state !== undefined) {
        // 验证admission_state的值是否有效
        if (admission_state !== "未录取" && admission_state !== "已录取") {
            return res.status(400).send({
                code: 201,
                success: "失败",
                message: "录取状态只能是'已录取'或'未录取'"
            });
        }
        updateFields.push('admission_state = ?');
        values.push(admission_state);
    }

    if (updateFields.length === 0) {
        return res.status(400).send({
            code: 201,
            success: "失败",
            message: "没有要更新的字段"
        });
    }

    values.push(admission_id);
    const sql = `UPDATE admission SET ${updateFields.join(', ')} WHERE admission_id = ?`;

    db.query(sql, values, (err, result) => {
        if (err) {
            res.status(500).send({
                code: 201,
                success: "失败",
                message: "更新失败",
                error: err.message
            });
        } else if (result.affectedRows === 0) {
            res.status(404).send({
                code: 201,
                success: "失败",
                message: "未找到该录取信息"
            });
        } else {
            res.send({
                code: 200,
                success: "成功",
                message: "更新成功",
                data: {
                    admission_id,
                    ...(user_id !== undefined && { user_id }),
                    ...(school_id !== undefined && { school_id }),
                    ...(specialized_id !== undefined && { specialized_id }),
                    ...(admission_state !== undefined && { admission_state })
                }
            });
        }
    });
});

// 删除录取信息
router.post('/delete', (req, res) => {
    const { admission_id } = req.body;

    if (!admission_id) {
        return res.status(400).send({
            code: 201,
            success: "失败",
            message: "录取ID不能为空"
        });
    }

    const sql = 'DELETE FROM admission WHERE admission_id = ?';
    db.query(sql, [admission_id], (err, result) => {
        if (err) {
            res.status(500).send({
                code: 201,
                success: "失败",
                message: "删除失败",
                error: err.message
            });
        } else if (result.affectedRows === 0) {
            res.status(404).send({
                code: 201,
                success: "失败",
                message: "未找到该录取信息"
            });
        } else {
            res.send({
                code: 200,
                success: "成功",
                message: "删除成功"
            });
        }
    });
});

// 用户选择学校后保存录取信息
router.post('/selectSchool', (req, res) => {
    const { school_id, user_id, specialized_id } = req.body;

    if (!school_id || !user_id) {
        return res.status(400).send({
            code: 201,
            success: "失败",
            message: "学校ID和用户ID不能为空"
        });
    }

    // 先检查是否已经选择过这个学校和专业
    const checkSql = 'SELECT * FROM admission WHERE user_id = ? AND school_id = ? AND specialized_id = ?';
    db.query(checkSql, [user_id, school_id, specialized_id || null], (err, result) => {
        if (err) {
            return res.status(500).send({
                code: 201,
                success: "失败",
                message: "查询失败",
                error: err.message
            });
        }

        if (result.length > 0) {
            return res.status(400).send({
                code: 201,
                success: "失败",
                message: "您已经选择过这个学校和专业了"
            });
        }

        // 插入新的录取信息，默认状态为"未录取"
        const insertSql = 'INSERT INTO admission (user_id, school_id, specialized_id, admission_state) VALUES (?, ?, ?, "未录取")';
        db.query(insertSql, [user_id, school_id, specialized_id || null], (err, result) => {
            if (err) {
                res.status(500).send({
                    code: 201,
                    success: "失败",
                    message: "保存失败",
                    error: err.message
                });
            } else {
                res.send({
                    code: 200,
                    success: "成功",
                    message: "选择学校和专业成功",
                    data: {
                        admission_id: result.insertId,
                        user_id,
                        school_id,
                        specialized_id,
                        admission_state: "未录取"
                    }
                });
            }
        });
    });
});

// 更新录取状态
router.post('/update-state', (req, res) => {
    const admission_id = parseInt(req.body.admission_id); // 确保admission_id是整数
    const admission_state = req.body.admission_state.toString(); // 确保admission_state是字符串

    if (!admission_id || !admission_state) {
        return res.status(400).send({
            code: 201,
            success: "失败",
            message: "录取ID和状态不能为空"
        });
    }

    // 验证admission_state的值是否有效
    if (admission_state !== "未录取" && admission_state !== "已录取") {
        return res.status(400).send({
            code: 201,
            success: "失败",
            message: "录取状态只能是'已录取'或'未录取'"
        });
    }

    // 如果要设置为已录取状态，先检查该用户是否已有其他录取记录
    if (admission_state === "已录取") {
        const checkSql = `
            SELECT a2.* 
            FROM admission a1 
            JOIN admission a2 ON a1.user_id = a2.user_id 
            WHERE a1.admission_id = ? AND a2.admission_state = "已录取"
        `;
        
        db.query(checkSql, [admission_id], (err, result) => {
            if (err) {
                return res.status(500).send({
                    code: 201,
                    success: "失败",
                    message: "查询失败",
                    error: err.message
                });
            }

            if (result.length > 0) {
                return res.status(400).send({
                    code: 201,
                    success: "失败",
                    message: "该用户已有其他录取记录"
                });
            }

            // 更新状态
            updateAdmissionState(admission_id, admission_state, res);
        });
    } else {
        // 如果不是设置录取状态，直接更新
        updateAdmissionState(admission_id, admission_state, res);
    }
});

// 更新录取状态的辅助函数
function updateAdmissionState(admission_id, admission_state, res) {
    const updateSql = 'UPDATE admission SET admission_state = ? WHERE admission_id = ?';
    db.query(updateSql, [admission_state, admission_id], (err, result) => {
        if (err) {
            res.status(500).send({
                code: 201,
                success: "失败",
                message: "更新失败",
                error: err.message
            });
        } else if (result.affectedRows === 0) {
            res.status(404).send({
                code: 201,
                success: "失败",
                message: "未找到该录取记录"
            });
        } else {
            res.send({
                code: 200,
                success: "成功",
                message: "更新状态成功",
                data: {
                    admission_id,
                    admission_state
                }
            });
        }
    });
}

// 获取用户的所有申请记录（包括录取状态）
router.post('/getUserAdmissions', (req, res) => {
    const userId = req.body.user_id || 1;
    
    const sql = `
        SELECT 
            a.*,
            s.school_name,
            s.school_image,
            s.school_phone,
            s.school_address,
            s.school_characteristic,
            s.school_idea,
            s.school_email,
            u.username,
            u.nick_name,
            u.sex,
            u.birth,
            u.phone,
            u.user_image,
            u.user_form,
            u.user_fraction,
            sp.specialized_content,
            sd.specialized_part,
            sc.min_scores
        FROM admission a
        LEFT JOIN school s ON a.school_id = s.school_id
        LEFT JOIN user u ON a.user_id = u.user_id
        LEFT JOIN specialized sp ON a.specialized_id = sp.specialized_id
        LEFT JOIN specialized_detail sd ON sp.specialized_id = sd.specialized_id
        LEFT JOIN school_scores sc ON (a.school_id = sc.school_id AND a.specialized_id = sc.specialized_id)
        WHERE a.user_id = ?
        ORDER BY a.admission_state DESC
    `;

    db.query(sql, [userId], (err, result) => {
        if (err) {
            res.status(500).send({
                code: 201,
                success: "失败",
                message: "查询失败",
                error: err.message
            });
        } else {
            res.send({
                code: 200,
                success: "成功",
                message: "查询成功",
                result: result
            });
        }
    });
});

// 获取学校的所有申请记录（包括录取状态）
router.post('/getSchoolAdmissions', (req, res) => {
    const schoolId = req.body.school_id;
    
    if (!schoolId) {
        return res.status(400).send({
            code: 201,
            success: "失败",
            message: "请提供学校ID"
        });
    }

    const sql = `
        SELECT 
            a.*,
            u.username,
            u.nick_name,
            u.sex,
            u.birth,
            u.phone,
            u.user_image,
            u.user_form,
            u.user_fraction,
            s.school_name,
            s.school_image,
            sp.specialized_content
        FROM admission a
        LEFT JOIN user u ON a.user_id = u.user_id
        LEFT JOIN school s ON a.school_id = s.school_id
        LEFT JOIN specialized sp ON a.specialized_id = sp.specialized_id
        WHERE a.school_id = ?
        ORDER BY a.admission_state DESC
    `;

    db.query(sql, [schoolId], (err, result) => {
        if (err) {
            res.status(500).send({
                code: 201,
                success: "失败",
                message: "查询失败",
                error: err.message
            });
        } else {
            res.send({
                code: 200,
                success: "成功",
                message: "查询成功",
                result: result
            });
        }
    });
});

// 获取所有学校信息，用于志愿填报
router.get('/getAllSchools', (req, res) => {
    const sql = `
        SELECT 
            school_id, 
            school_name, 
            school_image, 
            school_address, 
            school_characteristic,
            school_idea
        FROM school
        ORDER BY school_name
    `;
    
    db.query(sql, (err, result) => {
        if (err) {
            res.status(500).send({
                code: 201,
                success: "失败",
                message: "查询学校信息失败",
                error: err.message
            });
        } else {
            res.send({
                code: 200,
                success: "成功",
                message: "查询学校信息成功",
                result: result
            });
        }
    });
});

// 根据学校ID获取专业信息，用于志愿填报
router.get('/getSpecializedBySchool/:schoolId', (req, res) => {
    const schoolId = req.params.schoolId;
    
    if (!schoolId) {
        return res.status(400).send({
            code: 201,
            success: "失败",
            message: "请提供学校ID"
        });
    }
    
    const sql = `
        SELECT 
            sp.specialized_id,
            sp.specialized_content,
            sp.school_id
        FROM specialized sp
        WHERE sp.school_id = ?
        ORDER BY sp.specialized_content
    `;
    
    db.query(sql, [schoolId], (err, result) => {
        if (err) {
            res.status(500).send({
                code: 201,
                success: "失败",
                message: "查询专业信息失败",
                error: err.message
            });
        } else {
            res.send({
                code: 200,
                success: "成功",
                message: "查询专业信息成功",
                result: result
            });
        }
    });
});

// 添加专业信息
router.post('/addSpecialized', (req, res) => {
    const { school_id, specialized_content } = req.body;

    if (!school_id || !specialized_content) {
        return res.status(400).send({
            code: 201,
            success: "失败",
            message: "学校ID和专业名称不能为空"
        });
    }

    const sql = 'INSERT INTO specialized (school_id, specialized_content) VALUES (?, ?)';
    db.query(sql, [school_id, specialized_content], (err, result) => {
        if (err) {
            res.status(500).send({
                code: 201,
                success: "失败",
                message: "添加专业失败",
                error: err.message
            });
        } else {
            res.send({
                code: 200,
                success: "成功",
                message: "添加专业成功",
                data: {
                    specialized_id: result.insertId,
                    school_id,
                    specialized_content
                }
            });
        }
    });
});

// 获取学校专业关联信息
router.get('/getSchoolSpecializedInfo', (req, res) => {
    const sql = `
        SELECT 
            ss.school_specialized,
            ss.school_id,
            ss.specialized_id,
            s.school_name,
            s.school_image,
            s.school_address,
            sp.specialized_content
        FROM school_specialized ss
        LEFT JOIN school s ON ss.school_id = s.school_id
        LEFT JOIN specialized sp ON ss.specialized_id = sp.specialized_id
        ORDER BY ss.school_id, sp.specialized_content
    `;
    
    db.query(sql, (err, result) => {
        if (err) {
            res.status(500).send({
                code: 201,
                success: "失败",
                message: "查询学校专业关联信息失败",
                error: err.message
            });
        } else {
            res.send({
                code: 200,
                success: "成功",
                message: "查询学校专业关联信息成功",
                result: result
            });
        }
    });
});

// 根据学校ID获取专业关联信息
router.get('/getSchoolSpecializedBySchoolId/:schoolId', (req, res) => {
    const schoolId = req.params.schoolId;
    
    if (!schoolId) {
        return res.status(400).send({
            code: 201,
            success: "失败",
            message: "请提供学校ID"
        });
    }
    
    const sql = `
        SELECT 
            ss.school_specialized,
            ss.school_id,
            ss.specialized_id,
            s.school_name,
            s.school_image,
            s.school_address,
            sp.specialized_content
        FROM school_specialized ss
        LEFT JOIN school s ON ss.school_id = s.school_id
        LEFT JOIN specialized sp ON ss.specialized_id = sp.specialized_id
        WHERE ss.school_id = ?
        ORDER BY sp.specialized_content
    `;
    
    db.query(sql, [schoolId], (err, result) => {
        if (err) {
            res.status(500).send({
                code: 201,
                success: "失败",
                message: "查询学校专业关联信息失败",
                error: err.message
            });
        } else {
            // 如果该学校没有专业数据，返回一个默认信息
            if (result.length === 0) {
                // 先查询学校信息
                const schoolSql = "SELECT * FROM school WHERE school_id = ?";
                db.query(schoolSql, [schoolId], (schoolErr, schoolResult) => {
                    if (schoolErr || schoolResult.length === 0) {
                        return res.status(404).send({
                            code: 201,
                            success: "失败",
                            message: "未找到该学校信息"
                        });
                    }
                    
                    res.send({
                        code: 200,
                        success: "成功",
                        message: "该学校暂无专业关联信息",
                        result: [{
                            school_specialized: null,
                            school_id: parseInt(schoolId),
                            specialized_id: null,
                            school_name: schoolResult[0].school_name,
                            school_image: schoolResult[0].school_image,
                            school_address: schoolResult[0].school_address,
                            specialized_content: "暂无专业信息"
                        }]
                    });
                });
            } else {
                res.send({
                    code: 200,
                    success: "成功",
                    message: "查询学校专业关联信息成功",
                    result: result
                });
            }
        }
    });
});

// 添加学校专业关联信息
router.post('/addSchoolSpecialized', (req, res) => {
    const { school_id, specialized_id } = req.body;
    
    if (!school_id || !specialized_id) {
        return res.status(400).send({
            code: 201,
            success: "失败",
            message: "学校ID和专业ID不能为空"
        });
    }
    
    // 先检查是否已存在相同的关联
    const checkSql = 'SELECT * FROM school_specialized WHERE school_id = ? AND specialized_id = ?';
    db.query(checkSql, [school_id, specialized_id], (checkErr, checkResult) => {
        if (checkErr) {
            return res.status(500).send({
                code: 201,
                success: "失败",
                message: "查询失败",
                error: checkErr.message
            });
        }
        
        if (checkResult.length > 0) {
            return res.status(400).send({
                code: 201,
                success: "失败",
                message: "该学校与专业的关联已存在"
            });
        }
        
        // 插入新的关联
        const insertSql = 'INSERT INTO school_specialized (school_id, specialized_id) VALUES (?, ?)';
        db.query(insertSql, [school_id, specialized_id], (insertErr, insertResult) => {
            if (insertErr) {
                res.status(500).send({
                    code: 201,
                    success: "失败",
                    message: "添加学校专业关联失败",
                    error: insertErr.message
                });
            } else {
                res.send({
                    code: 200,
                    success: "成功",
                    message: "添加学校专业关联成功",
                    data: {
                        school_specialized: insertResult.insertId,
                        school_id,
                        specialized_id
                    }
                });
            }
        });
    });
});

// 批量提交志愿信息
router.post('/submitVolunteers', (req, res) => {
    const { user_id, volunteers, append } = req.body;
    
    if (!user_id || !volunteers || !Array.isArray(volunteers) || volunteers.length === 0) {
        return res.status(400).send({
            code: 201,
            success: "失败",
            message: "用户ID和志愿信息不能为空"
        });
    }
    
    // 检查用户是否已经提交过志愿
    const checkSql = 'SELECT * FROM admission WHERE user_id = ?';
    db.query(checkSql, [user_id], (err, result) => {
        if (err) {
            return res.status(500).send({
                code: 201,
                success: "失败",
                message: "查询失败",
                error: err.message
            });
        }
        
        // 如果已经提交过志愿，并且不是追加模式，则删除已有志愿再添加新的
        if (result.length > 0 && !append) {
            // 删除已有志愿
            const deleteSql = 'DELETE FROM admission WHERE user_id = ?';
            db.query(deleteSql, [user_id], (err, deleteResult) => {
                if (err) {
                    return res.status(500).send({
                        code: 201,
                        success: "失败",
                        message: "删除已有志愿失败",
                        error: err.message
                    });
                }
                
                // 删除成功后继续添加新志愿
                insertVolunteers();
            });
        } else {
            // 如果没有提交过志愿或者是追加模式，直接添加新志愿
            insertVolunteers();
        }
    });
    
    // 插入志愿信息的函数
    function insertVolunteers() {
        // 如果是追加模式，需要检查是否有重复的学校和专业组合
        if (append) {
            // 获取用户当前的所有志愿
            const getCurrentVolunteersSql = 'SELECT school_id, specialized_id FROM admission WHERE user_id = ?';
            db.query(getCurrentVolunteersSql, [user_id], (err, currentVolunteers) => {
                if (err) {
                    return res.status(500).send({
                        code: 201,
                        success: "失败",
                        message: "获取当前志愿失败",
                        error: err.message
                    });
                }
                
                // 过滤掉已存在的志愿组合
                const existingCombinations = new Set();
                currentVolunteers.forEach(v => {
                    existingCombinations.add(`${v.school_id}_${v.specialized_id || 'null'}`);
                });
                
                const newVolunteers = volunteers.filter(v => {
                    const key = `${v.school_id}_${v.specialized_id || 'null'}`;
                    return !existingCombinations.has(key);
                });
                
                if (newVolunteers.length === 0) {
                    return res.send({
                        code: 200,
                        success: "成功",
                        message: "所有志愿已存在，无需添加",
                        data: {
                            user_id,
                            volunteers_count: volunteers.length,
                            inserted_count: 0
                        }
                    });
                }
                
                // 准备批量插入的SQL和参数
                const insertSql = 'INSERT INTO admission (user_id, school_id, specialized_id, admission_state) VALUES ?';
                const values = newVolunteers.map(v => [user_id, v.school_id, v.specialized_id || null, "未录取"]);
                
                db.query(insertSql, [values], (err, result) => {
                    if (err) {
                        res.status(500).send({
                            code: 201,
                            success: "失败",
                            message: "保存志愿信息失败",
                            error: err.message
                        });
                    } else {
                        res.send({
                            code: 200,
                            success: "成功",
                            message: "追加志愿信息成功",
                            data: {
                                user_id,
                                volunteers_count: newVolunteers.length,
                                inserted_count: result.affectedRows
                            }
                        });
                    }
                });
            });
        } else {
            // 非追加模式，直接插入所有志愿
            const insertSql = 'INSERT INTO admission (user_id, school_id, specialized_id, admission_state) VALUES ?';
            const values = volunteers.map(v => [user_id, v.school_id, v.specialized_id || null, "未录取"]);
            
            db.query(insertSql, [values], (err, result) => {
                if (err) {
                    res.status(500).send({
                        code: 201,
                        success: "失败",
                        message: "保存志愿信息失败",
                        error: err.message
                    });
                } else {
                    res.send({
                        code: 200,
                        success: "成功",
                        message: "提交志愿信息成功",
                        data: {
                            user_id,
                            volunteers_count: volunteers.length,
                            inserted_count: result.affectedRows
                        }
                    });
                }
            });
        }
    }
});

module.exports = router;

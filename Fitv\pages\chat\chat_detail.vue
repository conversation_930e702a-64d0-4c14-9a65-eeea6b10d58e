<!-- 消息详情页面 -->
<template>
	<view class="detail-container">
		<!-- 加载状态 -->
		<view class="loading-container" v-if="loading">
			<uni-icons type="spinner-cycle" size="30" color="#007aff"></uni-icons>
			<text class="loading-text">加载中...</text>
		</view>
		
		<!-- 消息详情 -->
		<view class="message-detail" v-else-if="messageDetail">
			<!-- 用户信息卡片 -->
			<view class="user-card">
				<image :src="getAvatarUrl(messageDetail.user_image)" class="avatar"></image>
				<view class="user-info">
					<text class="username">{{messageDetail.nick_name || '用户' + messageDetail.user_id}}</text>
					<br />
					<text class="post-time">{{getCurrentTime()}}</text>
				</view>
			</view>
			
			<!-- 消息内容卡片 -->
			<view class="content-card">
				<text class="content-text">{{messageDetail.message_content}}</text>
				
				<!-- 图片网格 -->
				<view class="images-grid" v-if="hasImages(messageDetail)">
					<image 
						v-for="(image, index) in getMessageImages(messageDetail)" 
						:key="index"
						:src="getImageUrl(image)" 
						mode="aspectFill" 
						class="content-image"
						@click="previewImage(getMessageImages(messageDetail), index)"
					></image>
				</view>
			</view>
			
			<!-- 互动统计 -->
			<view class="stats-card">
				<view class="stat-item">
					<uni-icons type="heart" size="18" color="#ff6b6b"></uni-icons>
					<text class="stat-text">{{messageDetail.likes || 0}}</text>
				</view>
				<view class="stat-item">
					<uni-icons type="chat" size="18" color="#4ecdc4"></uni-icons>
					<text class="stat-text">{{comments.length}}</text>
				</view>
			</view>
			
			<!-- 评论区域 -->
			<view class="comments-card">
				<view class="section-title">
					<text class="title-text">评论</text>
					<text class="count-badge">{{comments.length}}</text>
				</view>
				
				<view v-if="comments.length === 0" class="empty-state">
					<uni-icons type="chat" size="40" color="#e0e0e0"></uni-icons>
					<text class="empty-text">还没有评论，快来抢沙发吧~</text>
				</view>
				
				<view class="comment-list" v-else>
					<view class="comment-item" v-for="(comment, index) in comments" :key="index">
						<image :src="comment.avatar" class="comment-avatar"></image>
						<view class="comment-content">
							<text class="comment-user">{{comment.username}}</text>
							<text class="comment-text">{{comment.content}}</text>
							<image v-if="comment.image" :src="comment.image" mode="widthFix" class="comment-image" @click="previewImage([comment.image], 0)"></image>
							<text class="comment-time">{{comment.time}}</text>
						</view>
					</view>
				</view>
				
				<!-- 评论输入 -->
				<view class="comment-input-card">
					<view class="input-wrapper">
						<input 
							type="text" 
							v-model="newComment" 
							placeholder="写下你的想法..." 
							class="comment-input"
							@confirm="sendComment"
						/>
						<view class="input-actions">
							<view class="action-btn" @click="chooseCommentImage">
								<uni-icons type="image" size="20" color="#999"></uni-icons>
							</view>
							<button 
								class="send-btn" 
								@click="sendComment" 
								:disabled="!canSendComment"
								:class="{ 'active': canSendComment }"
							>
								发送
							</button>
						</view>
					</view>
					
					<!-- 图片预览 -->
					<view class="image-preview" v-if="commentImage">
						<image :src="commentImage" mode="aspectFill" class="preview-image"></image>
						<view class="remove-btn" @click="removeCommentImage">
							<uni-icons type="close" size="14" color="#fff"></uni-icons>
						</view>
					</view>
				</view>
			</view>
		</view>
		
		<!-- 错误状态 -->
		<view class="error-container" v-else>
			<uni-icons type="info" size="40" color="#ff6b6b"></uni-icons>
			<text class="error-text">消息不存在或已被删除</text>
		</view>
	</view>
</template>

<script>
import request from '../../utils/request.js';

export default {
	data() {
		return {
			messageId: '',
			messageDetail: null,
			comments: [],
			loading: true,
			newComment: '',
			commentImage: '',
			userId: ''
		}
	},
	
	computed: {
		canSendComment() {
			const hasContent = this.newComment.trim().length > 0 || this.commentImage;
			const hasUserId = this.userId && this.userId !== '';
			return hasContent && hasUserId;
		}
	},
	
	onLoad(options) {
		this.messageId = options.messageId;
		this.initUserData();
		
		if (this.messageId) {
			this.loadMessageDetail();
		} else {
			this.showError('消息ID缺失');
		}
	},
	
	methods: {
		// 初始化用户数据
		initUserData() {
			try {
				const userInfoStr = uni.getStorageSync('userInfo');
				if (userInfoStr) {
					const userInfo = JSON.parse(userInfoStr);
					this.userId = userInfo.id || userInfo.user_id || '';
				}
				
				if (!this.userId) {
					this.userId = uni.getStorageSync('userId') || uni.getStorageSync('user_id') || '';
				}
				
				if (!this.userId) {
					this.redirectToLogin();
				}
			} catch (e) {
				console.error('获取用户信息失败:', e);
				this.redirectToLogin();
			}
		},
		
		// 跳转到登录页
		redirectToLogin() {
			uni.showToast({
				title: '请先登录',
				icon: 'none'
			});
			setTimeout(() => {
				uni.navigateTo({ url: '/pages/Me/Login' });
			}, 1500);
		},
		
		// 显示错误
		showError(message) {
			uni.showToast({
				title: message,
				icon: 'none'
			});
			this.loading = false;
		},
		
		// 加载消息详情
		async loadMessageDetail() {
			try {
				const res = await request.post('/message/detail', {
					message_id: this.messageId
				});
				
				if (res.code === 200 && res.result) {
					this.messageDetail = res.result;
					console.log('消息详情:', this.messageDetail);
					console.log('创建时间:', this.messageDetail.create_time);
					await this.loadComments();
				} else {
					this.showError(res.message || '获取消息详情失败');
				}
			} catch (err) {
				console.error('加载消息详情失败:', err);
				this.showError('网络请求失败');
			} finally {
				this.loading = false;
			}
		},
		
		// 加载评论
		async loadComments() {
			try {
				const res = await request.post('/message/comments', {
					message_id: this.messageId
				});
				
				if (res.code === 200) {
					this.comments = res.result.map(comment => ({
						username: comment.nick_name || '用户' + comment.user_id,
						avatar: this.getAvatarUrl(comment.user_image),
						content: comment.comment_content,
						image: comment.comment_image ? this.getImageUrl(comment.comment_image) : null,
						time: this.formatTime(comment.create_time)
					}));
				}
			} catch (err) {
				console.error('加载评论失败:', err);
			}
		},
		
		// 发送评论
		async sendComment() {
			if (!this.canSendComment) return;
			
			uni.showLoading({ title: '发送中...' });
			
			try {
				let res;
				if (this.commentImage) {
					res = await this.uploadCommentWithImage();
				} else {
					res = await request.post('/message/addComment', {
						message_id: this.messageId,
						user_id: this.userId,
						comment_content: this.newComment
					});
				}
				
				this.handleCommentResponse(res);
			} catch (err) {
				this.handleCommentError(err);
			}
		},
		
		// 上传带图片的评论
		uploadCommentWithImage() {
			return new Promise((resolve, reject) => {
				uni.uploadFile({
					url: request.baseUrl + '/message/addComment',
					filePath: this.commentImage,
					name: 'comment_image',
					formData: {
						message_id: String(this.messageId),
						user_id: String(this.userId),
						comment_content: this.newComment || ''
					},
					success: (res) => {
						try {
							resolve(JSON.parse(res.data));
						} catch (e) {
							reject(e);
						}
					},
					fail: reject
				});
			});
		},
		
		// 处理评论响应
		handleCommentResponse(res) {
			uni.hideLoading();
			if (res.code === 200) {
				uni.showToast({
					title: '评论成功',
					icon: 'success'
				});
				this.resetCommentForm();
				this.loadComments();
			} else {
				uni.showToast({
					title: res.message || '评论失败',
					icon: 'none'
				});
			}
		},
		
		// 处理评论错误
		handleCommentError(err) {
			uni.hideLoading();
			console.error('评论失败:', err);
			uni.showToast({
				title: '评论失败，请重试',
				icon: 'none'
			});
		},
		
		// 重置评论表单
		resetCommentForm() {
			this.newComment = '';
			this.commentImage = '';
		},
		
		// 选择评论图片
		chooseCommentImage() {
			uni.chooseImage({
				count: 1,
				sizeType: ['compressed'],
				sourceType: ['album', 'camera'],
				success: (res) => {
					this.commentImage = res.tempFilePaths[0];
				}
			});
		},
		
		// 移除评论图片
		removeCommentImage() {
			this.commentImage = '';
		},
		
		// 获取头像URL
		getAvatarUrl(userImage) {
			if (userImage && userImage.trim() !== '') {
				return userImage.startsWith('http') ? userImage : request.baseUrl + userImage;
			}
			return '/static/default-avatar.png';
		},
		
		// 获取图片URL
		getImageUrl(imagePath) {
			if (!imagePath) return '';
			return imagePath.startsWith('http') ? imagePath : request.baseUrl + imagePath;
		},
		
		// 检查是否有图片
		hasImages(item) {
			return item.message_image || item.message_imageone || item.message_imagetwo;
		},
		
		// 获取消息图片数组
		getMessageImages(item) {
			const images = [];
			if (item.message_image) images.push(item.message_image);
			if (item.message_imageone) images.push(item.message_imageone);
			if (item.message_imagetwo) images.push(item.message_imagetwo);
			return images;
		},
		
		// 预览图片
		previewImage(images, current) {
			uni.previewImage({
				urls: images.map(img => this.getImageUrl(img)),
				current: current
			});
		},
		
		// 获取当前时间
		getCurrentTime() {
			const now = new Date();
			const year = now.getFullYear();
			const month = String(now.getMonth() + 1).padStart(2, '0');
			const day = String(now.getDate()).padStart(2, '0');
			const hours = String(now.getHours()).padStart(2, '0');
			const minutes = String(now.getMinutes()).padStart(2, '0');
			
			return `${year}-${month}-${day} ${hours}:${minutes}`;
		},
		
		// 移除或简化 formatTime 方法
		formatTime(timeStr) {
			return this.getCurrentTime();
		}
	}
}
</script>

<style scoped>
.detail-container {
    min-height: 100vh;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    padding: 20rpx;
}

.loading-container, .error-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 60vh;
    gap: 20rpx;
}

.loading-text, .error-text {
    font-size: 28rpx;
    color: #666;
}

.message-detail {
    display: flex;
    flex-direction: column;
    gap: 24rpx;
}

/* 卡片通用样式 */
.user-card, .content-card, .stats-card, .comments-card {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 24rpx;
    padding: 32rpx;
    box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.08);
    backdrop-filter: blur(10px);
}

/* 用户信息卡片 */
.user-card {
    display: flex;
    align-items: center;
    gap: 24rpx;
}

.avatar {
    width: 88rpx;
    height: 88rpx;
    border-radius: 20rpx;
    border: 4rpx solid #fff;
    box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
}

.username {
    font-size: 32rpx;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 8rpx;
}

.post-time {
    font-size: 24rpx;
    color: #95a5a6;
}

/* 内容卡片 */
.content-text {
    font-size: 30rpx;
    line-height: 1.6;
    color: #34495e;
    margin-bottom: 24rpx;
}

.images-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200rpx, 1fr));
    gap: 16rpx;
    margin-top: 24rpx;
}

.content-image {
    width: 100%;
    height: 200px;
    border-radius: 16rpx;
    object-fit: cover;
}

/* 统计卡片 */
.stats-card {
    display: flex;
    gap: 40rpx;
    padding: 24rpx 32rpx;
}

.stat-item {
    display: flex;
    align-items: center;
    gap: 12rpx;
}

.stat-text {
    font-size: 26rpx;
    color: #7f8c8d;
    font-weight: 500;
}

/* 评论卡片 */
.section-title {
    display: flex;
    align-items: center;
    gap: 16rpx;
    margin-bottom: 32rpx;
}

.title-text {
    font-size: 32rpx;
    font-weight: 600;
    color: #2c3e50;
}

.count-badge {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    font-size: 22rpx;
    padding: 8rpx 16rpx;
    border-radius: 20rpx;
    font-weight: 500;
}

.empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 20rpx;
    padding: 80rpx 0;
}

.empty-text {
    font-size: 28rpx;
    color: #bdc3c7;
}

/* 评论列表 */
.comment-list {
    display: flex;
    flex-direction: column;
    gap: 32rpx;
    margin-bottom: 32rpx;
}

.comment-item {
    display: flex;
    gap: 20rpx;
}

.comment-avatar {
    width: 64rpx;
    height: 64rpx;
    border-radius: 32rpx;
    flex-shrink: 0;
}

.comment-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 8rpx;
}

.comment-user {
    font-size: 26rpx;
    font-weight: 600;
    color: #3498db;
}

.comment-text {
    font-size: 28rpx;
    line-height: 1.5;
    color: #2c3e50;
}

.comment-image {
    max-width: 300rpx;
    border-radius: 12rpx;
    margin-top: 12rpx;
}

.comment-time {
    font-size: 22rpx;
    color: #95a5a6;
}

/* 评论输入 */
.comment-input-card {
    border-top: 2rpx solid #ecf0f1;
    padding-top: 32rpx;
    margin-top: 32rpx;
}

.input-wrapper {
    display: flex;
    align-items: center;
    gap: 16rpx;
    background: #f8f9fa;
    border-radius: 32rpx;
    padding: 16rpx 24rpx;
}

.comment-input {
    flex: 1;
    font-size: 28rpx;
    color: #2c3e50;
    background: transparent;
    border: none;
}

.input-actions {
    display: flex;
    align-items: center;
    gap: 16rpx;
}

.action-btn {
    width: 60rpx;
    height: 64rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 32rpx;
    background: #ecf0f1;
    transition: all 0.3s ease;
}

.action-btn:active {
    background: #bdc3c7;
    transform: scale(0.95);
}

.send-btn {
    background: #bdc3c7;
    color: #7f8c8d;
    border: none;
    border-radius: 32rpx;
    padding: 16rpx 32rpx;
    font-size: 26rpx;
    font-weight: 500;
    transition: all 0.3s ease;
}

.send-btn.active {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    transform: translateY(-2rpx);
    box-shadow: 0 8rpx 24rpx rgba(102, 126, 234, 0.3);
}

/* 图片预览 */
.image-preview {
    position: relative;
    margin-top: 20rpx;
    display: inline-block;
}

.preview-image {
    width: 120rpx;
    height: 120rpx;
    border-radius: 16rpx;
    object-fit: cover;
}

.remove-btn {
    position: absolute;
    top: -12rpx;
    right: -12rpx;
    width: 40rpx;
    height: 40rpx;
    background: #e74c3c;
    border-radius: 20rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 4rpx 12rpx rgba(231, 76, 60, 0.3);
}
</style>


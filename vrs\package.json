{"name": "shop", "version": "0.1.0", "private": true, "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build", "lint": "vue-cli-service lint"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "axios": "^1.7.9", "core-js": "^3.8.3", "cors": "^2.8.5", "echarts": "^5.5.1", "element-plus": "^2.9.0", "express": "^4.21.1", "int": "^0.2.0", "mysql": "^2.18.1", "vue": "^3.2.13", "vue-router": "^4.0.3", "vuex": "^4.0.0"}, "devDependencies": {"@babel/core": "^7.12.16", "@babel/eslint-parser": "^7.12.16", "@vue/cli-plugin-babel": "~5.0.0", "@vue/cli-plugin-eslint": "~5.0.0", "@vue/cli-plugin-router": "~5.0.0", "@vue/cli-plugin-vuex": "~5.0.0", "@vue/cli-service": "~5.0.0", "eslint": "^7.32.0", "eslint-plugin-vue": "^8.0.3"}}
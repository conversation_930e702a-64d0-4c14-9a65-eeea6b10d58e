<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>学校信息上传</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 28px;
            margin-bottom: 10px;
        }

        .header p {
            opacity: 0.9;
            font-size: 16px;
        }

        .form-container {
            padding: 40px;
        }

        .form-section {
            margin-bottom: 40px;
        }

        .section-title {
            font-size: 20px;
            color: #333;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid #667eea;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-row {
            display: flex;
            gap: 20px;
        }

        .form-row .form-group {
            flex: 1;
        }

        label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #555;
        }

        input, textarea, select {
            width: 100%;
            padding: 12px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-size: 14px;
            transition: border-color 0.3s ease;
        }

        input:focus, textarea:focus, select:focus {
            outline: none;
            border-color: #667eea;
        }

        textarea {
            resize: vertical;
            min-height: 100px;
        }

        .file-upload {
            position: relative;
            display: inline-block;
            cursor: pointer;
            width: 100%;
        }

        .file-upload input[type=file] {
            position: absolute;
            opacity: 0;
            width: 100%;
            height: 100%;
            cursor: pointer;
        }

        .file-upload-label {
            display: block;
            padding: 12px;
            background: #f8f9fa;
            border: 2px dashed #dee2e6;
            border-radius: 8px;
            text-align: center;
            color: #6c757d;
            transition: all 0.3s ease;
        }

        .file-upload:hover .file-upload-label {
            background: #e9ecef;
            border-color: #667eea;
        }

        .specialized-list {
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            padding: 20px;
            background: #f8f9fa;
        }

        .specialized-item {
            background: white;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 15px;
            position: relative;
        }

        .specialized-item:last-child {
            margin-bottom: 0;
        }

        .remove-btn {
            position: absolute;
            top: 10px;
            right: 10px;
            background: #dc3545;
            color: white;
            border: none;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            cursor: pointer;
            font-size: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .add-specialized-btn {
            background: #28a745;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            margin-top: 15px;
            transition: background-color 0.3s ease;
        }

        .add-specialized-btn:hover {
            background: #218838;
        }

        .submit-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 15px 40px;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            width: 100%;
            transition: transform 0.3s ease;
        }

        .submit-btn:hover {
            transform: translateY(-2px);
        }

        .submit-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .loading {
            display: none;
            text-align: center;
            padding: 20px;
        }

        .loading.show {
            display: block;
        }

        .message {
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            display: none;
        }

        .message.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .message.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .message.show {
            display: block;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🏫 学校信息上传</h1>
            <p>添加学校基本信息、专业设置和分数线</p>
        </div>

        <div class="form-container">
            <div id="message" class="message"></div>
            
            <form id="schoolForm" enctype="multipart/form-data">
                <!-- 学校基本信息 -->
                <div class="form-section">
                    <h2 class="section-title">📋 学校基本信息</h2>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="school_account">学校账号 *</label>
                            <input type="text" id="school_account" name="school_account" required>
                        </div>
                        <div class="form-group">
                            <label for="school_password">学校密码 *</label>
                            <input type="password" id="school_password" name="school_password" required>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="school_name">学校名称 *</label>
                        <input type="text" id="school_name" name="school_name" required>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="school_phone">联系电话</label>
                            <input type="tel" id="school_phone" name="school_phone">
                        </div>
                        <div class="form-group">
                            <label for="school_email">邮箱地址</label>
                            <input type="email" id="school_email" name="school_email">
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="school_address">学校地址</label>
                        <input type="text" id="school_address" name="school_address">
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="school_characteristic">学校特色</label>
                            <input type="text" id="school_characteristic" name="school_characteristic">
                        </div>
                        <div class="form-group">
                            <label for="school_number">学校编号</label>
                            <input type="text" id="school_number" name="school_number">
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="school_idea">办学理念</label>
                        <textarea id="school_idea" name="school_idea" placeholder="请输入学校的办学理念和教育特色"></textarea>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="school_image">学校图片</label>
                            <div class="file-upload">
                                <input type="file" id="school_image" name="school_image" accept="image/*">
                                <label for="school_image" class="file-upload-label">
                                    📷 点击选择学校图片
                                </label>
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="school_video">学校视频</label>
                            <div class="file-upload">
                                <input type="file" id="school_video" name="school_video" accept="video/*">
                                <label for="school_video" class="file-upload-label">
                                    🎥 点击选择学校视频
                                </label>
                            </div>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="identity">身份类型</label>
                        <select id="identity" name="identity">
                            <option value="">请选择身份类型</option>
                            <option value="public">公立学校</option>
                            <option value="private">私立学校</option>
                            <option value="joint">合作办学</option>
                        </select>
                    </div>
                </div>

                <!-- 专业信息 -->
                <div class="form-section">
                    <h2 class="section-title">🎓 专业设置</h2>
                    
                    <div class="specialized-list" id="specializedList">
                        <div class="specialized-item">
                            <div class="form-row">
                                <div class="form-group">
                                    <label>专业名称</label>
                                    <input type="text" name="specialized_content" placeholder="如：计算机科学与技术">
                                </div>
                                <div class="form-group">
                                    <label>最低分数</label>
                                    <input type="number" name="min_scores" placeholder="如：580">
                                </div>
                                <div class="form-group">
                                    <label>最高分数</label>
                                    <input type="number" name="max_scores" placeholder="如：650">
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <button type="button" class="add-specialized-btn" onclick="addSpecialized()">
                        ➕ 添加专业
                    </button>
                </div>

                <div class="loading" id="loading">
                    <p>⏳ 正在上传，请稍候...</p>
                </div>

                <button type="submit" class="submit-btn" id="submitBtn">
                    🚀 提交学校信息
                </button>
            </form>
        </div>
    </div>

    <script>
        // 添加专业
        function addSpecialized() {
            const specializedList = document.getElementById('specializedList');
            const newItem = document.createElement('div');
            newItem.className = 'specialized-item';
            newItem.innerHTML = `
                <button type="button" class="remove-btn" onclick="removeSpecialized(this)">×</button>
                <div class="form-row">
                    <div class="form-group">
                        <label>专业名称</label>
                        <input type="text" name="specialized_content" placeholder="如：计算机科学与技术">
                    </div>
                    <div class="form-group">
                        <label>最低分数</label>
                        <input type="number" name="min_scores" placeholder="如：580">
                    </div>
                    <div class="form-group">
                        <label>最高分数</label>
                        <input type="number" name="max_scores" placeholder="如：650">
                    </div>
                </div>
            `;
            specializedList.appendChild(newItem);
        }

        // 删除专业
        function removeSpecialized(btn) {
            const specializedList = document.getElementById('specializedList');
            if (specializedList.children.length > 1) {
                btn.parentElement.remove();
            } else {
                showMessage('至少需要保留一个专业', 'error');
            }
        }

        // 显示消息
        function showMessage(text, type) {
            const message = document.getElementById('message');
            message.textContent = text;
            message.className = `message ${type} show`;
            setTimeout(() => {
                message.classList.remove('show');
            }, 5000);
        }

        // 文件选择提示
        document.getElementById('school_image').addEventListener('change', function(e) {
            const label = this.nextElementSibling;
            if (e.target.files.length > 0) {
                label.textContent = `📷 已选择: ${e.target.files[0].name}`;
            } else {
                label.textContent = '📷 点击选择学校图片';
            }
        });

        document.getElementById('school_video').addEventListener('change', function(e) {
            const label = this.nextElementSibling;
            if (e.target.files.length > 0) {
                label.textContent = `🎥 已选择: ${e.target.files[0].name}`;
            } else {
                label.textContent = '🎥 点击选择学校视频';
            }
        });

        // 表单提交
        document.getElementById('schoolForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const submitBtn = document.getElementById('submitBtn');
            const loading = document.getElementById('loading');
            
            // 收集专业数据
            const specializedItems = document.querySelectorAll('.specialized-item');
            const specializedList = [];
            
            for (let item of specializedItems) {
                const content = item.querySelector('input[name="specialized_content"]').value;
                const minScores = item.querySelector('input[name="min_scores"]').value;
                const maxScores = item.querySelector('input[name="max_scores"]').value;
                
                if (content.trim()) {
                    specializedList.push({
                        specialized_content: content.trim(),
                        min_scores: minScores ? parseInt(minScores) : null,
                        max_scores: maxScores ? parseInt(maxScores) : null
                    });
                }
            }

            // 验证必填字段
            const schoolAccount = document.getElementById('school_account').value.trim();
            const schoolPassword = document.getElementById('school_password').value.trim();
            const schoolName = document.getElementById('school_name').value.trim();

            if (!schoolAccount || !schoolPassword || !schoolName) {
                showMessage('请填写学校账号、密码和名称', 'error');
                return;
            }

            // 创建FormData
            const formData = new FormData();
            
            // 添加学校基本信息
            formData.append('school_account', schoolAccount);
            formData.append('school_password', schoolPassword);
            formData.append('school_name', schoolName);
            formData.append('school_phone', document.getElementById('school_phone').value || '');
            formData.append('school_email', document.getElementById('school_email').value || '');
            formData.append('school_address', document.getElementById('school_address').value || '');
            formData.append('school_characteristic', document.getElementById('school_characteristic').value || '');
            formData.append('school_number', document.getElementById('school_number').value || '');
            formData.append('school_idea', document.getElementById('school_idea').value || '');
            formData.append('identity', document.getElementById('identity').value || '学校');
            
            // 添加文件
            const imageFile = document.getElementById('school_image').files[0];
            const videoFile = document.getElementById('school_video').files[0];
            if (imageFile) formData.append('school_image', imageFile);
            if (videoFile) formData.append('school_video', videoFile);
            
            // 添加专业列表 - 确保是字符串格式
            if (specializedList.length > 0) {
                formData.append('specializedList', JSON.stringify(specializedList));
            }

            // 调试：打印FormData内容
            console.log('提交的数据:');
            for (let [key, value] of formData.entries()) {
                console.log(key, ':', value);
            }

            // 显示加载状态
            submitBtn.disabled = true;
            loading.classList.add('show');

            try {
                const response = await fetch('http://************:9077/school/add', {
                    method: 'POST',
                    body: formData,
                    // 不要设置Content-Type，让浏览器自动设置multipart/form-data
                });

                console.log('响应状态:', response.status);
                
                if (!response.ok) {
                    const errorText = await response.text();
                    console.error('服务器错误响应:', errorText);
                    throw new Error(`HTTP ${response.status}: ${errorText}`);
                }

                const result = await response.json();
                console.log('服务器响应:', result);

                if (result.code === 200) {
                    showMessage('学校信息上传成功！', 'success');
                    document.getElementById('schoolForm').reset();
                    // 重置文件选择提示
                    document.querySelector('label[for="school_image"]').textContent = '📷 点击选择学校图片';
                    document.querySelector('label[for="school_video"]').textContent = '🎥 点击选择学校视频';
                    // 重置专业列表
                    resetSpecializedList();
                } else {
                    showMessage(result.message || '上传失败', 'error');
                }
            } catch (error) {
                console.error('上传失败:', error);
                showMessage(`上传失败: ${error.message}`, 'error');
            } finally {
                submitBtn.disabled = false;
                loading.classList.remove('show');
            }
        });

        // 重置专业列表
        function resetSpecializedList() {
            const specializedList = document.getElementById('specializedList');
            specializedList.innerHTML = `
                <div class="specialized-item">
                    <div class="form-row">
                        <div class="form-group">
                            <label>专业名称</label>
                            <input type="text" name="specialized_content" placeholder="如：计算机科学与技术">
                        </div>
                        <div class="form-group">
                            <label>最低分数</label>
                            <input type="number" name="min_scores" placeholder="如：580">
                        </div>
                        <div class="form-group">
                            <label>最高分数</label>
                            <input type="number" name="max_scores" placeholder="如：650">
                        </div>
                    </div>
                </div>
            `;
        }
    </script>
</body>
</html>

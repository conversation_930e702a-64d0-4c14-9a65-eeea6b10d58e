<template>
  <div class="login-container">
    <div class="login-content">
      <div class="login-box">
        <div class="login-header">
          <h2>高考志愿填报系统</h2>
          <p>管理员登录</p>
        </div>
        <el-form
          ref="loginFormRef"
          :model="loginForm"
          :rules="loginRules"
          class="login-form"
        >
          <el-form-item prop="administrator_name">
            <el-input
              v-model="loginForm.administrator_name"
              placeholder="请输入用户名"
              :prefix-icon="User"
            />
          </el-form-item>
          <el-form-item prop="administrator_password">
            <el-input
              v-model="loginForm.administrator_password"
              type="password"
              placeholder="请输入密码"
              :prefix-icon="Lock"
              show-password
              @keyup.enter="handleLogin"
            />
          </el-form-item>
          <el-form-item class="login-btn">
            <el-button
              :loading="loading"
              type="primary"
              style="width: 100%"
              @click="handleLogin"
            >
              {{ loading ? '登录中...' : '登录' }}
            </el-button>
          </el-form-item>
        </el-form>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, reactive } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { User, Lock } from '@element-plus/icons-vue'
import { post } from '@/utils/request'

export default {
  name: 'login',
  components: {
    User,
    Lock
  },
  setup() {
    const router = useRouter()
    const loginFormRef = ref(null)
    const loading = ref(false)

    // 登录表单数据
    const loginForm = reactive({
      administrator_name: '',
      administrator_password: ''
    })

    // 表单验证规则
    const loginRules = {
      administrator_name: [
        { required: true, message: '请输入用户名', trigger: 'blur' }
      ],
      administrator_password: [
        { required: true, message: '请输入密码', trigger: 'blur' }
      ]
    }

    // 登录处理
    const handleLogin = async () => {
      if (!loginFormRef.value) return

      try {
        await loginFormRef.value.validate()
        loading.value = true

        const response = await post('/administrator/login', {
          administrator_name: loginForm.administrator_name,
          administrator_password: loginForm.administrator_password
        })

        if (response && response.code === 200) {
          // 保存管理员信息到本地存储
          localStorage.setItem('adminInfo', JSON.stringify(response.result))
          // 生成临时token或使用管理员ID作为token
          const token = `admin-${response.result.administrator_id}-${Date.now()}`
          localStorage.setItem('token', token)
          
          ElMessage.success('登录成功')
          await router.push('/')
        } else {
          ElMessage.error(response?.success || '登录失败')
        }
      } catch (error) {
        console.error('登录失败：', error)
        ElMessage.error(error.response?.data?.msg || error.message || '登录失败')
      } finally {
        loading.value = false
      }
    }

    return {
      loginFormRef,
      loginForm,
      loginRules,
      loading,
      handleLogin,
      User,
      Lock
    }
  }
}
</script>

<style scoped>
.login-container {
  height: 100vh;
  width: 100vw;
  display: flex;
  justify-content: flex-end;
  align-items: center;
  background: url('@/assets/1.png') no-repeat center center fixed;
  background-size: cover;
  position: fixed;
  top: 0;
  left: 0;
  overflow: hidden;
  /* 添加以下属性让背景图片更清晰 */
  image-rendering: -webkit-optimize-contrast;
  image-rendering: crisp-edges;
  image-rendering: pixelated;
  -webkit-transform: translateZ(0);
  transform: translateZ(0);
}

.login-container::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    to right,
    rgba(255, 255, 255, 0.05),
    rgba(255, 255, 255, 0.6)
  );
  backdrop-filter: blur(0px);
  -webkit-backdrop-filter: blur(0px);
}

.login-content {
  position: relative;
  width: 500px;
  margin-right: 10%;
  z-index: 2;
  animation: fadeIn 0.8s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.login-box {
  width: 100%;
  padding: 40px;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.login-header {
  text-align: center;
  margin-bottom: 40px;
}

.login-header h2 {
  font-size: 28px;
  color: #303133;
  margin-bottom: 12px;
  font-weight: 600;
  background: linear-gradient(135deg, #409EFF, #2c50e8);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.login-header p {
  font-size: 16px;
  color: #606266;
  margin: 0;
}

.login-form {
  margin-top: 30px;
}

.login-form :deep(.el-input) {
  height: 50px;
}

.login-form :deep(.el-input__wrapper) {
  padding: 0 15px;
  background-color: rgba(245, 247, 250, 0.5);
  border: 2px solid transparent;
  border-radius: 12px;
  transition: all 0.3s ease;
}

.login-form :deep(.el-input__wrapper:hover),
.login-form :deep(.el-input__wrapper.is-focus) {
  border-color: #409EFF;
  background-color: rgba(255, 255, 255, 0.7);
  box-shadow: 0 0 0 1px #409EFF;
}

.login-form :deep(.el-input__inner) {
  height: 46px;
  font-size: 15px;
}

.login-form :deep(.el-button) {
  height: 50px;
  font-size: 16px;
  font-weight: 600;
  border-radius: 12px;
  background: linear-gradient(135deg, #409EFF 0%, #2c50e8 100%);
  border: none;
  transition: all 0.3s ease;
}

.login-form :deep(.el-button:hover) {
  transform: translateY(-2px);
  box-shadow: 0 8px 16px rgba(44, 80, 232, 0.3);
}

.login-btn {
  margin-top: 40px;
}

@media (max-width: 768px) {
  .login-content {
    width: 90%;
    margin: 0 auto;
  }

  .login-box {
    padding: 30px;
  }

  .login-header h2 {
    font-size: 24px;
  }

  .login-header p {
    font-size: 14px;
  }
}
</style>

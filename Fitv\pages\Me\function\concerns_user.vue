<!-- 关注的用户信息 -->
<template>
    <view class="user-detail-container">
        <!-- 加载中 -->
        <view v-if="loading" class="loading-container">
            <uni-icons type="spinner-cycle" size="30" color="#667eea"></uni-icons>
            <text class="loading-text">加载中...</text>
        </view>
        
        <!-- 用户信息和发布的内容 -->
        <view v-else class="user-profile">
            <!-- 用户头像和基本信息 -->
            <view class="profile-header">
                <image :src="userDetail.avatar" class="user-avatar" @error="handleAvatarError"></image>
                <view class="user-basic-info">
                    <text class="user-name">{{userDetail.name}}</text>
                    <text class="user-school">{{userDetail.school}}</text>
                </view>
                <button class="follow-btn" :class="userDetail.isFollowed ? 'followed' : ''" @click="toggleFollow">
                    <uni-icons :type="userDetail.isFollowed ? 'heart-filled' : 'heart'" size="16" :color="userDetail.isFollowed ? '#fff' : '#667eea'"></uni-icons>
                    <text>{{userDetail.isFollowed ? '已关注' : '关注'}}</text>
                </button>
            </view>
            
            <!-- 发布的内容列表 -->
            <view class="messages-section">
                <view class="section-title">
                    <uni-icons type="chat" size="18" color="#667eea"></uni-icons>
                    <text>TA的发布</text>
                    <text class="post-count">({{userMessages.length}})</text>
                </view>
                
                <!-- 空状态 -->
                <view v-if="userMessages.length === 0" class="empty-state">
                    <image src="/static/empty.png" class="empty-image"></image>
                    <text class="empty-text">暂无发布内容</text>
                </view>
                
                <!-- 消息列表 -->
                <view v-else class="message-list">
                    <view v-for="(message, index) in userMessages" :key="message.message_id" class="message-item" @click="viewMessageDetail(message)">
                        <view class="message-content">
                            <text class="content-text">{{message.content}}</text>
                            <view v-if="message.images.length > 0" class="image-grid">
                                <image 
                                    v-for="(img, imgIndex) in message.images" 
                                    :key="imgIndex"
                                    :src="img" 
                                    class="content-image"
                                    mode="aspectFill"
                                    @click.stop="previewImage(message.images, imgIndex)"
                                ></image>
                            </view>
                        </view>
                        <view class="message-footer">
                            <text class="publish-time">{{formatTimeByMessageId(message.message_id)}}</text>
                            <view class="action-icons">
                                <view class="action-item">
                                    <uni-icons type="chat" size="14" color="#999"></uni-icons>
                                    <text class="action-text">评论</text>
                                </view>
                            </view>
                        </view>
                    </view>
                </view>
            </view>
        </view>
    </view>
</template>

<script>
import request from '../../../utils/request.js';

export default {
    data() {
        return {
            loading: true,
            userDetail: {
                id: '',
                name: '',
                avatar: '/static/c1.png',
                sex: '',
                birth: '',
                phone: '',
                school: '',
                form: '',
                fraction: '',
                isFollowed: false
            },
            userMessages: [],
            currentUserId: null
        }
    },
    onLoad(options) {
        console.log('用户详情页面参数:', options);
        if (options.userId) {
            this.userDetail.id = options.userId;
            this.userDetail.name = options.userName || '未知用户';
            this.getCurrentUser();
            this.loadUserDetail();
        } else {
            uni.showToast({
                title: '缺少用户信息',
                icon: 'none'
            });
            setTimeout(() => {
                uni.navigateBack();
            }, 1500);
        }
    },
    methods: {
        // 获取当前登录用户信息
        getCurrentUser() {
            try {
                const userInfo = uni.getStorageSync('userInfo');
                if (userInfo) {
                    const user = JSON.parse(userInfo);
                    this.currentUserId = user.id || user.user_id;
                }
            } catch (e) {
                console.error('获取当前用户信息失败:', e);
            }
        },
        
        // 加载用户详细信息和发布的内容
        async loadUserDetail() {
            this.loading = true;
            
            try {
                const res = await request.post('/concern/getUserDetail', {
                    user_id: this.userDetail.id
                });
                
                if (res.code === 200 && res.result) {
                    const { userInfo, messages } = res.result;
                    
                    // 设置用户信息
                    this.userDetail = {
                        id: userInfo.user_id,
                        name: userInfo.nick_name || '未知用户',
                        avatar: this.processAvatarUrl(userInfo.user_image),
                        sex: userInfo.sex || '未知',
                        birth: userInfo.birth || '',
                        phone: userInfo.phone || '未知',
                        school: userInfo.user_school || '未知学校',
                        isFollowed: false
                    };
                    
                    // 处理消息列表
                    this.userMessages = messages.map(msg => ({
                        message_id: msg.message_id,
                        content: msg.message_content || '无内容',
                        images: this.getMessageImages(msg)
                    }));
                    
                    // 检查关注状态
                    this.checkFollowStatus();
                } else {
                    uni.showToast({
                        title: '获取用户信息失败',
                        icon: 'none'
                    });
                }
            } catch (err) {
                console.error('获取用户详情失败:', err);
                uni.showToast({
                    title: '网络请求失败',
                    icon: 'none'
                });
            } finally {
                this.loading = false;
            }
        },
        
        // 获取消息图片
        getMessageImages(message) {
            const images = [];
            if (message.message_image) images.push(this.processAvatarUrl(message.message_image));
            if (message.message_imageone) images.push(this.processAvatarUrl(message.message_imageone));
            if (message.message_imagetwo) images.push(this.processAvatarUrl(message.message_imagetwo));
            return images;
        },
        
        // 格式化时间
        formatTime(timeString) {
            if (!timeString) return '';
            const date = new Date(timeString);
            const now = new Date();
            const diff = now - date;
            
            if (diff < 60000) return '刚刚';
            if (diff < 3600000) return Math.floor(diff / 60000) + '分钟前';
            if (diff < 86400000) return Math.floor(diff / 3600000) + '小时前';
            if (diff < 604800000) return Math.floor(diff / 86400000) + '天前';
            
            return date.toLocaleDateString();
        },
        
        // 预览图片
        previewImage(images, current) {
            uni.previewImage({
                urls: images,
                current: images[current]
            });
        },
        
        // 查看消息详情
        viewMessageDetail(message) {
            uni.navigateTo({
                url: `/pages/chat/chat_detail?messageId=${message.message_id}`
            });
        },
        
        // 检查关注状态
        async checkFollowStatus() {
            if (!this.currentUserId) return;
            
            try {
                const res = await request.post('/concern/checkUserFollow', {
                    user_id: this.currentUserId,
                    target_user_id: this.userDetail.id
                });
                
                if (res.code === 200) {
                    this.userDetail.isFollowed = res.isFollowed;
                }
            } catch (err) {
                console.error('检查关注状态失败:', err);
                this.userDetail.isFollowed = false;
            }
        },
        
        // 处理头像URL
        processAvatarUrl(url) {
            if (!url) return '/static/c1.png';
            if (url.startsWith('http')) return url;
            if (url.startsWith('/static')) return url;
            if (!url.startsWith('/')) url = '/' + url;
            return request.baseUrl + url;
        },
        
        // 处理头像加载错误
        handleAvatarError() {
            this.userDetail.avatar = '/static/c1.png';
        },
        
        // 切换关注状态
        async toggleFollow() {
            if (!this.currentUserId) {
                uni.showToast({
                    title: '请先登录',
                    icon: 'none'
                });
                return;
            }
            
            if (this.currentUserId === this.userDetail.id) {
                uni.showToast({
                    title: '不能关注自己',
                    icon: 'none'
                });
                return;
            }
            
            try {
                uni.showLoading({ title: this.userDetail.isFollowed ? '取消关注中...' : '关注中...' });
                
                const apiUrl = this.userDetail.isFollowed ? '/concern/unfollowUser' : '/concern/followUser';
                const res = await request.post(apiUrl, {
                    user_id: this.currentUserId,
                    target_user_id: this.userDetail.id
                });
                
                uni.hideLoading();
                
                if (res.code === 200) {
                    this.userDetail.isFollowed = !this.userDetail.isFollowed;
                    uni.showToast({
                        title: this.userDetail.isFollowed ? '关注成功' : '取消关注成功',
                        icon: 'success'
                    });
                } else {
                    uni.showToast({
                        title: res.message || '操作失败',
                        icon: 'none'
                    });
                }
            } catch (err) {
                uni.hideLoading();
                console.error('关注操作失败:', err);
                uni.showToast({
                    title: '操作失败，请重试',
                    icon: 'none'
                });
            }
        },
        // 根据消息ID生成时间显示
        formatTimeByMessageId(messageId) {
            if (!messageId) return '未知时间';
            
            // 根据消息ID生成相对时间，较新的消息ID显示为较近的时间
            const hour = Math.abs(messageId % 24); // 0-23小时
            const day = Math.abs(messageId % 7); // 0-6天
            
            if (messageId > 100) {
                return '刚刚';
            } else if (messageId > 50) {
                return hour + '小时前';
            } else if (messageId > 20) {
                return day + '天前';
            } else {
                return '一周前';
            }
        }
    }
}
</script>

<style>
.user-detail-container {
    background-color: #f8f9fa;
    min-height: 100vh;
}

.loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 400rpx;
}

.loading-text {
    margin-top: 20rpx;
    color: #667eea;
    font-size: 28rpx;
}

.profile-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    padding: 40rpx 32rpx;
    display: flex;
    align-items: center;
    position: relative;
}

.user-avatar {
    width: 120rpx;
    height: 120rpx;
    border-radius: 60rpx;
    border: 4rpx solid rgba(255,255,255,0.3);
}

.user-basic-info {
    flex: 1;
    margin-left: 24rpx;
}

.user-name {
    display: block;
    font-size: 36rpx;
    font-weight: 600;
    color: white;
    margin-bottom: 8rpx;
}

.user-school {
    font-size: 26rpx;
    color: rgba(255,255,255,0.9);
}

.follow-btn {
	height: 39px;
    background-color: rgba(255,255,255,0.2);
    border: 2rpx solid rgba(255,255,255,0.5);
    border-radius: 40rpx;
    padding: 16rpx 24rpx;
    display: flex;
    align-items: center;
    color: white;
    font-size: 26rpx;
}

.follow-btn.followed {
    background-color: rgba(255,45,85,0.8);
    border-color: rgba(255,45,85,0.8);
}

.follow-btn text {
    margin-left: 8rpx;
}

.messages-section {
    padding: 32rpx;
}

.section-title {
    display: flex;
    align-items: center;
    margin-bottom: 24rpx;
    font-size: 32rpx;
    font-weight: 600;
    color: #2c3e50;
}

.section-title text {
    margin-left: 12rpx;
}

.post-count {
    margin-left: 8rpx !important;
    font-size: 28rpx !important;
    color: #999 !important;
    font-weight: 400 !important;
}

.empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 80rpx 0;
}

.empty-image {
    width: 200rpx;
    height: 200rpx;
    opacity: 0.5;
}

.empty-text {
    margin-top: 24rpx;
    color: #999;
    font-size: 28rpx;
}

.message-list {
    /* 消息列表容器 */
}

.message-item {
    background-color: white;
    border-radius: 16rpx;
    padding: 24rpx;
    margin-bottom: 16rpx;
    box-shadow: 0 2rpx 12rpx rgba(0,0,0,0.04);
}

.message-content {
    margin-bottom: 16rpx;
}

.content-text {
    font-size: 28rpx;
    color: #2c3e50;
    line-height: 1.6;
    display: block;
    margin-bottom: 16rpx;
}

.image-grid {
    display: flex;
    flex-wrap: wrap;
    gap: 12rpx;
}

.content-image {
    width: 200rpx;
    height: 200rpx;
    border-radius: 12rpx;
}

.message-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.publish-time {
    font-size: 24rpx;
    color: #999;
}

.action-icons {
    display: flex;
    gap: 24rpx;
}

.action-item {
    display: flex;
    align-items: center;
}

.action-text {
    margin-left: 8rpx;
    font-size: 24rpx;
    color: #999;
}
</style>





















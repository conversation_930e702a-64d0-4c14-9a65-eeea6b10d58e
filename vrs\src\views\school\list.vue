<template>
  <div class="school-list-container">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>院校管理</span>
          <el-button type="primary" @click="handleAdd">新增院校</el-button>
        </div>
      </template>

      <!-- 搜索表单 -->
      <el-form :model="searchForm" class="search-form" inline>
        <el-form-item label="院校名称">
          <el-input v-model="searchForm.school_name" placeholder="请输入院校名称" clearable />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">搜索</el-button>
          <el-button @click="resetSearch">重置</el-button>
        </el-form-item>
      </el-form>

      <!-- 数据表格 -->
      <el-table :data="tableData" style="width: 100%" v-loading="loading" border>
        <el-table-column prop="school_name" label="院校名称" min-width="200" align="center" header-align="center" />
        <el-table-column prop="school_image" label="院校图片" width="120" align="center" header-align="center">
          <template #default="scope">
            <el-image 
              :src="scope.row.school_image" 
              style="width: 60px; height: 40px"
              fit="cover"
              :preview-src-list="[scope.row.school_image]"
            />
          </template>
        </el-table-column>
        <el-table-column prop="identity" label="院校类型" min-width="120" align="center" header-align="center" />
        <el-table-column prop="Score_line" label="分数线" width="100" align="center" header-align="center" />
        <el-table-column prop="school_number" label="招生人数" width="120" align="center" header-align="center" />
        <el-table-column label="操作" fixed="right" width="250" align="center" header-align="center">
          <template #default="scope">
            <el-button type="info" link @click="handleView(scope.row)">查看</el-button>
            <el-button type="primary" link @click="handleEdit(scope.row)">编辑</el-button>
            <el-button type="danger" link @click="handleDelete(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 查看对话框 -->
    <el-dialog 
      title="查看院校详情" 
      v-model="viewDialogVisible" 
      width="600px"
    >
      <el-descriptions :column="2" border>
        <el-descriptions-item label="院校ID">{{ viewData.school_id }}</el-descriptions-item>
        <el-descriptions-item label="院校账号">{{ viewData.school_account }}</el-descriptions-item>
        <el-descriptions-item label="院校名称">{{ viewData.school_name }}</el-descriptions-item>
        <el-descriptions-item label="院校类型">{{ viewData.identity }}</el-descriptions-item>
        <el-descriptions-item label="分数线">{{ viewData.Score_line }}</el-descriptions-item>
        <el-descriptions-item label="招生人数">{{ viewData.school_number }}</el-descriptions-item>
        <el-descriptions-item label="联系电话">{{ viewData.school_phone }}</el-descriptions-item>
        <el-descriptions-item label="邮箱">{{ viewData.school_email }}</el-descriptions-item>
        <el-descriptions-item label="地址" :span="2">{{ viewData.school_address }}</el-descriptions-item>
        <el-descriptions-item label="院校特色" :span="2">{{ viewData.school_characteristic }}</el-descriptions-item>
        <el-descriptions-item label="办学理念" :span="2">{{ viewData.school_idea }}</el-descriptions-item>
        <el-descriptions-item label="院校图片" :span="2">
          <el-image 
            v-if="viewData.school_image"
            :src="viewData.school_image" 
            style="width: 100px; height: 60px"
            fit="cover"
            :preview-src-list="[viewData.school_image]"
          />
          <span v-else>暂无图片</span>
        </el-descriptions-item>
        <el-descriptions-item label="院校视频" :span="2">
          <video 
            v-if="viewData.school_video" 
            :src="viewData.school_video" 
            controls 
            style="width: 200px; height: 120px"
          ></video>
          <span v-else>暂无视频</span>
        </el-descriptions-item>
      </el-descriptions>
    </el-dialog>

    <!-- 新增/编辑对话框 -->
    <el-dialog 
      :title="dialogTitle" 
      v-model="dialogVisible" 
      width="800px"
      @close="resetForm"
    >
      <el-form :model="form" :rules="rules" ref="formRef" label-width="100px">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="院校账号" prop="school_account">
              <el-input v-model="form.school_account" placeholder="请输入院校账号" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="院校密码" prop="school_password">
              <el-input v-model="form.school_password" type="password" placeholder="请输入院校密码" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="院校名称" prop="school_name">
              <el-input v-model="form.school_name" placeholder="请输入院校名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="院校类型" prop="identity">
              <el-input v-model="form.identity" placeholder="请输入院校类型" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="分数线" prop="Score_line">
              <el-input-number v-model="form.Score_line" :min="0" placeholder="请输入分数线" style="width: 100%" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="招生人数" prop="school_number">
              <el-input-number v-model="form.school_number" :min="0" placeholder="请输入招生人数" style="width: 100%" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="联系电话" prop="school_phone">
              <el-input v-model="form.school_phone" placeholder="请输入联系电话" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="邮箱" prop="school_email">
              <el-input v-model="form.school_email" placeholder="请输入邮箱" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="院校地址" prop="school_address">
          <el-input v-model="form.school_address" placeholder="请输入院校地址" />
        </el-form-item>
        <el-form-item label="院校图片" prop="school_image">
          <el-input v-model="form.school_image" placeholder="请输入图片URL" />
        </el-form-item>
        <el-form-item label="院校视频" prop="school_video">
          <el-input v-model="form.school_video" placeholder="请输入视频URL" />
        </el-form-item>
        <el-form-item label="院校特色" prop="school_characteristic">
          <el-input v-model="form.school_characteristic" type="textarea" :rows="3" placeholder="请输入院校特色" />
        </el-form-item>
        <el-form-item label="办学理念" prop="school_idea">
          <el-input v-model="form.school_idea" type="textarea" :rows="3" placeholder="请输入办学理念" />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSubmit">确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import axios from 'axios'

export default {
  name: 'SchoolList',
  setup() {
    const loading = ref(false)
    const dialogVisible = ref(false)
    const viewDialogVisible = ref(false)
    const currentPage = ref(1)
    const pageSize = ref(10)
    const total = ref(0)
    const formRef = ref(null)

    const searchForm = ref({
      school_name: ''
    })

    const tableData = ref([])
    const viewData = ref({})

    const form = ref({
      school_id: '',
      school_account: '',
      school_password: '',
      school_name: '',
      school_image: '',
      school_phone: '',
      school_address: '',
      school_characteristic: '',
      school_idea: '',
      identity: '',
      school_email: '',
      Score_line: null,
      school_number: null,
      school_video: ''
    })

    const rules = reactive({
      school_name: [
        { required: true, message: '请输入院校名称', trigger: 'blur' }
      ],
      school_account: [
        { required: true, message: '请输入院校账号', trigger: 'blur' }
      ]
    })

    const dialogTitle = computed(() => {
      return form.value.school_id ? '编辑院校' : '新增院校'
    })

    // 获取数据
    const fetchData = async () => {
      loading.value = true
      try {
        const params = {}
        if (searchForm.value.school_name) {
          params.school_name = searchForm.value.school_name
        }

        const response = await axios.post('http://************:9077/school/', params)
        
        if (response.data && response.data.code === 200) {
          tableData.value = response.data.result || []
          total.value = tableData.value.length
        } else {
          tableData.value = []
          total.value = 0
          ElMessage.error(response.data?.message || '获取数据失败')
        }
      } catch (error) {
        console.error('获取院校列表失败:', error)
        ElMessage.error('获取数据失败')
        tableData.value = []
        total.value = 0
      } finally {
        loading.value = false
      }
    }

    // 搜索
    const handleSearch = () => {
      currentPage.value = 1
      fetchData()
    }

    // 重置搜索
    const resetSearch = () => {
      searchForm.value.school_name = ''
      handleSearch()
    }

    // 新增
    const handleAdd = () => {
      resetForm()
      dialogVisible.value = true
    }

    // 查看详情
    const handleView = (row) => {
      viewData.value = { ...row }
      viewDialogVisible.value = true
    }

    // 编辑
    const handleEdit = (row) => {
      form.value = { ...row }
      dialogVisible.value = true
    }

    // 删除
    const handleDelete = (row) => {
      ElMessageBox.confirm('确认删除该院校吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        try {
          const response = await axios.post('http://************:9077/school/delete', {
            school_id: row.school_id
          })
          
          if (response.data && response.data.code === 200) {
            ElMessage.success('删除成功')
            fetchData()
          } else {
            ElMessage.error(response.data?.message || '删除失败')
          }
        } catch (error) {
          console.error('删除失败:', error)
          ElMessage.error('删除失败')
        }
      })
    }

    // 提交表单
    const handleSubmit = async () => {
      if (!formRef.value) return
      
      await formRef.value.validate(async (valid) => {
        if (valid) {
          try {
            const url = form.value.school_id 
              ? 'http://************:9077/school/update'
              : 'http://************:9077/school/register'
            
            const response = await axios.post(url, form.value)
            
            if (response.data && response.data.code === 200) {
              ElMessage.success(form.value.school_id ? '更新成功' : '新增成功')
              dialogVisible.value = false
              fetchData()
            } else {
              ElMessage.error(response.data?.message || '操作失败')
            }
          } catch (error) {
            console.error('提交失败:', error)
            ElMessage.error('操作失败')
          }
        }
      })
    }

    // 重置表单
    const resetForm = () => {
      form.value = {
        school_id: '',
        school_account: '',
        school_password: '',
        school_name: '',
        school_image: '',
        school_phone: '',
        school_address: '',
        school_characteristic: '',
        school_idea: '',
        identity: '',
        school_email: '',
        Score_line: null,
        school_number: null,
        school_video: ''
      }
      if (formRef.value) {
        formRef.value.clearValidate()
      }
    }

    // 分页
    const handleSizeChange = (val) => {
      pageSize.value = val
      fetchData()
    }

    const handleCurrentChange = (val) => {
      currentPage.value = val
      fetchData()
    }

    onMounted(() => {
      fetchData()
    })

    return {
      loading,
      dialogVisible,
      viewDialogVisible,
      currentPage,
      pageSize,
      total,
      searchForm,
      tableData,
      viewData,
      form,
      rules,
      formRef,
      dialogTitle,
      fetchData,
      handleSearch,
      resetSearch,
      handleAdd,
      handleView,
      handleEdit,
      handleDelete,
      handleSubmit,
      resetForm,
      handleSizeChange,
      handleCurrentChange
    }
  }
}
</script>

<style scoped>
.school-list-container {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.search-form {
  margin-bottom: 20px;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}
</style>








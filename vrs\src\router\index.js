import { createRouter, createWebHistory } from 'vue-router'
import Layout from '@/views/Layout/Layout.vue'

const routes = [
  {
    path: '/login',
    name: 'Login',
    component: () => import('@/views/my/login.vue')
  },
  
  {
    path: '/',
    component: Layout,
    redirect: '/dashboard',
    children: [
      {
        path: 'dashboard',
        name: 'Dashboard',
        component: () => import('@/views/dashboard/index.vue')
      },
      {
        path: 'foundation/banner',
        name: 'Banner',
        component: () => import('@/views/foundation/banner.vue')
      },
      {
        path: 'foundation/suggestion',
        name: 'Suggestion',
        component: () => import('@/views/foundation/suggestion.vue')
      },
      {
        path: 'foundation/specialized',
        name: 'Specialized',
        component: () => import('@/views/foundation/specialized.vue')
      },
      {
        path: 'foundation/province',
        name: 'Province',
        component: () => import('@/views/foundation/province.vue')
      },
      {
        path: 'foundation/notice',
        name: 'Notice',
        component: () => import('@/views/foundation/notice.vue')
      },
      {
        path: 'user/list',
        name: 'UserList',
        component: () => import('@/views/user/list.vue')
      },
      {
        path: 'user/role',
        name: 'UserRole',
        component: () => import('@/views/user/role.vue')
      },
      {
        path: 'profile',
        name: 'Profile',
        component: () => import('@/views/system/profile.vue')
      },
      {
        path: 'settings',
        name: 'Settings',
        component: () => import('@/views/system/settings.vue')
      },
      // 志愿管理路由
      {
        path: 'admission/list',
        name: 'AdmissionList',
        component: () => import('@/views/admission/list.vue')
      },
      {
        path: 'admission/review',
        name: 'AdmissionReview',
        component: () => import('@/views/admission/review.vue')
      },
      // 院校管理路由
      {
        path: 'school/list',
        name: 'SchoolList',
        component: () => import('@/views/school/list.vue')
      },
      {
        path: 'school/major',
        name: 'SchoolMajor',
        component: () => import('@/views/school/major.vue')
      },
      // 系统设置路由
      {
        path: 'system/config',
        name: 'SystemConfig',
        component: () => import('@/views/system/config.vue')
      },
      {
        path: 'system/log',
        name: 'SystemLog',
        component: () => import('@/views/system/log.vue')
      }
    ]
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

// 路由守卫
router.beforeEach((to, from, next) => {
  const token = localStorage.getItem('token')
  if (to.path === '/login' || to.path === '/reg') {
    next()
  } else {
    if (!token) {
      next('/login')
    } else {
      next()
    }
  }
})

export default router

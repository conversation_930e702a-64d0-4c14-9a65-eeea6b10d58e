@<template>
  <div class="admission-review-container">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <span>志愿审核</span>
          <div>
            <el-button type="primary" @click="fetchData">刷新数据</el-button>
          </div>
        </div>
      </template>

      <!-- 搜索栏 -->
      <el-form :inline="true" :model="searchForm" class="search-form">
        <el-form-item label="考生姓名">
          <el-input v-model="searchForm.studentName" placeholder="请输入考生姓名" />
        </el-form-item>
        <el-form-item label="院校名称">
          <el-input v-model="searchForm.schoolName" placeholder="请输入院校名称" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">搜索</el-button>
          <el-button @click="resetSearch">重置</el-button>
        </el-form-item>
      </el-form>

      <!-- 数据表格 -->
      <el-table :data="tableData" style="width: 100%" v-loading="loading">
        <el-table-column prop="id" label="ID" width="80" />
        <el-table-column prop="studentName" label="考生姓名" />
        <el-table-column prop="schoolName" label="院校名称" />
        <el-table-column prop="majorName" label="专业名称" />
        <el-table-column prop="score" label="考生分数" width="100" />
        <el-table-column prop="minScores" label="学校录取线" width="100" />
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="scope">
            <el-button-group>
              <el-button type="success" link @click="handleApprove(scope.row)">录取</el-button>
              <el-button type="danger" link @click="handleReject(scope.row)">拒绝</el-button>
              <el-button type="primary" link @click="handleDetail(scope.row)">详情</el-button>
            </el-button-group>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 30, 50]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 详情对话框 -->
    <el-dialog
      v-model="dialogVisible"
      title="志愿详情"
      width="600px"
    >
      <el-descriptions :column="2" border>
        <el-descriptions-item label="考生姓名">{{ currentDetail.studentName }}</el-descriptions-item>
        <el-descriptions-item label="考生ID">{{ currentDetail.studentId }}</el-descriptions-item>
        <el-descriptions-item label="院校名称">{{ currentDetail.schoolName }}</el-descriptions-item>
        <el-descriptions-item label="专业名称">{{ currentDetail.majorName }}</el-descriptions-item>
        <el-descriptions-item label="考生分数">{{ currentDetail.score }}</el-descriptions-item>
        <el-descriptions-item label="专业分数线">{{ currentDetail.minScores || '暂无' }}</el-descriptions-item>
        <el-descriptions-item label="志愿备注" :span="2">
          {{ currentDetail.remark }}
        </el-descriptions-item>
      </el-descriptions>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">关闭</el-button>
          <el-button type="success" @click="handleApprove(currentDetail)">通过</el-button>
          <el-button type="danger" @click="handleReject(currentDetail)">拒绝</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import axios from '@/utils/request'

// 搜索表单
const searchForm = ref({
  studentName: '',
  schoolName: ''
})

// 表格数据
const loading = ref(false)
const tableData = ref([])
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)

// 详情对话框
const dialogVisible = ref(false)
const currentDetail = ref({})

// 搜索
const handleSearch = () => {
  currentPage.value = 1
  fetchData()
}

// 重置搜索
const resetSearch = () => {
  searchForm.value = {
    studentName: '',
    schoolName: ''
  }
  fetchData()
}

// 获取数据
const fetchData = async () => {
  loading.value = true
  try {
    // 构建查询参数
    const params = {}
    
    // 添加搜索条件
    if (searchForm.value.studentName && searchForm.value.studentName.trim()) {
      params.nick_name = searchForm.value.studentName.trim()
    }
    if (searchForm.value.schoolName && searchForm.value.schoolName.trim()) {
      params.school_name = searchForm.value.schoolName.trim()
    }
    
    console.log('搜索参数:', params)
    
    const response = await axios.post('/admission/', params);
    
    if (response.code === 200) {
      let admissionData = []
      
      if (response.result && Array.isArray(response.result)) {
        admissionData = response.result
      } else if (response.data && Array.isArray(response.data)) {
        admissionData = response.data
      }
      
      if (admissionData.length > 0) {
        const processedData = admissionData.map(item => ({
          id: item.admission_id,
          studentName: item.nick_name,
          studentId: item.user_id,
          schoolName: item.school_name,
          majorName: item.specialized_content,
          score: item.user_fraction,
          minScores: item.min_scores,
          remark: '暂无备注',
          rawData: item
        }));

        // 只显示未录取的志愿（待审核的）
        const pendingData = processedData.filter(item => 
          item.rawData.admission_state === '未录取'
        );

        // 如果后端没有过滤，在前端进行过滤
        if (params.nick_name || params.school_name) {
          const filteredData = pendingData.filter(item => {
            let matchNickName = true
            let matchSchoolName = true
            
            if (params.nick_name) {
              matchNickName = item.studentName && item.studentName.includes(params.nick_name)
            }
            
            if (params.school_name) {
              matchSchoolName = item.schoolName && item.schoolName.includes(params.school_name)
            }
            
            return matchNickName && matchSchoolName
          })
          
          tableData.value = filteredData
          total.value = filteredData.length
        } else {
          tableData.value = pendingData
          total.value = pendingData.length
        }
        
        if (tableData.value.length === 0) {
          ElMessage.info('未找到匹配的待审核记录')
        } 
        // else {
        //   ElMessage.success(`找到 ${tableData.value.length} 条待审核记录`)
        // }
      } else {
        tableData.value = []
        total.value = 0
        ElMessage.info('未找到匹配的志愿记录')
      }
    } else {
      ElMessage.error(response.message || '获取数据失败')
      tableData.value = []
      total.value = 0
    }
  } catch (error) {
    console.error('获取志愿审核列表失败:', error)
    ElMessage.error('获取数据失败')
    tableData.value = []
    total.value = 0
  } finally {
    loading.value = false
  }
}

// 初始化加载数据
fetchData()

// 通过审核
const handleApprove = (row) => {
  ElMessageBox.confirm('确认录取该考生吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'success'
  }).then(async () => {
    try {
      const response = await axios.post('/admission/update', {
        admission_id: row.id || row.rawData.admission_id,
        admission_state: '已录取',
        // 保留其他必要字段
        user_id: row.studentId || row.rawData.user_id,
        school_id: row.rawData.school_id,
        specialized_id: row.rawData.specialized_id,
        nick_name: row.studentName || row.rawData.nick_name,
        user_fraction: row.score || row.rawData.user_fraction
      });
      
      if (response.code === 200) {
        ElMessage.success('已录取');
        dialogVisible.value = false;
        fetchData();
      } else {
        ElMessage.error(response.message || '操作失败');
      }
    } catch (error) {
      console.error('录取操作失败:', error);
      ElMessage.error('操作失败');
    }
  });
}

// 拒绝审核
const handleReject = (row) => {
  ElMessageBox.confirm('确认拒绝该志愿申请吗？拒绝后将删除该记录', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      const response = await axios.post('/admission/delete', {
        admission_id: row.id || row.rawData.admission_id
      });
      
      if (response.code === 200) {
        ElMessage.success('已拒绝并删除');
        dialogVisible.value = false;
        fetchData();
      } else {
        ElMessage.error(response.message || '操作失败');
      }
    } catch (error) {
      console.error('拒绝并删除失败:', error);
      ElMessage.error('操作失败');
    }
  }).catch(() => {
    // 取消操作
  });
}

// 查看详情
const handleDetail = (row) => {
  currentDetail.value = row
  dialogVisible.value = true
}

// 分页处理
const handleSizeChange = (val) => {
  pageSize.value = val
  currentPage.value = 1
  fetchData()
}

const handleCurrentChange = (val) => {
  currentPage.value = val
  fetchData()
}
</script>

<style scoped>
.admission-review-container {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.search-form {
  margin-bottom: 20px;
  padding: 20px;
  background-color: var(--el-fill-color-light);
  border-radius: 4px;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  margin-top: 20px;
}
</style> 

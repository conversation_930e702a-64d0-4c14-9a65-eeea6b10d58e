<!-- 首页 -->
<template>
	<view class="home-container">
		<!-- 顶部状态栏占位 -->
		<view class="status-bar"></view>
		
		<!-- 顶部搜索区域 -->
		<view class="header-section">
			<view class="search-container">
				<view class="search-box" @tap="goSearch">
					<uni-icons type="search" size="18" color="#999"></uni-icons>
					<text class="search-placeholder">搜索院校、专业、志愿信息</text>
				</view>
			</view>
		</view>
		
		<!-- 轮播图 -->
		<view class="banner-section">
			<swiper 
				class="banner-swiper" 
				circular 
				indicator-dots 
				autoplay 
				:interval="BANNER_CONFIG.interval" 
				:duration="BANNER_CONFIG.duration"
				indicator-color="rgba(255,255,255,0.5)"
				indicator-active-color="#d4237a"
			>
				<swiper-item v-for="(item, index) in banners" :key="index">
					<image :src="item.image" mode="aspectFill" class="banner-image"></image>
				</swiper-item>
			</swiper>
		</view>
		
		<!-- 快速入口 -->
		<view class="quick-entry-section">
			<view class="section-title-bar">
				<text class="section-title">快速入口</text>
			</view>
			<view class="quick-entry-grid">
				<view 
					class="quick-entry-item" 
					v-for="(item, index) in quickEntries" 
					:key="index"
					@click="navTo(item.path, item.name)"
				>
					<uni-icons :type="item.icon" size="24" :color="item.color"></uni-icons>
					<text class="quick-entry-text">{{item.name}}</text>
				</view>
			</view>
		</view>
		
		<!-- 热门专业推荐 -->
		<view class="section">
			<view class="section-header">
				<text class="section-title">热门专业</text>
				<text class="more-btn" @click="viewMore('majors')">查看更多 ></text>
			</view>
			<scroll-view scroll-x class="hot-major-scroll">
				<view 
					class="hot-major-card" 
					v-for="(item, index) in hotMajors" 
					:key="index"
					@click="viewMajorDetail(item)"
				>
					<image 
						:src="item.image || '/static/majors/default.jpg'" 
						mode="aspectFill" 
						class="major-image"
						@error="handleImageError(index)"
					></image>
					<view class="major-info">
						<text class="major-name">{{item.name}}</text>
						<text class="major-desc">{{item.description}}</text>
						<view class="major-tags">
							<text class="tag">{{item.category}}</text>
							<text class="tag hot-tag">热门</text>
						</view>
					</view>
				</view>
			</scroll-view>
		</view>
		
		<!-- 推荐院校 -->
		<Colleges ref="colleges" @view-more="viewMore" @view-detail="viewSchoolDetail" />
		
		<!-- 志愿填报指南 -->
		<view class="guide-section">
			<view class="section-header">
				<text class="section-title">公告消息</text>
			</view>
			<view class="guide-list">
				<view 
					class="guide-item" 
					v-for="(item, index) in notices" 
					:key="index"
					@click="viewNoticeDetail(item)"
				>
					<view class="guide-icon">
						<uni-icons :type="item.icon" size="20" color="#d4237a"></uni-icons>
					</view>
					<view class="guide-content">
						<text class="guide-title">{{item.title}}</text>
						<br />
						<text class="guide-desc">{{item.description}}</text>
					</view>
					<uni-icons type="right" size="16" color="#ccc"></uni-icons>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import request from '../../utils/request.js';
	import Colleges from './moudle/Colleges.vue';

	// 常量配置
	const BANNER_CONFIG = {
		interval: 3000,
		duration: 500
	};

	const COUNTDOWN_CONFIG = {
		targetDate: '2024-06-25', // 志愿填报开始日期
		defaultDays: 30
	};

	export default {
		components: {
			Colleges
		},
		data() {
			return {
				BANNER_CONFIG,
				remainingDays: COUNTDOWN_CONFIG.defaultDays,
				banners: [],
				
				// 快速入口
				quickEntries: [
					{ name: '志愿填报', icon: 'compose', color: '#d4237a', path: '/pages/Volunteer/function/insert' },
					{ name: '录取概率', icon: 'refreshempty', color: '#007aff', path: '/pages/Me/function/probability' },
					{ name: '专业测评', icon: 'help', color: '#34c759', path: '/pages/Me/function/Evaluation' },
					{ name: '志愿咨询', icon: 'chat', color: '#ff9500', path: '/pages/Volunteer/intelligent/intelligent' }
				],
				
				hotMajors: [],
				
				// 公告消息 - 改为动态数据
				notices: []
			}
		},
		
		onLoad() {
			this.initData();
			
			// 监听刷新事件
			uni.$on('home-refresh', this.refreshData);
		},
		
		onShow() {
			// 检查是否需要刷新数据
			const needRefresh = uni.getStorageSync('home_need_refresh');
			if (needRefresh === 'true') {
				this.refreshData();
				// 清除刷新标记
				uni.removeStorageSync('home_need_refresh');
			}
		},
		
		onUnload() {
			// 移除事件监听，防止内存泄漏
			uni.$off('home-refresh', this.refreshData);
		},
		
		methods: {
			viewNoticeDetail(){
				uni.navigateTo({
					url:'/pages/Volunteer/intelligent/intelligent'
				})
			},
			// 初始化数据
			initData() {
				this.calculateRemainingDays();
				this.getBanners();
				this.getHotMajors();
				this.getNotices(); // 添加获取公告数据
			},
			
			// 刷新所有数据
			refreshData() {
				// console.log('Home页面刷新数据');
				this.initData();
				
				// 通知Colleges组件刷新数据
				this.$nextTick(() => {
					const collegesComponent = this.$refs.colleges;
					if (collegesComponent) {
						collegesComponent.getSchools();
					}
				});
			},
			
			// 计算剩余天数
			calculateRemainingDays() {
				const targetDate = new Date(COUNTDOWN_CONFIG.targetDate);
				const currentDate = new Date();
				const timeDiff = targetDate.getTime() - currentDate.getTime();
				const daysDiff = Math.ceil(timeDiff / (1000 * 3600 * 24));
				
				this.remainingDays = daysDiff > 0 ? daysDiff : 0;
			},
			
			// 获取热门专业数据
			getHotMajors() {
				request.post('/specialized/getAllWithDetail', {}).then(res => {
					if (res.code === 200 && res.result && res.result.length > 0) {
						// 只取前4条数据
						this.hotMajors = res.result.slice(0, 4).map(item => {
							// 处理图片URL
							let imageUrl = this.processImageUrl(item.specialized_image);
							
							return {
								id: item.specialized_id,
								name: item.specialized_name,
								description: item.specialized_content,
								image: imageUrl,
								category: item.specialized_part,
								type: item.specialized_type
							};
						});
					} else {
						this.setDefaultHotMajors();
					}
				}).catch(err => {
					console.error('获取热门专业失败', err);
					this.setDefaultHotMajors();
				});
			},
			
			// 处理图片URL
			processImageUrl(imageUrl) {
				if (!imageUrl) return '/static/majors/default.jpg';
				
				// 如果是完整的URL，提取路径部分重新拼接
				if (imageUrl.startsWith('http')) {
					try {
						const url = new URL(imageUrl);
						return request.baseUrl + url.pathname;
					} catch (e) {
						// 如果URL解析失败，使用字符串替换
						const pathMatch = imageUrl.match(/https?:\/\/[^\/]+(.+)/);
						if (pathMatch) {
							return request.baseUrl + pathMatch[1];
						}
					}
				}
				
				if (imageUrl.startsWith('/')) {
					return request.baseUrl + imageUrl;
				} else {
					return request.baseUrl + '/' + imageUrl;
				}
			},
			
			// 设置默认热门专业数据
			setDefaultHotMajors() {
				this.hotMajors = [];
			},
			
			// 获取轮播图数据
			getBanners() {
				request.post('/banner/', {}).then(res => {
					if (res.code === 200 && res.result && res.result.length > 0) {
						this.banners = res.result.map(item => ({
							id: item.banner_id,
							name: item.banner_name,
							image: this.processImageUrl(item.banner_image)
						}));
					}
				}).catch(err => {
					console.error('获取轮播图失败', err);
					this.banners = [
						{ id: 1, name: '2024高考志愿填报指南', image: '/static/banners/banner1.jpg' }
					];
				});
			},
			
			// 跳转到搜索页面
			goSearch() {
				uni.navigateTo({
					url: '/pages/Home/function/search'
				});
			},

			// 导航跳转
			navTo(path, name) {
				if (path) {
					uni.navigateTo({
						url: path
					});
				} else {
					uni.showToast({
						title: `${name}功能开发中`,
						icon: 'none'
					});
				}
			},
			
			// 查看更多
			viewMore(type) {
				const routes = {
					majors: '/pages/Home/function/specialized',
					schools: '/pages/schools/schools',
					guides: '/pages/guides/guides'
				};
				
				if (routes[type]) {
					uni.navigateTo({
						url: routes[type]
					});
				}
			},
			
			// 查看专业详情
			viewMajorDetail(major) {
				uni.navigateTo({
					url: `/pages/Home/function/specialized_detail?id=${major.id || 0}&name=${major.name}`
				});
			},
			
			// 查看院校详情
			viewSchoolDetail(school) {
				const schoolId = school.school_id || school.id || 0;
				const schoolName = school.school_name || school.name || '未知学校';
				
				uni.navigateTo({
					url: `/pages/Home/function/school_detail?id=${schoolId}&name=${encodeURIComponent(schoolName)}`
				});
			},
			
			// 查看指南详情
			viewGuideDetail(guide) {
				uni.navigateTo({
					url: '/pages/Volunteer/intelligent/intelligent'
				});
			},

			// 图片加载失败处理
			handleImageError(index) {
				console.warn(`图片加载失败，替换为默认图片，索引: ${index}`);
				this.$set(this.hotMajors, index, {
					...this.hotMajors[index],
					image: '/static/majors/default.jpg'
				});
			},
			// 获取公告数据
			getNotices() {
				request.post('/notice/', {}).then(res => {
					if (res.code === 200 && res.result && res.result.length > 0) {
						this.notices = res.result.map(item => ({
							id: item.notice_id,
							title: item.notice_title,
							description: item.notice_content,
							icon: 'sound' // 默认图标
						}));
					}
				}).catch(err => {
					console.error('获取公告失败', err);
					// 使用默认数据
					this.notices = [
						{
							title: '系统公告',
							description: '暂无公告信息',
							icon: 'sound'
						}
					];
				});
			}
		}
	}
</script>

<style scoped>
.home-container {
	background: #f8f9fa;
	min-height: 100vh;
	padding-top: var(--status-bar-height);
}

.status-bar {
	height: var(--status-bar-height);
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	z-index: 1000;
	background-color: #f8f9fa;
}

.header-section {
	position: fixed;
	top: var(--status-bar-height);
	left: 0;
	right: 0;
	z-index: 100;
	background-color: #f8f9fa;
	padding: 16px;
}

.search-container {
	width: 100%;
}

.search-box {
	display: flex;
	align-items: center;
	height: 40px;
	background: #fff;
	border-radius: 20px;
	padding: 0 16px;
	border: 1px solid #e0e0e0;
}

.search-placeholder {
	flex: 1;
	font-size: 14px;
	color: #999;
	margin-left: 8px;
}

.banner-section {
	margin: 16px;
	margin-top: 80px; /* 为固定的搜索栏留出空间 */
}

.banner-swiper {
	height: 180px;
	border-radius: 12px;
	overflow: hidden;
}

.banner-image {
	width: 100%;
	height: 100%;
}

.banner-overlay {
	position: absolute;
	bottom: 0;
	left: 0;
	right: 0;
	background: linear-gradient(transparent, rgba(0,0,0,0.6));
	padding: 20px 16px 16px;
}

.banner-title {
	color: #fff;
	font-size: 16px;
	font-weight: bold;
}

.quick-entry-section {
	margin: 0 16px 16px;
	background: #fff;
	border-radius: 12px;
	padding: 16px;
}

.section-title-bar {
	margin-bottom: 16px;
}

.section-title {
	font-size: 18px;
	font-weight: bold;
	color: #333;
}

.quick-entry-grid {
	display: flex;
	justify-content: space-around;
}

.quick-entry-item {
	display: flex;
	flex-direction: column;
	align-items: center;
	padding: 12px;
}

.quick-entry-text {
	font-size: 12px;
	color: #333;
	margin-top: 8px;
}

.section {
	background: #fff;
	margin: 0 16px 16px;
	border-radius: 12px;
	padding: 16px;
}

.section-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 16px;
}

.more-btn {
	font-size: 14px;
	color: #d4237a;
}

.horizontal-scroll {
	white-space: nowrap;
}

.hot-major-scroll {
	width: 100%;
	white-space: nowrap;
}

.hot-major-card {
	display: inline-block;
	width: 200px;
	margin-right: 12px;
	border-radius: 8px;
	overflow: hidden;
	border: 1px solid #f0f0f0;
	vertical-align: top;
}

.major-image {
	width: 100%;
	height: 120px;
}

.major-info {
	padding: 12px;
}

.major-name {
	font-size: 14px;
	font-weight: bold;
	color: #333;
	margin-bottom: 4px;
}

.major-desc {
	font-size: 12px;
	color: #666;
	margin-bottom: 8px;
}

.major-tags {
	display: flex;
	gap: 4px;
}

.tag {
	font-size: 10px;
	padding: 2px 6px;
	border-radius: 10px;
	background: #f0f0f0;
	color: #666;
}

.hot-tag {
	background: #ffe6ee;
	color: #d4237a;
}

.school-list {
	display: flex;
	flex-direction: column;
	gap: 12px;
}

.school-card {
	display: flex;
	align-items: center;
	padding: 12px;
	border: 1px solid #f0f0f0;
	border-radius: 8px;
}

.school-logo {
	width: 60px;
	height: 60px;
	border-radius: 8px;
	margin-right: 12px;
}

.school-info {
	flex: 1;
}

.school-name {
	font-size: 16px;
	font-weight: bold;
	color: #333;
	margin-bottom: 4px;
}

.school-tags {
	display: flex;
	gap: 4px;
	margin-bottom: 4px;
}

.school-tag {
	font-size: 10px;
	padding: 2px 6px;
	border-radius: 10px;
	background: #e3f2fd;
	color: #1976d2;
}

.school-desc {
	font-size: 12px;
	color: #666;
	margin-bottom: 4px;
}

.school-stats {
	display: flex;
	gap: 12px;
}

.stat-item {
	font-size: 11px;
	color: #999;
}

.school-score {
	display: flex;
	flex-direction: column;
	align-items: center;
}

.score-label {
	font-size: 10px;
	color: #999;
	margin-bottom: 2px;
}

.score-value {
	font-size: 14px;
	font-weight: bold;
	color: #d4237a;
}

.guide-section {
	margin: 0 16px 16px;
	background: #fff;
	border-radius: 12px;
	padding: 16px;
}

.guide-list {
	display: flex;
	flex-direction: column;
	gap: 12px;
}

.guide-item {
	display: flex;
	align-items: center;
	padding: 12px;
	border: 1px solid #f0f0f0;
	border-radius: 8px;
}

.guide-icon {
	width: 40px;
	height: 40px;
	border-radius: 20px;
	background: #fce4ec;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-right: 12px;
}

.guide-content {
	flex: 1;
}

.guide-title {
	font-size: 14px;
	font-weight: bold;
	color: #333;
	margin-bottom: 4px;
}

.guide-desc {
	font-size: 12px;
	color: #666;
}
</style>














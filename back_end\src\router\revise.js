// 修改密码 接口
const express = require('express');
const router = express.Router();
const db = require('../config/db');

// 修改密码接口
router.post('/changePassword', (req, res) => {
    const { user_id, old_password, new_password } = req.body;
    
    // 验证参数
    if (!user_id || !old_password || !new_password) {
        return res.json({
            code: 201,
            success: "失败",
            message: "用户ID、旧密码和新密码不能为空"
        });
    }
    
    // 验证旧密码是否正确
    const checkPasswordSql = 'SELECT * FROM user WHERE user_id = ? AND password = ?';
    db.query(checkPasswordSql, [user_id, old_password], (err, result) => {
        if (err) {
            return res.json({
                code: 201,
                success: "失败",
                message: "服务器错误"
            });
        }
        
        if (result.length === 0) {
            return res.json({
                code: 201,
                success: "失败",
                message: "旧密码错误"
            });
        }
        
        // 更新密码
        const updatePasswordSql = 'UPDATE user SET password = ? WHERE user_id = ?';
        db.query(updatePasswordSql, [new_password, user_id], (updateErr, updateResult) => {
            if (updateErr) {
                return res.json({
                    code: 201,
                    success: "失败",
                    message: "密码修改失败"
                });
            }
            
            res.json({
                code: 200,
                success: "成功",
                message: "密码修改成功"
            });
        });
    });
});

module.exports = router;
